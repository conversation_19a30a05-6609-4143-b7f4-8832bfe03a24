// Simplified generic taint analysis: source -> sink with automatic intermediate node discovery
// Required params: $sourceNames, $sinkNames
// Optional param:  $sinkArgIndices (defaults to [1])
// No manual anchor specification needed - automatically captures all intermediate nodes

WITH apoc.convert.toList($sourceNames) AS sourceNames,
     apoc.convert.toList($sinkNames) AS sinkNames,
     apoc.convert.toList(coalesce($sinkArgIndices, [1])) AS sinkArgIndices

// Resolve sink calls and their arguments
MATCH (snk:CALL)
WHERE snk.NAME IN sinkNames OR snk.METHOD_FULL_NAME IN sinkNames
WITH collect(snk) AS snkCalls, sourceNames, sinkNames, sinkArgIndices

MATCH (snk2:CALL)
WHERE snk2.NAME IN sinkNames OR snk2.METHOD_FULL_NAME IN sinkNames
MATCH (snk2)-[:ARGUMENT]->(sinkArg)
WHERE sinkArg.ARGUMENT_INDEX IN sinkArgIndices AND NOT 'LITERAL' IN labels(sinkArg)
WITH snkCalls, collect(sinkArg) AS sinkArgs, sourceNames

// Sources: IDENTIFIER/LOCAL with name in sourceNames OR indexAccess/indirectIndexAccess whose first arg IDENTIFIER has name in sourceNames
MATCH (base)
WHERE (base:IDENTIFIER AND base.NAME IN sourceNames)
   OR (base:LOCAL AND base.NAME IN sourceNames)
   OR (base:CALL AND base.METHOD_FULL_NAME IN ['<operator>.indexAccess','<operator>.indirectIndexAccess']
       AND exists( (base)-[:ARGUMENT]->(:IDENTIFIER) )
       AND any(x IN [(base)-[:ARGUMENT]->(id:IDENTIFIER) | id.NAME] WHERE x IN sourceNames) )
WITH snkCalls, sinkArgs, collect(base) AS sources

UNWIND sources AS s

// Direct source -> sink path with comprehensive intermediate node capture
CALL apoc.path.expandConfig(s, {
  endNodes: sinkArgs,
  minLevel: 1,
  maxLevel: 300,
  bfs: true,
  uniqueness: 'NODE_GLOBAL',
  relationshipFilter: 'REACHING_DEF>|REACHING_DEF<|ARGUMENT>|ARGUMENT<|PARAMETER_LINK>|PARAMETER_LINK<|CALL>|CALL<|CROSS_FILE_CALL>|CROSS_FILE_CALL<|RECEIVER>|RECEIVER<',
  labelFilter: '+IDENTIFIER|+LOCAL|+CALL|+METHOD|+METHOD_PARAMETER_IN|+METHOD_PARAMETER_OUT|+METHOD_RETURN|+LITERAL'
}) YIELD path
WITH s, path, snkCalls
WHERE any(r IN relationships(path) WHERE type(r)='REACHING_DEF')

// Merge path nodes and append sink CALL node, with deduplication
WITH nodes(path) + [head(snkCalls)] AS allNodes
WITH apoc.coll.toSet(allNodes) AS uniqueNodes
RETURN [n IN uniqueNodes | { id: id(n), labels: labels(n), properties: properties(n) }] AS nodes
ORDER BY size(nodes) ASC
LIMIT 10;
