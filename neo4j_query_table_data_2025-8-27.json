[
  {
    "nodes": [
      {
        "id": 180,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771112,
          "NAME": "<operator>.indexAccess",
          "ARGUMENT_INDEX": 1,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 1,
          "LINE_NUMBER": 59,
          "METHOD_FULL_NAME": "<operator>.indexAccess",
          "CODE": "$_GET["filename"]"
        }
      },
      {
        "id": 1559,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476762,
          "NAME": "_GET",
          "ARGUMENT_INDEX": 1,
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 1,
          "LINE_NUMBER": 59,
          "CODE": "$_GET"
        }
      },
      {
        "id": 2007,
        "labels": [
          "LOCAL"
        ],
        "properties": {
          "id": 94489280522,
          "NAME": "_GET",
          "GENERIC_SIGNATURE": "<empty>",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 4,
          "LINE_NUMBER": 59,
          "CODE": "$_GET"
        }
      },
      {
        "id": 1562,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476765,
          "NAME": "_GET",
          "ARGUMENT_INDEX": 1,
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 1,
          "LINE_NUMBER": 62,
          "CODE": "$_GET"
        }
      },
      {
        "id": 185,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771117,
          "NAME": "<operator>.indexAccess",
          "ARGUMENT_INDEX": 1,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 1,
          "LINE_NUMBER": 62,
          "METHOD_FULL_NAME": "<operator>.indexAccess",
          "CODE": "$_GET["subdir"]"
        }
      },
      {
        "id": 184,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771116,
          "NAME": "<operator>.coalesce",
          "ARGUMENT_INDEX": 2,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 2,
          "LINE_NUMBER": 62,
          "METHOD_FULL_NAME": "<operator>.coalesce",
          "CODE": "$_GET["subdir"] ?? """
        }
      },
      {
        "id": 182,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771114,
          "NAME": "<operator>.coalesce",
          "ARGUMENT_INDEX": 2,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 2,
          "LINE_NUMBER": 62,
          "METHOD_FULL_NAME": "<operator>.coalesce",
          "CODE": "$_POST["subdir"] ?? $_GET["subdir"] ?? """
        }
      },
      {
        "id": 183,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771115,
          "NAME": "<operator>.indexAccess",
          "ARGUMENT_INDEX": 1,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 1,
          "LINE_NUMBER": 62,
          "METHOD_FULL_NAME": "<operator>.indexAccess",
          "CODE": "$_POST["subdir"]"
        }
      },
      {
        "id": 1561,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476764,
          "NAME": "_POST",
          "ARGUMENT_INDEX": 1,
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 1,
          "LINE_NUMBER": 62,
          "CODE": "$_POST"
        }
      },
      {
        "id": 2008,
        "labels": [
          "LOCAL"
        ],
        "properties": {
          "id": 94489280523,
          "NAME": "_POST",
          "GENERIC_SIGNATURE": "<empty>",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 5,
          "LINE_NUMBER": 59,
          "CODE": "$_POST"
        }
      },
      {
        "id": 1558,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476761,
          "NAME": "_POST",
          "ARGUMENT_INDEX": 1,
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 1,
          "LINE_NUMBER": 59,
          "CODE": "$_POST"
        }
      },
      {
        "id": 178,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771110,
          "NAME": "<operator>.indexAccess",
          "ARGUMENT_INDEX": 1,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 1,
          "LINE_NUMBER": 59,
          "METHOD_FULL_NAME": "<operator>.indexAccess",
          "CODE": "$_POST["filename"]"
        }
      },
      {
        "id": 177,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771109,
          "NAME": "<operator>.coalesce",
          "ARGUMENT_INDEX": 2,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 2,
          "LINE_NUMBER": 59,
          "METHOD_FULL_NAME": "<operator>.coalesce",
          "CODE": "$_POST["filename"] ?? $_GET["filename"] ?? """
        }
      },
      {
        "id": 179,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771111,
          "NAME": "<operator>.coalesce",
          "ARGUMENT_INDEX": 2,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 2,
          "LINE_NUMBER": 59,
          "METHOD_FULL_NAME": "<operator>.coalesce",
          "CODE": "$_GET["filename"] ?? """
        }
      },
      {
        "id": 2419,
        "labels": [
          "METHOD"
        ],
        "properties": {
          "id": 111669149720,
          "FULL_NAME": "<operator>.coalesce",
          "NAME": "<operator>.coalesce",
          "GENERIC_SIGNATURE": "<empty>",
          "ORDER": 0,
          "FILENAME": "<empty>",
          "AST_PARENT_FULL_NAME": "<global>",
          "IS_EXTERNAL": true,
          "AST_PARENT_TYPE": "NAMESPACE_BLOCK",
          "CODE": "<empty>"
        }
      },
      {
        "id": 153,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771085,
          "NAME": "<operator>.coalesce",
          "ARGUMENT_INDEX": 2,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 2,
          "LINE_NUMBER": 38,
          "METHOD_FULL_NAME": "<operator>.coalesce",
          "CODE": "$_POST["action"] ?? $_GET["action"] ?? "upload""
        }
      },
      {
        "id": 154,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771086,
          "NAME": "<operator>.indexAccess",
          "ARGUMENT_INDEX": 1,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 1,
          "LINE_NUMBER": 38,
          "METHOD_FULL_NAME": "<operator>.indexAccess",
          "CODE": "$_POST["action"]"
        }
      },
      {
        "id": 1542,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476745,
          "NAME": "_POST",
          "ARGUMENT_INDEX": 1,
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 1,
          "LINE_NUMBER": 38,
          "CODE": "$_POST"
        }
      },
      {
        "id": 2397,
        "labels": [
          "METHOD"
        ],
        "properties": {
          "id": 111669149698,
          "NAME": "handleRequest",
          "FULL_NAME": "SecurityDemo\\FileUpload\\FileUploadController->handleRequest",
          "SIGNATURE": "<unresolvedSignature>(0)",
          "GENERIC_SIGNATURE": "<empty>",
          "ORDER": 4,
          "FILENAME": "FileUploadHandler.php",
          "LINE_NUMBER": 35,
          "IS_EXTERNAL": false,
          "AST_PARENT_FULL_NAME": "SecurityDemo\\FileUpload\\FileUploadController",
          "AST_PARENT_TYPE": "TYPE_DECL",
          "CODE": "PUBLIC function handleRequest(this)"
        }
      },
      {
        "id": 1546,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476749,
          "NAME": "action",
          "ARGUMENT_INDEX": 1,
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 1,
          "LINE_NUMBER": 42,
          "CODE": "$action"
        }
      },
      {
        "id": 2003,
        "labels": [
          "LOCAL"
        ],
        "properties": {
          "id": 94489280518,
          "NAME": "action",
          "GENERIC_SIGNATURE": "<empty>",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 4,
          "LINE_NUMBER": 38,
          "CODE": "$action"
        }
      },
      {
        "id": 1544,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476747,
          "NAME": "action",
          "ARGUMENT_INDEX": 1,
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 1,
          "LINE_NUMBER": 40,
          "CODE": "$action"
        }
      },
      {
        "id": 157,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771089,
          "NAME": "<operator>.identical",
          "ARGUMENT_INDEX": -1,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 1,
          "LINE_NUMBER": 40,
          "METHOD_FULL_NAME": "<operator>.identical",
          "CODE": "$action === "upload""
        }
      },
      {
        "id": 2421,
        "labels": [
          "METHOD"
        ],
        "properties": {
          "id": 111669149722,
          "FULL_NAME": "<operator>.identical",
          "NAME": "<operator>.identical",
          "GENERIC_SIGNATURE": "<empty>",
          "ORDER": 0,
          "FILENAME": "<empty>",
          "AST_PARENT_FULL_NAME": "<global>",
          "IS_EXTERNAL": true,
          "AST_PARENT_TYPE": "NAMESPACE_BLOCK",
          "CODE": "<empty>"
        }
      },
      {
        "id": 259,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771191,
          "NAME": "<operator>.identical",
          "ARGUMENT_INDEX": 1,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 1,
          "LINE_NUMBER": 115,
          "METHOD_FULL_NAME": "<operator>.identical",
          "CODE": "$_SERVER["REQUEST_METHOD"] === "POST""
        }
      },
      {
        "id": 258,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771190,
          "NAME": "<operator>.logicalOr",
          "ARGUMENT_INDEX": -1,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 1,
          "LINE_NUMBER": 115,
          "METHOD_FULL_NAME": "<operator>.logicalOr",
          "CODE": "$_SERVER["REQUEST_METHOD"] === "POST" || !empty($_GET["action"])"
        }
      },
      {
        "id": 261,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771193,
          "NAME": "<operator>.logicalNot",
          "ARGUMENT_INDEX": 2,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 2,
          "LINE_NUMBER": 115,
          "METHOD_FULL_NAME": "<operator>.logicalNot",
          "CODE": "!empty($_GET["action"])"
        }
      },
      {
        "id": 262,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771194,
          "NAME": "empty",
          "ARGUMENT_INDEX": 1,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "bool",
          "ORDER": 1,
          "LINE_NUMBER": 115,
          "METHOD_FULL_NAME": "empty",
          "DYNAMIC_TYPE_HINT_FULL_NAME": [
            "empty"
          ],
          "CODE": "empty($_GET["action"])"
        }
      },
      {
        "id": 263,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771195,
          "NAME": "<operator>.indexAccess",
          "ARGUMENT_INDEX": 1,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 1,
          "LINE_NUMBER": 115,
          "METHOD_FULL_NAME": "<operator>.indexAccess",
          "CODE": "$_GET["action"]"
        }
      },
      {
        "id": 1621,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476824,
          "NAME": "_GET",
          "ARGUMENT_INDEX": 1,
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 1,
          "LINE_NUMBER": 115,
          "CODE": "$_GET"
        }
      },
      {
        "id": 2395,
        "labels": [
          "METHOD"
        ],
        "properties": {
          "id": 111669149696,
          "NAME": "<global>",
          "FULL_NAME": "FileUploadHandler.php:<global>",
          "SIGNATURE": "<unresolvedSignature>(0)",
          "GENERIC_SIGNATURE": "<empty>",
          "ORDER": 1,
          "FILENAME": "FileUploadHandler.php",
          "LINE_NUMBER": 9,
          "IS_EXTERNAL": false,
          "AST_PARENT_FULL_NAME": "FileUploadHandler.php:<global>",
          "AST_PARENT_TYPE": "TYPE_DECL",
          "CODE": "VIRTUAL PUBLIC STATIC function <global>()"
        }
      },
      {
        "id": 1628,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476831,
          "NAME": "controller",
          "ARGUMENT_INDEX": 0,
          "TYPE_FULL_NAME": "SecurityDemo\\FileUpload\\FileUploadController",
          "ORDER": 1,
          "LINE_NUMBER": 118,
          "CODE": "$controller"
        }
      },
      {
        "id": 270,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771202,
          "NAME": "sendResponse",
          "ARGUMENT_INDEX": -1,
          "SIGNATURE": "<unresolvedSignature>(1)",
          "DISPATCH_TYPE": "DYNAMIC_DISPATCH",
          "TYPE_FULL_NAME": "SecurityDemo\\FileUpload\\FileUploadController->sendResponse-><returnValue>",
          "ORDER": 3,
          "LINE_NUMBER": 118,
          "METHOD_FULL_NAME": "SecurityDemo\\FileUpload\\FileUploadController->sendResponse",
          "CODE": "$controller->sendResponse($result)"
        }
      },
      {
        "id": 1629,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476832,
          "NAME": "result",
          "ARGUMENT_INDEX": 1,
          "TYPE_FULL_NAME": "array",
          "ORDER": 2,
          "LINE_NUMBER": 118,
          "CODE": "$result"
        }
      },
      {
        "id": 1626,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476829,
          "NAME": "result",
          "ARGUMENT_INDEX": 1,
          "TYPE_FULL_NAME": "array",
          "ORDER": 1,
          "LINE_NUMBER": 117,
          "CODE": "$result"
        }
      },
      {
        "id": 268,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771200,
          "NAME": "<operator>.assignment",
          "ARGUMENT_INDEX": -1,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 2,
          "LINE_NUMBER": 117,
          "METHOD_FULL_NAME": "<operator>.assignment",
          "CODE": "$result = $controller->handleRequest()"
        }
      },
      {
        "id": 269,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771201,
          "NAME": "handleRequest",
          "ARGUMENT_INDEX": 2,
          "SIGNATURE": "<unresolvedSignature>(0)",
          "DISPATCH_TYPE": "DYNAMIC_DISPATCH",
          "TYPE_FULL_NAME": "SecurityDemo\\FileUpload\\FileUploadController->handleRequest-><returnValue>",
          "ORDER": 2,
          "LINE_NUMBER": 117,
          "METHOD_FULL_NAME": "SecurityDemo\\FileUpload\\FileUploadController->handleRequest",
          "CODE": "$controller->handleRequest()"
        }
      },
      {
        "id": 1627,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476830,
          "NAME": "controller",
          "ARGUMENT_INDEX": 0,
          "TYPE_FULL_NAME": "SecurityDemo\\FileUpload\\FileUploadController",
          "ORDER": 1,
          "LINE_NUMBER": 117,
          "CODE": "$controller"
        }
      },
      {
        "id": 1622,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476825,
          "NAME": "controller",
          "ARGUMENT_INDEX": 1,
          "TYPE_FULL_NAME": "SecurityDemo\\FileUpload\\FileUploadController",
          "ORDER": 1,
          "LINE_NUMBER": 116,
          "CODE": "$controller"
        }
      },
      {
        "id": 264,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771196,
          "NAME": "<operator>.assignment",
          "ARGUMENT_INDEX": -1,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 1,
          "LINE_NUMBER": 116,
          "METHOD_FULL_NAME": "<operator>.assignment",
          "CODE": "$controller = "
        }
      },
      {
        "id": 2416,
        "labels": [
          "METHOD"
        ],
        "properties": {
          "id": 111669149717,
          "FULL_NAME": "<operator>.assignment",
          "NAME": "<operator>.assignment",
          "GENERIC_SIGNATURE": "<empty>",
          "ORDER": 0,
          "FILENAME": "<empty>",
          "AST_PARENT_FULL_NAME": "<global>",
          "IS_EXTERNAL": true,
          "AST_PARENT_TYPE": "NAMESPACE_BLOCK",
          "CODE": "<empty>"
        }
      },
      {
        "id": 144,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771076,
          "NAME": "<operator>.assignment",
          "ARGUMENT_INDEX": -1,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "SecurityDemo\\FileUpload\\VulnerableFileUploader",
          "ORDER": 1,
          "LINE_NUMBER": 27,
          "METHOD_FULL_NAME": "<operator>.assignment",
          "CODE": "$SecurityDemo\\FileUpload\\FileUploadController->__construct@tmp-0 = SecurityDemo\\FileUpload\\VulnerableFileUploader.<alloc>()"
        }
      },
      {
        "id": 145,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771077,
          "NAME": "<operator>.alloc",
          "ARGUMENT_INDEX": 2,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "SecurityDemo\\FileUpload\\VulnerableFileUploader",
          "ORDER": 2,
          "LINE_NUMBER": 27,
          "METHOD_FULL_NAME": "<operator>.alloc",
          "CODE": "SecurityDemo\\FileUpload\\VulnerableFileUploader.<alloc>()"
        }
      },
      {
        "id": 2396,
        "labels": [
          "METHOD"
        ],
        "properties": {
          "id": 111669149697,
          "NAME": "__construct",
          "FULL_NAME": "SecurityDemo\\FileUpload\\FileUploadController->__construct",
          "SIGNATURE": "<unresolvedSignature>(0)",
          "GENERIC_SIGNATURE": "<empty>",
          "ORDER": 3,
          "FILENAME": "FileUploadHandler.php",
          "LINE_NUMBER": 25,
          "IS_EXTERNAL": false,
          "AST_PARENT_FULL_NAME": "SecurityDemo\\FileUpload\\FileUploadController",
          "AST_PARENT_TYPE": "TYPE_DECL",
          "CODE": "PUBLIC function __construct(this)"
        }
      },
      {
        "id": 150,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771082,
          "NAME": "<operator>.alloc",
          "ARGUMENT_INDEX": 2,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "SecurityDemo\\FileUpload\\FileValidator",
          "ORDER": 2,
          "LINE_NUMBER": 28,
          "METHOD_FULL_NAME": "<operator>.alloc",
          "CODE": "SecurityDemo\\FileUpload\\FileValidator.<alloc>()"
        }
      },
      {
        "id": 149,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771081,
          "NAME": "<operator>.assignment",
          "ARGUMENT_INDEX": -1,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "SecurityDemo\\FileUpload\\FileValidator",
          "ORDER": 1,
          "LINE_NUMBER": 28,
          "METHOD_FULL_NAME": "<operator>.assignment",
          "CODE": "$SecurityDemo\\FileUpload\\FileUploadController->__construct@tmp-1 = SecurityDemo\\FileUpload\\FileValidator.<alloc>()"
        }
      },
      {
        "id": 1538,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476741,
          "NAME": "SecurityDemo\\FileUpload\\FileUploadController->__construct@tmp-1",
          "ARGUMENT_INDEX": 1,
          "TYPE_FULL_NAME": "SecurityDemo\\FileUpload\\FileValidator",
          "ORDER": 1,
          "LINE_NUMBER": 28,
          "CODE": "$SecurityDemo\\FileUpload\\FileUploadController->__construct@tmp-1"
        }
      },
      {
        "id": 1999,
        "labels": [
          "LOCAL"
        ],
        "properties": {
          "id": 94489280514,
          "NAME": "SecurityDemo\\FileUpload\\FileUploadController->__construct@tmp-1",
          "GENERIC_SIGNATURE": "<empty>",
          "TYPE_FULL_NAME": "SecurityDemo\\FileUpload\\FileValidator",
          "ORDER": 2,
          "LINE_NUMBER": 28,
          "CODE": "$SecurityDemo\\FileUpload\\FileUploadController->__construct@tmp-1"
        }
      },
      {
        "id": 1539,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476742,
          "NAME": "SecurityDemo\\FileUpload\\FileUploadController->__construct@tmp-1",
          "ARGUMENT_INDEX": 0,
          "TYPE_FULL_NAME": "SecurityDemo\\FileUpload\\FileValidator",
          "ORDER": 1,
          "LINE_NUMBER": 28,
          "CODE": "$SecurityDemo\\FileUpload\\FileUploadController->__construct@tmp-1"
        }
      },
      {
        "id": 151,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771083,
          "NAME": "__construct",
          "ARGUMENT_INDEX": -1,
          "SIGNATURE": "<unresolvedSignature>(0)",
          "DISPATCH_TYPE": "DYNAMIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 2,
          "LINE_NUMBER": 28,
          "METHOD_FULL_NAME": "SecurityDemo\\FileUpload\\FileValidator->__construct",
          "DYNAMIC_TYPE_HINT_FULL_NAME": [
            "SecurityDemo\\FileUpload\\FileValidator->__construct"
          ],
          "CODE": "SecurityDemo\\FileUpload\\FileValidator->__construct()"
        }
      },
      {
        "id": 2408,
        "labels": [
          "METHOD"
        ],
        "properties": {
          "id": 111669149709,
          "NAME": "__construct",
          "FULL_NAME": "SecurityDemo\\FileUpload\\FileValidator->__construct",
          "SIGNATURE": "<unresolvedSignature>(0)",
          "GENERIC_SIGNATURE": "<empty>",
          "ORDER": 3,
          "FILENAME": "FileValidator.php",
          "LINE_NUMBER": 26,
          "IS_EXTERNAL": false,
          "AST_PARENT_FULL_NAME": "SecurityDemo\\FileUpload\\FileValidator",
          "AST_PARENT_TYPE": "TYPE_DECL",
          "CODE": "PUBLIC function __construct(this)"
        }
      },
      {
        "id": 1739,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476942,
          "NAME": "SecurityDemo\\FileUpload\\FileValidator->__construct@tmp-4",
          "ARGUMENT_INDEX": 1,
          "TYPE_FULL_NAME": "array",
          "ORDER": 1,
          "LINE_NUMBER": 42,
          "DYNAMIC_TYPE_HINT_FULL_NAME": [
            "array",
            "array-><returnValue>"
          ],
          "CODE": "$SecurityDemo\\FileUpload\\FileValidator->__construct@tmp-4"
        }
      },
      {
        "id": 2042,
        "labels": [
          "LOCAL"
        ],
        "properties": {
          "id": 94489280557,
          "NAME": "SecurityDemo\\FileUpload\\FileValidator->__construct@tmp-4",
          "GENERIC_SIGNATURE": "<empty>",
          "TYPE_FULL_NAME": "array",
          "ORDER": 5,
          "LINE_NUMBER": 36,
          "DYNAMIC_TYPE_HINT_FULL_NAME": [
            "array",
            "array-><returnValue>"
          ],
          "CODE": "$SecurityDemo\\FileUpload\\FileValidator->__construct@tmp-4"
        }
      },
      {
        "id": 1733,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476936,
          "NAME": "SecurityDemo\\FileUpload\\FileValidator->__construct@tmp-4",
          "ARGUMENT_INDEX": 1,
          "TYPE_FULL_NAME": "array",
          "ORDER": 1,
          "LINE_NUMBER": 36,
          "DYNAMIC_TYPE_HINT_FULL_NAME": [
            "array",
            "array-><returnValue>"
          ],
          "CODE": "$SecurityDemo\\FileUpload\\FileValidator->__construct@tmp-4"
        }
      },
      {
        "id": 1377,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771350,
          "NAME": "<operator>.assignment",
          "ARGUMENT_INDEX": -1,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 1,
          "LINE_NUMBER": 36,
          "METHOD_FULL_NAME": "<operator>.assignment",
          "CODE": "$SecurityDemo\\FileUpload\\FileValidator->__construct@tmp-4 = array()"
        }
      },
      {
        "id": 1378,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771351,
          "NAME": "array",
          "ARGUMENT_INDEX": 2,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "SIGNATURE": "array()",
          "TYPE_FULL_NAME": "array",
          "ORDER": 2,
          "LINE_NUMBER": 36,
          "METHOD_FULL_NAME": "array",
          "DYNAMIC_TYPE_HINT_FULL_NAME": [
            "array"
          ],
          "CODE": "array()"
        }
      },
      {
        "id": 2424,
        "labels": [
          "METHOD"
        ],
        "properties": {
          "id": 111669149725,
          "FULL_NAME": "array",
          "NAME": "array",
          "SIGNATURE": "array()",
          "GENERIC_SIGNATURE": "<empty>",
          "ORDER": 0,
          "FILENAME": "<empty>",
          "AST_PARENT_FULL_NAME": "<global>",
          "IS_EXTERNAL": true,
          "AST_PARENT_TYPE": "NAMESPACE_BLOCK",
          "CODE": "<empty>"
        }
      },
      {
        "id": 249,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771181,
          "NAME": "array",
          "ARGUMENT_INDEX": 2,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "SIGNATURE": "array()",
          "TYPE_FULL_NAME": "array",
          "ORDER": 2,
          "LINE_NUMBER": 101,
          "METHOD_FULL_NAME": "array",
          "DYNAMIC_TYPE_HINT_FULL_NAME": [
            "array"
          ],
          "CODE": "array()"
        }
      },
      {
        "id": 248,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771180,
          "NAME": "<operator>.assignment",
          "ARGUMENT_INDEX": -1,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 1,
          "LINE_NUMBER": 101,
          "METHOD_FULL_NAME": "<operator>.assignment",
          "CODE": "$SecurityDemo\\FileUpload\\FileUploadController->processBatchUpload@tmp-3 = array()"
        }
      },
      {
        "id": 1613,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476816,
          "NAME": "SecurityDemo\\FileUpload\\FileUploadController->processBatchUpload@tmp-3",
          "ARGUMENT_INDEX": 1,
          "TYPE_FULL_NAME": "array",
          "ORDER": 1,
          "LINE_NUMBER": 101,
          "DYNAMIC_TYPE_HINT_FULL_NAME": [
            "array",
            "array-><returnValue>"
          ],
          "CODE": "$SecurityDemo\\FileUpload\\FileUploadController->processBatchUpload@tmp-3"
        }
      },
      {
        "id": 2014,
        "labels": [
          "LOCAL"
        ],
        "properties": {
          "id": 94489280529,
          "NAME": "SecurityDemo\\FileUpload\\FileUploadController->processBatchUpload@tmp-3",
          "GENERIC_SIGNATURE": "<empty>",
          "TYPE_FULL_NAME": "array",
          "ORDER": 4,
          "LINE_NUMBER": 101,
          "DYNAMIC_TYPE_HINT_FULL_NAME": [
            "array",
            "array-><returnValue>"
          ],
          "CODE": "$SecurityDemo\\FileUpload\\FileUploadController->processBatchUpload@tmp-3"
        }
      },
      {
        "id": 1614,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476817,
          "NAME": "SecurityDemo\\FileUpload\\FileUploadController->processBatchUpload@tmp-3",
          "ARGUMENT_INDEX": 1,
          "TYPE_FULL_NAME": "array",
          "ORDER": 1,
          "LINE_NUMBER": 101,
          "DYNAMIC_TYPE_HINT_FULL_NAME": [
            "array",
            "array-><returnValue>"
          ],
          "CODE": "$SecurityDemo\\FileUpload\\FileUploadController->processBatchUpload@tmp-3"
        }
      },
      {
        "id": 251,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771183,
          "NAME": "<operator>.indexAccess",
          "ARGUMENT_INDEX": 1,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 1,
          "LINE_NUMBER": 101,
          "METHOD_FULL_NAME": "<operator>.indexAccess",
          "CODE": "$SecurityDemo\FileUpload\FileUploadController->processBatchUpload@tmp-3["success"]"
        }
      },
      {
        "id": 1615,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476818,
          "NAME": "SecurityDemo\\FileUpload\\FileUploadController->processBatchUpload@tmp-3",
          "ARGUMENT_INDEX": 1,
          "TYPE_FULL_NAME": "array",
          "ORDER": 1,
          "LINE_NUMBER": 101,
          "DYNAMIC_TYPE_HINT_FULL_NAME": [
            "array",
            "array-><returnValue>"
          ],
          "CODE": "$SecurityDemo\\FileUpload\\FileUploadController->processBatchUpload@tmp-3"
        }
      },
      {
        "id": 253,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771185,
          "NAME": "<operator>.indexAccess",
          "ARGUMENT_INDEX": 1,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 1,
          "LINE_NUMBER": 101,
          "METHOD_FULL_NAME": "<operator>.indexAccess",
          "CODE": "$SecurityDemo\FileUpload\FileUploadController->processBatchUpload@tmp-3["results"]"
        }
      },
      {
        "id": 252,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771184,
          "NAME": "<operator>.assignment",
          "ARGUMENT_INDEX": -1,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 3,
          "LINE_NUMBER": 101,
          "METHOD_FULL_NAME": "<operator>.assignment",
          "CODE": "$SecurityDemo\FileUpload\FileUploadController->processBatchUpload@tmp-3["results"] = $results"
        }
      },
      {
        "id": 1616,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476819,
          "NAME": "results",
          "ARGUMENT_INDEX": 2,
          "TYPE_FULL_NAME": "array",
          "ORDER": 2,
          "LINE_NUMBER": 101,
          "DYNAMIC_TYPE_HINT_FULL_NAME": [
            "array",
            "array-><returnValue>"
          ],
          "CODE": "$results"
        }
      },
      {
        "id": 2022,
        "labels": [
          "LOCAL"
        ],
        "properties": {
          "id": 94489280537,
          "NAME": "results",
          "GENERIC_SIGNATURE": "<empty>",
          "TYPE_FULL_NAME": "array",
          "ORDER": 12,
          "LINE_NUMBER": 81,
          "DYNAMIC_TYPE_HINT_FULL_NAME": [
            "array",
            "array-><returnValue>"
          ],
          "CODE": "$results"
        }
      },
      {
        "id": 1577,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476780,
          "NAME": "results",
          "ARGUMENT_INDEX": 1,
          "TYPE_FULL_NAME": "array",
          "ORDER": 1,
          "LINE_NUMBER": 81,
          "DYNAMIC_TYPE_HINT_FULL_NAME": [
            "array",
            "array-><returnValue>"
          ],
          "CODE": "$results"
        }
      },
      {
        "id": 1611,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476814,
          "NAME": "results",
          "ARGUMENT_INDEX": 1,
          "TYPE_FULL_NAME": "array",
          "ORDER": 1,
          "LINE_NUMBER": 98,
          "DYNAMIC_TYPE_HINT_FULL_NAME": [
            "array",
            "array-><returnValue>"
          ],
          "CODE": "$results"
        }
      },
      {
        "id": 247,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771179,
          "NAME": "array_push",
          "ARGUMENT_INDEX": -1,
          "SIGNATURE": "<unresolvedSignature>(2)",
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 4,
          "LINE_NUMBER": 98,
          "METHOD_FULL_NAME": "array_push",
          "DYNAMIC_TYPE_HINT_FULL_NAME": [
            "array_push"
          ],
          "CODE": "$results[] = $result"
        }
      },
      {
        "id": 1612,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476815,
          "NAME": "result",
          "ARGUMENT_INDEX": 2,
          "TYPE_FULL_NAME": "array",
          "ORDER": 2,
          "LINE_NUMBER": 98,
          "CODE": "$result"
        }
      },
      {
        "id": 2021,
        "labels": [
          "LOCAL"
        ],
        "properties": {
          "id": 94489280536,
          "NAME": "result",
          "GENERIC_SIGNATURE": "<empty>",
          "TYPE_FULL_NAME": "array",
          "ORDER": 11,
          "LINE_NUMBER": 97,
          "CODE": "$result"
        }
      },
      {
        "id": 1607,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476810,
          "NAME": "result",
          "ARGUMENT_INDEX": 1,
          "TYPE_FULL_NAME": "array",
          "ORDER": 1,
          "LINE_NUMBER": 97,
          "CODE": "$result"
        }
      },
      {
        "id": 244,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771176,
          "NAME": "<operator>.assignment",
          "ARGUMENT_INDEX": -1,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 3,
          "LINE_NUMBER": 97,
          "METHOD_FULL_NAME": "<operator>.assignment",
          "CODE": "$result = $this->uploader->handleUpload($fileData,$customName)"
        }
      },
      {
        "id": 245,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771177,
          "NAME": "handleUpload",
          "ARGUMENT_INDEX": 2,
          "SIGNATURE": "<unresolvedSignature>(2)",
          "DISPATCH_TYPE": "DYNAMIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 2,
          "LINE_NUMBER": 97,
          "METHOD_FULL_NAME": "SecurityDemo\\FileUpload\\VulnerableFileUploader->handleUpload-><returnValue>",
          "CODE": "$this->uploader->handleUpload($fileData,$customName)"
        }
      },
      {
        "id": 246,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771178,
          "NAME": "<operator>.fieldAccess",
          "ARGUMENT_INDEX": 0,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 1,
          "LINE_NUMBER": 97,
          "METHOD_FULL_NAME": "<operator>.fieldAccess",
          "CODE": "$this->uploader"
        }
      },
      {
        "id": 1608,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476811,
          "NAME": "this",
          "ARGUMENT_INDEX": 1,
          "TYPE_FULL_NAME": "SecurityDemo\\FileUpload\\FileUploadController",
          "ORDER": 1,
          "LINE_NUMBER": 97,
          "CODE": "$this"
        }
      },
      {
        "id": 2070,
        "labels": [
          "METHOD_PARAMETER_IN"
        ],
        "properties": {
          "IS_VARIADIC": false,
          "id": 115964116995,
          "INDEX": 0,
          "NAME": "this",
          "TYPE_FULL_NAME": "SecurityDemo\\FileUpload\\FileUploadController",
          "ORDER": 0,
          "EVALUATION_STRATEGY": "BY_SHARING",
          "LINE_NUMBER": 75,
          "DYNAMIC_TYPE_HINT_FULL_NAME": [
            "SecurityDemo\\FileUpload\\FileUploadController"
          ],
          "CODE": "this"
        }
      },
      {
        "id": 2399,
        "labels": [
          "METHOD"
        ],
        "properties": {
          "id": 111669149700,
          "NAME": "processBatchUpload",
          "FULL_NAME": "SecurityDemo\\FileUpload\\FileUploadController->processBatchUpload",
          "SIGNATURE": "<unresolvedSignature>(0)",
          "GENERIC_SIGNATURE": "<empty>",
          "ORDER": 6,
          "FILENAME": "FileUploadHandler.php",
          "LINE_NUMBER": 75,
          "IS_EXTERNAL": false,
          "AST_PARENT_FULL_NAME": "SecurityDemo\\FileUpload\\FileUploadController",
          "AST_PARENT_TYPE": "TYPE_DECL",
          "CODE": "PRIVATE function processBatchUpload(this)"
        }
      },
      {
        "id": 1584,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476787,
          "NAME": "files",
          "ARGUMENT_INDEX": 1,
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 1,
          "LINE_NUMBER": 85,
          "CODE": "$files"
        }
      },
      {
        "id": 2019,
        "labels": [
          "LOCAL"
        ],
        "properties": {
          "id": 94489280534,
          "NAME": "files",
          "GENERIC_SIGNATURE": "<empty>",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 9,
          "LINE_NUMBER": 82,
          "CODE": "$files"
        }
      },
      {
        "id": 1595,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476798,
          "NAME": "files",
          "ARGUMENT_INDEX": 1,
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 1,
          "LINE_NUMBER": 89,
          "CODE": "$files"
        }
      },
      {
        "id": 231,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771163,
          "NAME": "<operator>.indexAccess",
          "ARGUMENT_INDEX": 1,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 1,
          "LINE_NUMBER": 89,
          "METHOD_FULL_NAME": "<operator>.indexAccess",
          "CODE": "$files["tmp_name"]"
        }
      },
      {
        "id": 230,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771162,
          "NAME": "<operator>.indexAccess",
          "ARGUMENT_INDEX": 2,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 2,
          "LINE_NUMBER": 89,
          "METHOD_FULL_NAME": "<operator>.indexAccess",
          "CODE": "$files["tmp_name"][$i]"
        }
      },
      {
        "id": 1596,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476799,
          "NAME": "i",
          "ARGUMENT_INDEX": 2,
          "TYPE_FULL_NAME": "int",
          "ORDER": 2,
          "LINE_NUMBER": 89,
          "CODE": "$i"
        }
      },
      {
        "id": 2020,
        "labels": [
          "LOCAL"
        ],
        "properties": {
          "id": 94489280535,
          "NAME": "i",
          "GENERIC_SIGNATURE": "<empty>",
          "TYPE_FULL_NAME": "int",
          "ORDER": 10,
          "LINE_NUMBER": 85,
          "CODE": "$i"
        }
      },
      {
        "id": 1582,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476785,
          "NAME": "i",
          "ARGUMENT_INDEX": 1,
          "TYPE_FULL_NAME": "int",
          "ORDER": 1,
          "LINE_NUMBER": 85,
          "CODE": "$i"
        }
      },
      {
        "id": 1583,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476786,
          "NAME": "i",
          "ARGUMENT_INDEX": 1,
          "TYPE_FULL_NAME": "int",
          "ORDER": 1,
          "LINE_NUMBER": 85,
          "CODE": "$i"
        }
      },
      {
        "id": 213,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771145,
          "NAME": "<operator>.lessThan",
          "ARGUMENT_INDEX": -1,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 2,
          "LINE_NUMBER": 85,
          "METHOD_FULL_NAME": "<operator>.lessThan",
          "CODE": "$i < count($files["name"])"
        }
      },
      {
        "id": 214,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771146,
          "NAME": "count",
          "ARGUMENT_INDEX": 2,
          "SIGNATURE": "<unresolvedSignature>(1)",
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 2,
          "LINE_NUMBER": 85,
          "METHOD_FULL_NAME": "count",
          "DYNAMIC_TYPE_HINT_FULL_NAME": [
            "count"
          ],
          "CODE": "count($files["name"])"
        }
      },
      {
        "id": 215,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771147,
          "NAME": "<operator>.indexAccess",
          "ARGUMENT_INDEX": 1,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 1,
          "LINE_NUMBER": 85,
          "METHOD_FULL_NAME": "<operator>.indexAccess",
          "CODE": "$files["name"]"
        }
      },
      {
        "id": 1601,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476804,
          "NAME": "files",
          "ARGUMENT_INDEX": 1,
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 1,
          "LINE_NUMBER": 91,
          "CODE": "$files"
        }
      },
      {
        "id": 239,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771171,
          "NAME": "<operator>.indexAccess",
          "ARGUMENT_INDEX": 1,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 1,
          "LINE_NUMBER": 91,
          "METHOD_FULL_NAME": "<operator>.indexAccess",
          "CODE": "$files["size"]"
        }
      },
      {
        "id": 238,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771170,
          "NAME": "<operator>.indexAccess",
          "ARGUMENT_INDEX": 2,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 2,
          "LINE_NUMBER": 91,
          "METHOD_FULL_NAME": "<operator>.indexAccess",
          "CODE": "$files["size"][$i]"
        }
      },
      {
        "id": 236,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771168,
          "NAME": "<operator>.assignment",
          "ARGUMENT_INDEX": -1,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 6,
          "LINE_NUMBER": 91,
          "METHOD_FULL_NAME": "<operator>.assignment",
          "CODE": "$SecurityDemo\FileUpload\FileUploadController->processBatchUpload@tmp-2["size"] = $files["size"][$i]"
        }
      },
      {
        "id": 237,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771169,
          "NAME": "<operator>.indexAccess",
          "ARGUMENT_INDEX": 1,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 1,
          "LINE_NUMBER": 91,
          "METHOD_FULL_NAME": "<operator>.indexAccess",
          "CODE": "$SecurityDemo\FileUpload\FileUploadController->processBatchUpload@tmp-2["size"]"
        }
      },
      {
        "id": 1600,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476803,
          "NAME": "SecurityDemo\\FileUpload\\FileUploadController->processBatchUpload@tmp-2",
          "ARGUMENT_INDEX": 1,
          "TYPE_FULL_NAME": "array",
          "ORDER": 1,
          "LINE_NUMBER": 91,
          "DYNAMIC_TYPE_HINT_FULL_NAME": [
            "array",
            "array-><returnValue>"
          ],
          "CODE": "$SecurityDemo\\FileUpload\\FileUploadController->processBatchUpload@tmp-2"
        }
      },
      {
        "id": 2013,
        "labels": [
          "LOCAL"
        ],
        "properties": {
          "id": 94489280528,
          "NAME": "SecurityDemo\\FileUpload\\FileUploadController->processBatchUpload@tmp-2",
          "GENERIC_SIGNATURE": "<empty>",
          "TYPE_FULL_NAME": "array",
          "ORDER": 3,
          "LINE_NUMBER": 86,
          "DYNAMIC_TYPE_HINT_FULL_NAME": [
            "array",
            "array-><returnValue>"
          ],
          "CODE": "$SecurityDemo\\FileUpload\\FileUploadController->processBatchUpload@tmp-2"
        }
      },
      {
        "id": 1597,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476800,
          "NAME": "SecurityDemo\\FileUpload\\FileUploadController->processBatchUpload@tmp-2",
          "ARGUMENT_INDEX": 1,
          "TYPE_FULL_NAME": "array",
          "ORDER": 1,
          "LINE_NUMBER": 90,
          "DYNAMIC_TYPE_HINT_FULL_NAME": [
            "array",
            "array-><returnValue>"
          ],
          "CODE": "$SecurityDemo\\FileUpload\\FileUploadController->processBatchUpload@tmp-2"
        }
      },
      {
        "id": 233,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771165,
          "NAME": "<operator>.indexAccess",
          "ARGUMENT_INDEX": 1,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 1,
          "LINE_NUMBER": 90,
          "METHOD_FULL_NAME": "<operator>.indexAccess",
          "CODE": "$SecurityDemo\FileUpload\FileUploadController->processBatchUpload@tmp-2["error"]"
        }
      },
      {
        "id": 232,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771164,
          "NAME": "<operator>.assignment",
          "ARGUMENT_INDEX": -1,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 5,
          "LINE_NUMBER": 90,
          "METHOD_FULL_NAME": "<operator>.assignment",
          "CODE": "$SecurityDemo\FileUpload\FileUploadController->processBatchUpload@tmp-2["error"] = $files["error"][$i]"
        }
      },
      {
        "id": 234,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771166,
          "NAME": "<operator>.indexAccess",
          "ARGUMENT_INDEX": 2,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 2,
          "LINE_NUMBER": 90,
          "METHOD_FULL_NAME": "<operator>.indexAccess",
          "CODE": "$files["error"][$i]"
        }
      },
      {
        "id": 235,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771167,
          "NAME": "<operator>.indexAccess",
          "ARGUMENT_INDEX": 1,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 1,
          "LINE_NUMBER": 90,
          "METHOD_FULL_NAME": "<operator>.indexAccess",
          "CODE": "$files["error"]"
        }
      },
      {
        "id": 1598,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476801,
          "NAME": "files",
          "ARGUMENT_INDEX": 1,
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 1,
          "LINE_NUMBER": 90,
          "CODE": "$files"
        }
      },
      {
        "id": 1580,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476783,
          "NAME": "files",
          "ARGUMENT_INDEX": 1,
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 1,
          "LINE_NUMBER": 82,
          "CODE": "$files"
        }
      },
      {
        "id": 210,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771142,
          "NAME": "<operator>.assignment",
          "ARGUMENT_INDEX": -1,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 15,
          "LINE_NUMBER": 82,
          "METHOD_FULL_NAME": "<operator>.assignment",
          "CODE": "$files = $_FILES["files"]"
        }
      },
      {
        "id": 211,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771143,
          "NAME": "<operator>.indexAccess",
          "ARGUMENT_INDEX": 2,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 2,
          "LINE_NUMBER": 82,
          "METHOD_FULL_NAME": "<operator>.indexAccess",
          "CODE": "$_FILES["files"]"
        }
      },
      {
        "id": 1581,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476784,
          "NAME": "_FILES",
          "ARGUMENT_INDEX": 1,
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 1,
          "LINE_NUMBER": 82,
          "CODE": "$_FILES"
        }
      },
      {
        "id": 2015,
        "labels": [
          "LOCAL"
        ],
        "properties": {
          "id": 94489280530,
          "NAME": "_FILES",
          "GENERIC_SIGNATURE": "<empty>",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 5,
          "LINE_NUMBER": 77,
          "CODE": "$_FILES"
        }
      },
      {
        "id": 1572,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476775,
          "NAME": "_FILES",
          "ARGUMENT_INDEX": 1,
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 1,
          "LINE_NUMBER": 77,
          "CODE": "$_FILES"
        }
      },
      {
        "id": 200,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771132,
          "NAME": "<operator>.indexAccess",
          "ARGUMENT_INDEX": 1,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 1,
          "LINE_NUMBER": 77,
          "METHOD_FULL_NAME": "<operator>.indexAccess",
          "CODE": "$_FILES["files"]"
        }
      },
      {
        "id": 199,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771131,
          "NAME": "isset",
          "ARGUMENT_INDEX": 1,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "bool",
          "ORDER": 1,
          "LINE_NUMBER": 77,
          "METHOD_FULL_NAME": "isset",
          "DYNAMIC_TYPE_HINT_FULL_NAME": [
            "isset"
          ],
          "CODE": "isset($_FILES["files"])"
        }
      },
      {
        "id": 198,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771130,
          "NAME": "<operator>.logicalNot",
          "ARGUMENT_INDEX": -1,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 1,
          "LINE_NUMBER": 77,
          "METHOD_FULL_NAME": "<operator>.logicalNot",
          "CODE": "!isset($_FILES["files"])"
        }
      },
      {
        "id": 2425,
        "labels": [
          "METHOD"
        ],
        "properties": {
          "id": 111669149726,
          "FULL_NAME": "<operator>.logicalNot",
          "NAME": "<operator>.logicalNot",
          "GENERIC_SIGNATURE": "<empty>",
          "ORDER": 0,
          "FILENAME": "<empty>",
          "AST_PARENT_FULL_NAME": "<global>",
          "IS_EXTERNAL": true,
          "AST_PARENT_TYPE": "NAMESPACE_BLOCK",
          "CODE": "<empty>"
        }
      },
      {
        "id": 353,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771285,
          "NAME": "<operator>.logicalNot",
          "ARGUMENT_INDEX": 1,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 1,
          "LINE_NUMBER": 122,
          "METHOD_FULL_NAME": "<operator>.logicalNot",
          "CODE": "!empty($customName)"
        }
      },
      {
        "id": 352,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771284,
          "NAME": "<operator>.conditional",
          "ARGUMENT_INDEX": 2,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 2,
          "LINE_NUMBER": 122,
          "METHOD_FULL_NAME": "<operator>.conditional",
          "CODE": "!empty($customName) ? $customName : $originalName"
        }
      },
      {
        "id": 1687,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476890,
          "NAME": "originalName",
          "ARGUMENT_INDEX": 3,
          "TYPE_FULL_NAME": "string",
          "ORDER": 3,
          "LINE_NUMBER": 122,
          "CODE": "$originalName"
        }
      },
      {
        "id": 2082,
        "labels": [
          "METHOD_PARAMETER_IN"
        ],
        "properties": {
          "IS_VARIADIC": false,
          "id": 115964117007,
          "INDEX": 1,
          "NAME": "originalName",
          "TYPE_FULL_NAME": "string",
          "ORDER": 1,
          "EVALUATION_STRATEGY": "BY_VALUE",
          "LINE_NUMBER": 120,
          "CODE": "$originalName"
        }
      },
      {
        "id": 2406,
        "labels": [
          "METHOD"
        ],
        "properties": {
          "id": 111669149707,
          "NAME": "processFileName",
          "FULL_NAME": "SecurityDemo\\FileUpload\\VulnerableFileUploader->processFileName",
          "SIGNATURE": "<unresolvedSignature>(2)",
          "GENERIC_SIGNATURE": "<empty>",
          "ORDER": 9,
          "FILENAME": "FileUploadVulnerability.php",
          "LINE_NUMBER": 120,
          "IS_EXTERNAL": false,
          "AST_PARENT_FULL_NAME": "SecurityDemo\\FileUpload\\VulnerableFileUploader",
          "AST_PARENT_TYPE": "TYPE_DECL",
          "CODE": "PRIVATE function processFileName(this,$originalName,$customName)"
        }
      },
      {
        "id": 1708,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476911,
          "NAME": "fileName",
          "ARGUMENT_INDEX": 1,
          "TYPE_FULL_NAME": "string",
          "ORDER": 1,
          "LINE_NUMBER": 139,
          "DYNAMIC_TYPE_HINT_FULL_NAME": [
            "string",
            "str_replace-><returnValue>"
          ],
          "CODE": "$fileName"
        }
      },
      {
        "id": 2037,
        "labels": [
          "LOCAL"
        ],
        "properties": {
          "id": 94489280552,
          "NAME": "fileName",
          "GENERIC_SIGNATURE": "<empty>",
          "TYPE_FULL_NAME": "string",
          "ORDER": 6,
          "LINE_NUMBER": 122,
          "DYNAMIC_TYPE_HINT_FULL_NAME": [
            "string",
            "str_replace-><returnValue>"
          ],
          "CODE": "$fileName"
        }
      },
      {
        "id": 1707,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476910,
          "NAME": "fileName",
          "ARGUMENT_INDEX": 1,
          "TYPE_FULL_NAME": "string",
          "ORDER": 1,
          "LINE_NUMBER": 134,
          "DYNAMIC_TYPE_HINT_FULL_NAME": [
            "string",
            "str_replace-><returnValue>"
          ],
          "CODE": "$fileName"
        }
      },
      {
        "id": 1695,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476898,
          "NAME": "fileName",
          "ARGUMENT_INDEX": 1,
          "TYPE_FULL_NAME": "string",
          "ORDER": 1,
          "LINE_NUMBER": 128,
          "DYNAMIC_TYPE_HINT_FULL_NAME": [
            "string",
            "str_replace-><returnValue>"
          ],
          "CODE": "$fileName"
        }
      },
      {
        "id": 1324,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771297,
          "NAME": "pathinfo",
          "ARGUMENT_INDEX": 1,
          "SIGNATURE": "<unresolvedSignature>(2)",
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 1,
          "LINE_NUMBER": 128,
          "METHOD_FULL_NAME": "pathinfo",
          "DYNAMIC_TYPE_HINT_FULL_NAME": [
            "pathinfo"
          ],
          "CODE": "pathinfo($fileName,PATHINFO_EXTENSION)"
        }
      },
      {
        "id": 1325,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771298,
          "NAME": "<operator>.fieldAccess",
          "ARGUMENT_INDEX": 2,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 2,
          "LINE_NUMBER": 128,
          "METHOD_FULL_NAME": "<operator>.fieldAccess",
          "CODE": "PATHINFO_EXTENSION"
        }
      },
      {
        "id": 2417,
        "labels": [
          "METHOD"
        ],
        "properties": {
          "id": 111669149718,
          "FULL_NAME": "<operator>.fieldAccess",
          "NAME": "<operator>.fieldAccess",
          "GENERIC_SIGNATURE": "<empty>",
          "ORDER": 0,
          "FILENAME": "<empty>",
          "AST_PARENT_FULL_NAME": "<global>",
          "IS_EXTERNAL": true,
          "AST_PARENT_TYPE": "NAMESPACE_BLOCK",
          "CODE": "<empty>"
        }
      },
      {
        "id": 273,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771205,
          "NAME": "<operator>.fieldAccess",
          "ARGUMENT_INDEX": 1,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 1,
          "LINE_NUMBER": 34,
          "METHOD_FULL_NAME": "<operator>.fieldAccess",
          "CODE": "$this->uploadDir"
        }
      },
      {
        "id": 272,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771204,
          "NAME": "<operator>.assignment",
          "ARGUMENT_INDEX": -1,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 3,
          "LINE_NUMBER": 34,
          "METHOD_FULL_NAME": "<operator>.assignment",
          "CODE": "$this->uploadDir = $uploadDir"
        }
      },
      {
        "id": 1631,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476834,
          "NAME": "uploadDir",
          "ARGUMENT_INDEX": 2,
          "TYPE_FULL_NAME": "string",
          "ORDER": 2,
          "LINE_NUMBER": 34,
          "CODE": "$uploadDir"
        }
      },
      {
        "id": 2074,
        "labels": [
          "METHOD_PARAMETER_IN"
        ],
        "properties": {
          "IS_VARIADIC": false,
          "id": 115964116999,
          "INDEX": 1,
          "NAME": "uploadDir",
          "TYPE_FULL_NAME": "string",
          "ORDER": 1,
          "EVALUATION_STRATEGY": "BY_VALUE",
          "LINE_NUMBER": 32,
          "CODE": "$uploadDir"
        }
      },
      {
        "id": 2402,
        "labels": [
          "METHOD"
        ],
        "properties": {
          "id": 111669149703,
          "NAME": "__construct",
          "FULL_NAME": "SecurityDemo\\FileUpload\\VulnerableFileUploader->__construct",
          "SIGNATURE": "<unresolvedSignature>(1)",
          "GENERIC_SIGNATURE": "<empty>",
          "ORDER": 5,
          "FILENAME": "FileUploadVulnerability.php",
          "LINE_NUMBER": 32,
          "IS_EXTERNAL": false,
          "AST_PARENT_FULL_NAME": "SecurityDemo\\FileUpload\\VulnerableFileUploader",
          "AST_PARENT_TYPE": "TYPE_DECL",
          "CODE": "PUBLIC function __construct(this,$uploadDir)"
        }
      },
      {
        "id": 1634,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476837,
          "NAME": "SecurityDemo\\FileUpload\\VulnerableFileUploader->__construct@tmp-0",
          "ARGUMENT_INDEX": 1,
          "TYPE_FULL_NAME": "array",
          "ORDER": 1,
          "LINE_NUMBER": 37,
          "DYNAMIC_TYPE_HINT_FULL_NAME": [
            "array",
            "array-><returnValue>"
          ],
          "CODE": "$SecurityDemo\\FileUpload\\VulnerableFileUploader->__construct@tmp-0"
        }
      },
      {
        "id": 2024,
        "labels": [
          "LOCAL"
        ],
        "properties": {
          "id": 94489280539,
          "NAME": "SecurityDemo\\FileUpload\\VulnerableFileUploader->__construct@tmp-0",
          "GENERIC_SIGNATURE": "<empty>",
          "TYPE_FULL_NAME": "array",
          "ORDER": 1,
          "LINE_NUMBER": 36,
          "DYNAMIC_TYPE_HINT_FULL_NAME": [
            "array",
            "array-><returnValue>"
          ],
          "CODE": "$SecurityDemo\\FileUpload\\VulnerableFileUploader->__construct@tmp-0"
        }
      },
      {
        "id": 1637,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476840,
          "NAME": "SecurityDemo\\FileUpload\\VulnerableFileUploader->__construct@tmp-0",
          "ARGUMENT_INDEX": 1,
          "TYPE_FULL_NAME": "array",
          "ORDER": 1,
          "LINE_NUMBER": 40,
          "DYNAMIC_TYPE_HINT_FULL_NAME": [
            "array",
            "array-><returnValue>"
          ],
          "CODE": "$SecurityDemo\\FileUpload\\VulnerableFileUploader->__construct@tmp-0"
        }
      },
      {
        "id": 285,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771217,
          "NAME": "<operator>.indexAccess",
          "ARGUMENT_INDEX": 1,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 1,
          "LINE_NUMBER": 40,
          "METHOD_FULL_NAME": "<operator>.indexAccess",
          "CODE": "$SecurityDemo\\FileUpload\\VulnerableFileUploader->__construct@tmp-0[3]"
        }
      },
      {
        "id": 2420,
        "labels": [
          "METHOD"
        ],
        "properties": {
          "id": 111669149721,
          "FULL_NAME": "<operator>.indexAccess",
          "NAME": "<operator>.indexAccess",
          "GENERIC_SIGNATURE": "<empty>",
          "ORDER": 0,
          "FILENAME": "<empty>",
          "AST_PARENT_FULL_NAME": "<global>",
          "IS_EXTERNAL": true,
          "AST_PARENT_TYPE": "NAMESPACE_BLOCK",
          "CODE": "<empty>"
        }
      },
      {
        "id": 1428,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771401,
          "NAME": "<operator>.indexAccess",
          "ARGUMENT_INDEX": 2,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 2,
          "LINE_NUMBER": 88,
          "METHOD_FULL_NAME": "<operator>.indexAccess",
          "CODE": "$bytes[$i]"
        }
      },
      {
        "id": 1425,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771398,
          "NAME": "<operator>.notIdentical",
          "ARGUMENT_INDEX": 2,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 2,
          "LINE_NUMBER": 88,
          "METHOD_FULL_NAME": "<operator>.notIdentical",
          "CODE": "ord($header[$i]) !== $bytes[$i]"
        }
      },
      {
        "id": 1421,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771394,
          "NAME": "<operator>.logicalOr",
          "ARGUMENT_INDEX": -1,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 1,
          "LINE_NUMBER": 88,
          "METHOD_FULL_NAME": "<operator>.logicalOr",
          "CODE": "!isset($header[$i]) || ord($header[$i]) !== $bytes[$i]"
        }
      },
      {
        "id": 1422,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771395,
          "NAME": "<operator>.logicalNot",
          "ARGUMENT_INDEX": 1,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 1,
          "LINE_NUMBER": 88,
          "METHOD_FULL_NAME": "<operator>.logicalNot",
          "CODE": "!isset($header[$i])"
        }
      },
      {
        "id": 1423,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771396,
          "NAME": "isset",
          "ARGUMENT_INDEX": 1,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "bool",
          "ORDER": 1,
          "LINE_NUMBER": 88,
          "METHOD_FULL_NAME": "isset",
          "DYNAMIC_TYPE_HINT_FULL_NAME": [
            "isset"
          ],
          "CODE": "isset($header[$i])"
        }
      },
      {
        "id": 1424,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771397,
          "NAME": "<operator>.indexAccess",
          "ARGUMENT_INDEX": 1,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 1,
          "LINE_NUMBER": 88,
          "METHOD_FULL_NAME": "<operator>.indexAccess",
          "CODE": "$header[$i]"
        }
      },
      {
        "id": 1770,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476973,
          "NAME": "i",
          "ARGUMENT_INDEX": 2,
          "TYPE_FULL_NAME": "int",
          "ORDER": 2,
          "LINE_NUMBER": 88,
          "CODE": "$i"
        }
      },
      {
        "id": 2047,
        "labels": [
          "LOCAL"
        ],
        "properties": {
          "id": 94489280562,
          "NAME": "i",
          "GENERIC_SIGNATURE": "<empty>",
          "TYPE_FULL_NAME": "int",
          "ORDER": 5,
          "LINE_NUMBER": 87,
          "CODE": "$i"
        }
      },
      {
        "id": 1772,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476975,
          "NAME": "i",
          "ARGUMENT_INDEX": 2,
          "TYPE_FULL_NAME": "int",
          "ORDER": 2,
          "LINE_NUMBER": 88,
          "CODE": "$i"
        }
      },
      {
        "id": 1427,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771400,
          "NAME": "<operator>.indexAccess",
          "ARGUMENT_INDEX": 1,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 1,
          "LINE_NUMBER": 88,
          "METHOD_FULL_NAME": "<operator>.indexAccess",
          "CODE": "$header[$i]"
        }
      },
      {
        "id": 1771,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476974,
          "NAME": "header",
          "ARGUMENT_INDEX": 1,
          "TYPE_FULL_NAME": "fread-><returnValue>",
          "ORDER": 1,
          "LINE_NUMBER": 88,
          "CODE": "$header"
        }
      },
      {
        "id": 2046,
        "labels": [
          "LOCAL"
        ],
        "properties": {
          "id": 94489280561,
          "NAME": "header",
          "GENERIC_SIGNATURE": "<empty>",
          "TYPE_FULL_NAME": "fread-><returnValue>",
          "ORDER": 4,
          "LINE_NUMBER": 81,
          "CODE": "$header"
        }
      },
      {
        "id": 1749,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476952,
          "NAME": "header",
          "ARGUMENT_INDEX": 1,
          "TYPE_FULL_NAME": "fread-><returnValue>",
          "ORDER": 1,
          "LINE_NUMBER": 81,
          "CODE": "$header"
        }
      },
      {
        "id": 1400,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771373,
          "NAME": "<operator>.assignment",
          "ARGUMENT_INDEX": -1,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 10,
          "LINE_NUMBER": 81,
          "METHOD_FULL_NAME": "<operator>.assignment",
          "CODE": "$header = fread($handle,8)"
        }
      },
      {
        "id": 1401,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771374,
          "NAME": "fread",
          "ARGUMENT_INDEX": 2,
          "SIGNATURE": "<unresolvedSignature>(2)",
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 2,
          "LINE_NUMBER": 81,
          "METHOD_FULL_NAME": "fread",
          "DYNAMIC_TYPE_HINT_FULL_NAME": [
            "fread"
          ],
          "CODE": "fread($handle,8)"
        }
      },
      {
        "id": 1750,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476953,
          "NAME": "handle",
          "ARGUMENT_INDEX": 1,
          "TYPE_FULL_NAME": "fopen-><returnValue>",
          "ORDER": 1,
          "LINE_NUMBER": 81,
          "CODE": "$handle"
        }
      },
      {
        "id": 2045,
        "labels": [
          "LOCAL"
        ],
        "properties": {
          "id": 94489280560,
          "NAME": "handle",
          "GENERIC_SIGNATURE": "<empty>",
          "TYPE_FULL_NAME": "fopen-><returnValue>",
          "ORDER": 3,
          "LINE_NUMBER": 76,
          "CODE": "$handle"
        }
      },
      {
        "id": 1751,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476954,
          "NAME": "handle",
          "ARGUMENT_INDEX": 1,
          "TYPE_FULL_NAME": "fopen-><returnValue>",
          "ORDER": 1,
          "LINE_NUMBER": 82,
          "CODE": "$handle"
        }
      },
      {
        "id": 2410,
        "labels": [
          "METHOD"
        ],
        "properties": {
          "id": 111669149711,
          "NAME": "checkMagicBytes",
          "FULL_NAME": "SecurityDemo\\FileUpload\\FileValidator->checkMagicBytes",
          "SIGNATURE": "<unresolvedSignature>(1)",
          "GENERIC_SIGNATURE": "<empty>",
          "ORDER": 5,
          "FILENAME": "FileValidator.php",
          "LINE_NUMBER": 74,
          "IS_EXTERNAL": false,
          "AST_PARENT_FULL_NAME": "SecurityDemo\\FileUpload\\FileValidator",
          "AST_PARENT_TYPE": "TYPE_DECL",
          "CODE": "PRIVATE function checkMagicBytes(this,$filePath)"
        }
      },
      {
        "id": 1767,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476970,
          "NAME": "bytes",
          "ARGUMENT_INDEX": 1,
          "TYPE_FULL_NAME": "Iterator.current-><returnValue>",
          "ORDER": 1,
          "LINE_NUMBER": 87,
          "CODE": "$bytes"
        }
      },
      {
        "id": 2044,
        "labels": [
          "LOCAL"
        ],
        "properties": {
          "id": 94489280559,
          "NAME": "bytes",
          "GENERIC_SIGNATURE": "<empty>",
          "TYPE_FULL_NAME": "Iterator.current-><returnValue>",
          "ORDER": 2,
          "LINE_NUMBER": 85,
          "CODE": "$bytes"
        }
      },
      {
        "id": 1762,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476965,
          "NAME": "bytes",
          "ARGUMENT_INDEX": 1,
          "TYPE_FULL_NAME": "Iterator.current-><returnValue>",
          "ORDER": 1,
          "LINE_NUMBER": 85,
          "CODE": "$bytes"
        }
      },
      {
        "id": 1414,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771387,
          "NAME": "<operator>.assignment",
          "ARGUMENT_INDEX": -1,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 2,
          "LINE_NUMBER": 85,
          "METHOD_FULL_NAME": "<operator>.assignment",
          "CODE": "$bytes = $SecurityDemo\\FileUpload\\FileValidator->checkMagicBytes@iter_tmp-0->current()"
        }
      },
      {
        "id": 1415,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771388,
          "NAME": "current",
          "ARGUMENT_INDEX": 2,
          "SIGNATURE": "<unresolvedSignature>(0)",
          "DISPATCH_TYPE": "DYNAMIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 2,
          "LINE_NUMBER": 85,
          "METHOD_FULL_NAME": "Iterator.current",
          "DYNAMIC_TYPE_HINT_FULL_NAME": [
            "Iterator.current"
          ],
          "CODE": "$SecurityDemo\\FileUpload\\FileValidator->checkMagicBytes@iter_tmp-0->current()"
        }
      },
      {
        "id": 1763,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476966,
          "NAME": "SecurityDemo\\FileUpload\\FileValidator->checkMagicBytes@iter_tmp-0",
          "ARGUMENT_INDEX": 0,
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 1,
          "LINE_NUMBER": 85,
          "DYNAMIC_TYPE_HINT_FULL_NAME": [
            "array",
            "array-><returnValue>"
          ],
          "CODE": "$SecurityDemo\\FileUpload\\FileValidator->checkMagicBytes@iter_tmp-0"
        }
      },
      {
        "id": 2043,
        "labels": [
          "LOCAL"
        ],
        "properties": {
          "id": 94489280558,
          "NAME": "SecurityDemo\\FileUpload\\FileValidator->checkMagicBytes@iter_tmp-0",
          "GENERIC_SIGNATURE": "<empty>",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 1,
          "LINE_NUMBER": 85,
          "DYNAMIC_TYPE_HINT_FULL_NAME": [
            "array",
            "array-><returnValue>"
          ],
          "CODE": "$SecurityDemo\\FileUpload\\FileValidator->checkMagicBytes@iter_tmp-0"
        }
      },
      {
        "id": 1752,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476955,
          "NAME": "SecurityDemo\\FileUpload\\FileValidator->checkMagicBytes@iter_tmp-0",
          "ARGUMENT_INDEX": 1,
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 1,
          "LINE_NUMBER": 85,
          "DYNAMIC_TYPE_HINT_FULL_NAME": [
            "array",
            "array-><returnValue>"
          ],
          "CODE": "$SecurityDemo\\FileUpload\\FileValidator->checkMagicBytes@iter_tmp-0"
        }
      },
      {
        "id": 1755,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476958,
          "NAME": "SecurityDemo\\FileUpload\\FileValidator->checkMagicBytes@iter_tmp-0",
          "ARGUMENT_INDEX": 0,
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 1,
          "LINE_NUMBER": 85,
          "DYNAMIC_TYPE_HINT_FULL_NAME": [
            "array",
            "array-><returnValue>"
          ],
          "CODE": "$SecurityDemo\\FileUpload\\FileValidator->checkMagicBytes@iter_tmp-0"
        }
      },
      {
        "id": 1406,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771379,
          "NAME": "key",
          "ARGUMENT_INDEX": 2,
          "SIGNATURE": "<unresolvedSignature>(0)",
          "DISPATCH_TYPE": "DYNAMIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 2,
          "LINE_NUMBER": 85,
          "METHOD_FULL_NAME": "Iterator.key",
          "DYNAMIC_TYPE_HINT_FULL_NAME": [
            "Iterator.key"
          ],
          "CODE": "$SecurityDemo\\FileUpload\\FileValidator->checkMagicBytes@iter_tmp-0->key()"
        }
      },
      {
        "id": 1405,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771378,
          "NAME": "<operator>.assignment",
          "ARGUMENT_INDEX": -1,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 1,
          "LINE_NUMBER": 85,
          "METHOD_FULL_NAME": "<operator>.assignment",
          "CODE": "$type = $SecurityDemo\\FileUpload\\FileValidator->checkMagicBytes@iter_tmp-0->key()"
        }
      },
      {
        "id": 1754,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476957,
          "NAME": "type",
          "ARGUMENT_INDEX": 1,
          "TYPE_FULL_NAME": "Iterator.key-><returnValue>",
          "ORDER": 1,
          "LINE_NUMBER": 85,
          "CODE": "$type"
        }
      },
      {
        "id": 2049,
        "labels": [
          "LOCAL"
        ],
        "properties": {
          "id": 94489280564,
          "NAME": "type",
          "GENERIC_SIGNATURE": "<empty>",
          "TYPE_FULL_NAME": "Iterator.key-><returnValue>",
          "ORDER": 7,
          "LINE_NUMBER": 85,
          "CODE": "$type"
        }
      },
      {
        "id": 1760,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476963,
          "NAME": "type",
          "ARGUMENT_INDEX": 1,
          "TYPE_FULL_NAME": "Iterator.key-><returnValue>",
          "ORDER": 1,
          "LINE_NUMBER": 85,
          "CODE": "$type"
        }
      },
      {
        "id": 1412,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771385,
          "NAME": "<operator>.assignment",
          "ARGUMENT_INDEX": -1,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 1,
          "LINE_NUMBER": 85,
          "METHOD_FULL_NAME": "<operator>.assignment",
          "CODE": "$type = $SecurityDemo\\FileUpload\\FileValidator->checkMagicBytes@iter_tmp-0->key()"
        }
      },
      {
        "id": 1413,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771386,
          "NAME": "key",
          "ARGUMENT_INDEX": 2,
          "SIGNATURE": "<unresolvedSignature>(0)",
          "DISPATCH_TYPE": "DYNAMIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 2,
          "LINE_NUMBER": 85,
          "METHOD_FULL_NAME": "Iterator.key",
          "DYNAMIC_TYPE_HINT_FULL_NAME": [
            "Iterator.key"
          ],
          "CODE": "$SecurityDemo\\FileUpload\\FileValidator->checkMagicBytes@iter_tmp-0->key()"
        }
      },
      {
        "id": 1761,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476964,
          "NAME": "SecurityDemo\\FileUpload\\FileValidator->checkMagicBytes@iter_tmp-0",
          "ARGUMENT_INDEX": 0,
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 1,
          "LINE_NUMBER": 85,
          "DYNAMIC_TYPE_HINT_FULL_NAME": [
            "array",
            "array-><returnValue>"
          ],
          "CODE": "$SecurityDemo\\FileUpload\\FileValidator->checkMagicBytes@iter_tmp-0"
        }
      },
      {
        "id": 1759,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476962,
          "NAME": "SecurityDemo\\FileUpload\\FileValidator->checkMagicBytes@iter_tmp-0",
          "ARGUMENT_INDEX": 0,
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 1,
          "LINE_NUMBER": 85,
          "DYNAMIC_TYPE_HINT_FULL_NAME": [
            "array",
            "array-><returnValue>"
          ],
          "CODE": "$SecurityDemo\\FileUpload\\FileValidator->checkMagicBytes@iter_tmp-0"
        }
      },
      {
        "id": 1411,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771384,
          "NAME": "next",
          "ARGUMENT_INDEX": -1,
          "DISPATCH_TYPE": "DYNAMIC_DISPATCH",
          "SIGNATURE": "void()",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 1,
          "LINE_NUMBER": 85,
          "METHOD_FULL_NAME": "Iterator.next",
          "DYNAMIC_TYPE_HINT_FULL_NAME": [
            "Iterator.next"
          ],
          "CODE": "$SecurityDemo\\FileUpload\\FileValidator->checkMagicBytes@iter_tmp-0->next()"
        }
      },
      {
        "id": 2456,
        "labels": [
          "METHOD"
        ],
        "properties": {
          "id": 111669149757,
          "FULL_NAME": "Iterator.next",
          "NAME": "next",
          "SIGNATURE": "void()",
          "GENERIC_SIGNATURE": "<empty>",
          "ORDER": 0,
          "FILENAME": "<empty>",
          "AST_PARENT_FULL_NAME": "<global>",
          "IS_EXTERNAL": true,
          "AST_PARENT_TYPE": "NAMESPACE_BLOCK",
          "CODE": "<empty>"
        }
      },
      {
        "id": 1332,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771305,
          "NAME": "next",
          "ARGUMENT_INDEX": -1,
          "DISPATCH_TYPE": "DYNAMIC_DISPATCH",
          "SIGNATURE": "void()",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 1,
          "LINE_NUMBER": 131,
          "METHOD_FULL_NAME": "Iterator.next",
          "DYNAMIC_TYPE_HINT_FULL_NAME": [
            "Iterator.next"
          ],
          "CODE": "$SecurityDemo\\FileUpload\\VulnerableFileUploader->processFileName@iter_tmp-1->next()"
        }
      },
      {
        "id": 1702,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476905,
          "NAME": "SecurityDemo\\FileUpload\\VulnerableFileUploader->processFileName@iter_tmp-1",
          "ARGUMENT_INDEX": 0,
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 1,
          "LINE_NUMBER": 131,
          "DYNAMIC_TYPE_HINT_FULL_NAME": [
            "array",
            "array-><returnValue>"
          ],
          "CODE": "$SecurityDemo\\FileUpload\\VulnerableFileUploader->processFileName@iter_tmp-1"
        }
      },
      {
        "id": 2033,
        "labels": [
          "LOCAL"
        ],
        "properties": {
          "id": 94489280548,
          "NAME": "SecurityDemo\\FileUpload\\VulnerableFileUploader->processFileName@iter_tmp-1",
          "GENERIC_SIGNATURE": "<empty>",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 2,
          "LINE_NUMBER": 131,
          "DYNAMIC_TYPE_HINT_FULL_NAME": [
            "array",
            "array-><returnValue>"
          ],
          "CODE": "$SecurityDemo\\FileUpload\\VulnerableFileUploader->processFileName@iter_tmp-1"
        }
      },
      {
        "id": 1704,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476907,
          "NAME": "SecurityDemo\\FileUpload\\VulnerableFileUploader->processFileName@iter_tmp-1",
          "ARGUMENT_INDEX": 0,
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 1,
          "LINE_NUMBER": 131,
          "DYNAMIC_TYPE_HINT_FULL_NAME": [
            "array",
            "array-><returnValue>"
          ],
          "CODE": "$SecurityDemo\\FileUpload\\VulnerableFileUploader->processFileName@iter_tmp-1"
        }
      },
      {
        "id": 1334,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771307,
          "NAME": "current",
          "ARGUMENT_INDEX": 2,
          "SIGNATURE": "<unresolvedSignature>(0)",
          "DISPATCH_TYPE": "DYNAMIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 2,
          "LINE_NUMBER": 131,
          "METHOD_FULL_NAME": "Iterator.current",
          "DYNAMIC_TYPE_HINT_FULL_NAME": [
            "Iterator.current"
          ],
          "CODE": "$SecurityDemo\\FileUpload\\VulnerableFileUploader->processFileName@iter_tmp-1->current()"
        }
      },
      {
        "id": 1333,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771306,
          "NAME": "<operator>.assignment",
          "ARGUMENT_INDEX": -1,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 2,
          "LINE_NUMBER": 131,
          "METHOD_FULL_NAME": "<operator>.assignment",
          "CODE": "$blocked = $SecurityDemo\\FileUpload\\VulnerableFileUploader->processFileName@iter_tmp-1->current()"
        }
      },
      {
        "id": 1703,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476906,
          "NAME": "blocked",
          "ARGUMENT_INDEX": 1,
          "TYPE_FULL_NAME": "Iterator.current-><returnValue>",
          "ORDER": 1,
          "LINE_NUMBER": 131,
          "CODE": "$blocked"
        }
      },
      {
        "id": 2035,
        "labels": [
          "LOCAL"
        ],
        "properties": {
          "id": 94489280550,
          "NAME": "blocked",
          "GENERIC_SIGNATURE": "<empty>",
          "TYPE_FULL_NAME": "Iterator.current-><returnValue>",
          "ORDER": 4,
          "LINE_NUMBER": 131,
          "CODE": "$blocked"
        }
      },
      {
        "id": 1701,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476904,
          "NAME": "blocked",
          "ARGUMENT_INDEX": 1,
          "TYPE_FULL_NAME": "Iterator.current-><returnValue>",
          "ORDER": 1,
          "LINE_NUMBER": 131,
          "CODE": "$blocked"
        }
      },
      {
        "id": 1331,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771304,
          "NAME": "is_null",
          "ARGUMENT_INDEX": 1,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "bool",
          "ORDER": 1,
          "LINE_NUMBER": 131,
          "METHOD_FULL_NAME": "is_null",
          "DYNAMIC_TYPE_HINT_FULL_NAME": [
            "is_null"
          ],
          "CODE": "is_null($blocked)"
        }
      },
      {
        "id": 2455,
        "labels": [
          "METHOD"
        ],
        "properties": {
          "id": 111669149756,
          "FULL_NAME": "is_null",
          "NAME": "is_null",
          "GENERIC_SIGNATURE": "<empty>",
          "ORDER": 0,
          "FILENAME": "<empty>",
          "AST_PARENT_FULL_NAME": "<global>",
          "IS_EXTERNAL": true,
          "AST_PARENT_TYPE": "NAMESPACE_BLOCK",
          "CODE": "<empty>"
        }
      },
      {
        "id": 1438,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771411,
          "NAME": "is_null",
          "ARGUMENT_INDEX": 1,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "bool",
          "ORDER": 1,
          "LINE_NUMBER": 112,
          "METHOD_FULL_NAME": "is_null",
          "DYNAMIC_TYPE_HINT_FULL_NAME": [
            "is_null"
          ],
          "CODE": "is_null($pattern)"
        }
      },
      {
        "id": 1784,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476987,
          "NAME": "pattern",
          "ARGUMENT_INDEX": 1,
          "TYPE_FULL_NAME": "Iterator.current-><returnValue>",
          "ORDER": 1,
          "LINE_NUMBER": 112,
          "CODE": "$pattern"
        }
      },
      {
        "id": 2052,
        "labels": [
          "LOCAL"
        ],
        "properties": {
          "id": 94489280567,
          "NAME": "pattern",
          "GENERIC_SIGNATURE": "<empty>",
          "TYPE_FULL_NAME": "Iterator.current-><returnValue>",
          "ORDER": 3,
          "LINE_NUMBER": 112,
          "CODE": "$pattern"
        }
      },
      {
        "id": 1789,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476992,
          "NAME": "pattern",
          "ARGUMENT_INDEX": 2,
          "TYPE_FULL_NAME": "Iterator.current-><returnValue>",
          "ORDER": 2,
          "LINE_NUMBER": 114,
          "CODE": "$pattern"
        }
      },
      {
        "id": 1443,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771416,
          "NAME": "strpos",
          "ARGUMENT_INDEX": 1,
          "SIGNATURE": "<unresolvedSignature>(2)",
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 1,
          "LINE_NUMBER": 114,
          "METHOD_FULL_NAME": "strpos",
          "DYNAMIC_TYPE_HINT_FULL_NAME": [
            "strpos"
          ],
          "CODE": "strpos($content,$pattern)"
        }
      },
      {
        "id": 1442,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771415,
          "NAME": "<operator>.notIdentical",
          "ARGUMENT_INDEX": -1,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 1,
          "LINE_NUMBER": 114,
          "METHOD_FULL_NAME": "<operator>.notIdentical",
          "CODE": "strpos($content,$pattern) !== false"
        }
      },
      {
        "id": 2449,
        "labels": [
          "METHOD"
        ],
        "properties": {
          "id": 111669149750,
          "FULL_NAME": "<operator>.notIdentical",
          "NAME": "<operator>.notIdentical",
          "GENERIC_SIGNATURE": "<empty>",
          "ORDER": 0,
          "FILENAME": "<empty>",
          "AST_PARENT_FULL_NAME": "<global>",
          "IS_EXTERNAL": true,
          "AST_PARENT_TYPE": "NAMESPACE_BLOCK",
          "CODE": "<empty>"
        }
      },
      {
        "id": 345,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771277,
          "NAME": "<operator>.notIdentical",
          "ARGUMENT_INDEX": -1,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 1,
          "LINE_NUMBER": 104,
          "METHOD_FULL_NAME": "<operator>.notIdentical",
          "CODE": "$fileData["error"] !== UPLOAD_ERR_OK"
        }
      },
      {
        "id": 346,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771278,
          "NAME": "<operator>.indexAccess",
          "ARGUMENT_INDEX": 1,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 1,
          "LINE_NUMBER": 104,
          "METHOD_FULL_NAME": "<operator>.indexAccess",
          "CODE": "$fileData["error"]"
        }
      },
      {
        "id": 1680,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476883,
          "NAME": "fileData",
          "ARGUMENT_INDEX": 1,
          "TYPE_FULL_NAME": "array",
          "ORDER": 1,
          "LINE_NUMBER": 104,
          "CODE": "$fileData"
        }
      },
      {
        "id": 2080,
        "labels": [
          "METHOD_PARAMETER_IN"
        ],
        "properties": {
          "IS_VARIADIC": false,
          "id": 115964117005,
          "INDEX": 1,
          "NAME": "fileData",
          "TYPE_FULL_NAME": "array",
          "ORDER": 1,
          "EVALUATION_STRATEGY": "BY_VALUE",
          "LINE_NUMBER": 101,
          "CODE": "$fileData"
        }
      },
      {
        "id": 1682,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476885,
          "NAME": "fileData",
          "ARGUMENT_INDEX": 1,
          "TYPE_FULL_NAME": "array",
          "ORDER": 1,
          "LINE_NUMBER": 109,
          "CODE": "$fileData"
        }
      },
      {
        "id": 349,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771281,
          "NAME": "<operator>.indexAccess",
          "ARGUMENT_INDEX": 1,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 1,
          "LINE_NUMBER": 109,
          "METHOD_FULL_NAME": "<operator>.indexAccess",
          "CODE": "$fileData["size"]"
        }
      },
      {
        "id": 348,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771280,
          "NAME": "<operator>.greaterThan",
          "ARGUMENT_INDEX": -1,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 1,
          "LINE_NUMBER": 109,
          "METHOD_FULL_NAME": "<operator>.greaterThan",
          "CODE": "$fileData["size"] > $this->maxFileSize"
        }
      },
      {
        "id": 350,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771282,
          "NAME": "<operator>.fieldAccess",
          "ARGUMENT_INDEX": 2,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 2,
          "LINE_NUMBER": 109,
          "METHOD_FULL_NAME": "<operator>.fieldAccess",
          "CODE": "$this->maxFileSize"
        }
      },
      {
        "id": 1683,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476886,
          "NAME": "this",
          "ARGUMENT_INDEX": 1,
          "TYPE_FULL_NAME": "SecurityDemo\\FileUpload\\VulnerableFileUploader",
          "ORDER": 1,
          "LINE_NUMBER": 109,
          "CODE": "$this"
        }
      },
      {
        "id": 2079,
        "labels": [
          "METHOD_PARAMETER_IN"
        ],
        "properties": {
          "IS_VARIADIC": false,
          "id": 115964117004,
          "INDEX": 0,
          "NAME": "this",
          "TYPE_FULL_NAME": "SecurityDemo\\FileUpload\\VulnerableFileUploader",
          "ORDER": 0,
          "EVALUATION_STRATEGY": "BY_SHARING",
          "LINE_NUMBER": 101,
          "DYNAMIC_TYPE_HINT_FULL_NAME": [
            "SecurityDemo\\FileUpload\\VulnerableFileUploader"
          ],
          "CODE": "this"
        }
      },
      {
        "id": 2405,
        "labels": [
          "METHOD"
        ],
        "properties": {
          "id": 111669149706,
          "NAME": "validateFileUpload",
          "FULL_NAME": "SecurityDemo\\FileUpload\\VulnerableFileUploader->validateFileUpload",
          "SIGNATURE": "<unresolvedSignature>(1)",
          "GENERIC_SIGNATURE": "<empty>",
          "ORDER": 8,
          "FILENAME": "FileUploadVulnerability.php",
          "LINE_NUMBER": 101,
          "IS_EXTERNAL": false,
          "AST_PARENT_FULL_NAME": "SecurityDemo\\FileUpload\\VulnerableFileUploader",
          "AST_PARENT_TYPE": "TYPE_DECL",
          "CODE": "PRIVATE function validateFileUpload(this,$fileData)"
        }
      },
      {
        "id": 305,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771237,
          "NAME": "validateFileUpload",
          "ARGUMENT_INDEX": 1,
          "SIGNATURE": "<unresolvedSignature>(1)",
          "DISPATCH_TYPE": "DYNAMIC_DISPATCH",
          "TYPE_FULL_NAME": "bool",
          "ORDER": 1,
          "LINE_NUMBER": 71,
          "METHOD_FULL_NAME": "SecurityDemo\\FileUpload\\VulnerableFileUploader->validateFileUpload",
          "CODE": "$this->validateFileUpload($fileData)"
        }
      },
      {
        "id": 1649,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476852,
          "NAME": "fileData",
          "ARGUMENT_INDEX": 1,
          "TYPE_FULL_NAME": "array",
          "ORDER": 2,
          "LINE_NUMBER": 71,
          "CODE": "$fileData"
        }
      },
      {
        "id": 2077,
        "labels": [
          "METHOD_PARAMETER_IN"
        ],
        "properties": {
          "IS_VARIADIC": false,
          "id": 115964117002,
          "INDEX": 1,
          "NAME": "fileData",
          "TYPE_FULL_NAME": "array",
          "ORDER": 1,
          "EVALUATION_STRATEGY": "BY_VALUE",
          "LINE_NUMBER": 67,
          "CODE": "$fileData"
        }
      },
      {
        "id": 1661,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476864,
          "NAME": "fileData",
          "ARGUMENT_INDEX": 1,
          "TYPE_FULL_NAME": "array",
          "ORDER": 1,
          "LINE_NUMBER": 82,
          "CODE": "$fileData"
        }
      },
      {
        "id": 319,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771251,
          "NAME": "<operator>.indexAccess",
          "ARGUMENT_INDEX": 1,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 1,
          "LINE_NUMBER": 82,
          "METHOD_FULL_NAME": "<operator>.indexAccess",
          "CODE": "$fileData["tmp_name"]"
        }
      },
      {
        "id": 318,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771250,
          "NAME": "move_uploaded_file",
          "ARGUMENT_INDEX": -1,
          "SIGNATURE": "<unresolvedSignature>(2)",
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 1,
          "LINE_NUMBER": 82,
          "METHOD_FULL_NAME": "move_uploaded_file",
          "DYNAMIC_TYPE_HINT_FULL_NAME": [
            "move_uploaded_file"
          ],
          "CODE": "move_uploaded_file($fileData["tmp_name"],$targetPath)"
        }
      },
      {
        "id": 1662,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476865,
          "NAME": "targetPath",
          "ARGUMENT_INDEX": 2,
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 2,
          "LINE_NUMBER": 82,
          "CODE": "$targetPath"
        }
      },
      {
        "id": 318,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771250,
          "NAME": "move_uploaded_file",
          "ARGUMENT_INDEX": -1,
          "SIGNATURE": "<unresolvedSignature>(2)",
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 1,
          "LINE_NUMBER": 82,
          "METHOD_FULL_NAME": "move_uploaded_file",
          "DYNAMIC_TYPE_HINT_FULL_NAME": [
            "move_uploaded_file"
          ],
          "CODE": "move_uploaded_file($fileData["tmp_name"],$targetPath)"
        }
      }
    ]
  },
  {
    "nodes": [
      {
        "id": 180,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771112,
          "NAME": "<operator>.indexAccess",
          "ARGUMENT_INDEX": 1,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 1,
          "LINE_NUMBER": 59,
          "METHOD_FULL_NAME": "<operator>.indexAccess",
          "CODE": "$_GET["filename"]"
        }
      },
      {
        "id": 1559,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476762,
          "NAME": "_GET",
          "ARGUMENT_INDEX": 1,
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 1,
          "LINE_NUMBER": 59,
          "CODE": "$_GET"
        }
      },
      {
        "id": 2007,
        "labels": [
          "LOCAL"
        ],
        "properties": {
          "id": 94489280522,
          "NAME": "_GET",
          "GENERIC_SIGNATURE": "<empty>",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 4,
          "LINE_NUMBER": 59,
          "CODE": "$_GET"
        }
      },
      {
        "id": 1562,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476765,
          "NAME": "_GET",
          "ARGUMENT_INDEX": 1,
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 1,
          "LINE_NUMBER": 62,
          "CODE": "$_GET"
        }
      },
      {
        "id": 185,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771117,
          "NAME": "<operator>.indexAccess",
          "ARGUMENT_INDEX": 1,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 1,
          "LINE_NUMBER": 62,
          "METHOD_FULL_NAME": "<operator>.indexAccess",
          "CODE": "$_GET["subdir"]"
        }
      },
      {
        "id": 184,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771116,
          "NAME": "<operator>.coalesce",
          "ARGUMENT_INDEX": 2,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 2,
          "LINE_NUMBER": 62,
          "METHOD_FULL_NAME": "<operator>.coalesce",
          "CODE": "$_GET["subdir"] ?? """
        }
      },
      {
        "id": 182,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771114,
          "NAME": "<operator>.coalesce",
          "ARGUMENT_INDEX": 2,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 2,
          "LINE_NUMBER": 62,
          "METHOD_FULL_NAME": "<operator>.coalesce",
          "CODE": "$_POST["subdir"] ?? $_GET["subdir"] ?? """
        }
      },
      {
        "id": 183,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771115,
          "NAME": "<operator>.indexAccess",
          "ARGUMENT_INDEX": 1,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 1,
          "LINE_NUMBER": 62,
          "METHOD_FULL_NAME": "<operator>.indexAccess",
          "CODE": "$_POST["subdir"]"
        }
      },
      {
        "id": 1561,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476764,
          "NAME": "_POST",
          "ARGUMENT_INDEX": 1,
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 1,
          "LINE_NUMBER": 62,
          "CODE": "$_POST"
        }
      },
      {
        "id": 2008,
        "labels": [
          "LOCAL"
        ],
        "properties": {
          "id": 94489280523,
          "NAME": "_POST",
          "GENERIC_SIGNATURE": "<empty>",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 5,
          "LINE_NUMBER": 59,
          "CODE": "$_POST"
        }
      },
      {
        "id": 1558,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476761,
          "NAME": "_POST",
          "ARGUMENT_INDEX": 1,
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 1,
          "LINE_NUMBER": 59,
          "CODE": "$_POST"
        }
      },
      {
        "id": 178,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771110,
          "NAME": "<operator>.indexAccess",
          "ARGUMENT_INDEX": 1,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 1,
          "LINE_NUMBER": 59,
          "METHOD_FULL_NAME": "<operator>.indexAccess",
          "CODE": "$_POST["filename"]"
        }
      },
      {
        "id": 177,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771109,
          "NAME": "<operator>.coalesce",
          "ARGUMENT_INDEX": 2,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 2,
          "LINE_NUMBER": 59,
          "METHOD_FULL_NAME": "<operator>.coalesce",
          "CODE": "$_POST["filename"] ?? $_GET["filename"] ?? """
        }
      },
      {
        "id": 179,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771111,
          "NAME": "<operator>.coalesce",
          "ARGUMENT_INDEX": 2,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 2,
          "LINE_NUMBER": 59,
          "METHOD_FULL_NAME": "<operator>.coalesce",
          "CODE": "$_GET["filename"] ?? """
        }
      },
      {
        "id": 2419,
        "labels": [
          "METHOD"
        ],
        "properties": {
          "id": 111669149720,
          "FULL_NAME": "<operator>.coalesce",
          "NAME": "<operator>.coalesce",
          "GENERIC_SIGNATURE": "<empty>",
          "ORDER": 0,
          "FILENAME": "<empty>",
          "AST_PARENT_FULL_NAME": "<global>",
          "IS_EXTERNAL": true,
          "AST_PARENT_TYPE": "NAMESPACE_BLOCK",
          "CODE": "<empty>"
        }
      },
      {
        "id": 153,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771085,
          "NAME": "<operator>.coalesce",
          "ARGUMENT_INDEX": 2,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 2,
          "LINE_NUMBER": 38,
          "METHOD_FULL_NAME": "<operator>.coalesce",
          "CODE": "$_POST["action"] ?? $_GET["action"] ?? "upload""
        }
      },
      {
        "id": 154,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771086,
          "NAME": "<operator>.indexAccess",
          "ARGUMENT_INDEX": 1,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 1,
          "LINE_NUMBER": 38,
          "METHOD_FULL_NAME": "<operator>.indexAccess",
          "CODE": "$_POST["action"]"
        }
      },
      {
        "id": 1542,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476745,
          "NAME": "_POST",
          "ARGUMENT_INDEX": 1,
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 1,
          "LINE_NUMBER": 38,
          "CODE": "$_POST"
        }
      },
      {
        "id": 2397,
        "labels": [
          "METHOD"
        ],
        "properties": {
          "id": 111669149698,
          "NAME": "handleRequest",
          "FULL_NAME": "SecurityDemo\\FileUpload\\FileUploadController->handleRequest",
          "SIGNATURE": "<unresolvedSignature>(0)",
          "GENERIC_SIGNATURE": "<empty>",
          "ORDER": 4,
          "FILENAME": "FileUploadHandler.php",
          "LINE_NUMBER": 35,
          "IS_EXTERNAL": false,
          "AST_PARENT_FULL_NAME": "SecurityDemo\\FileUpload\\FileUploadController",
          "AST_PARENT_TYPE": "TYPE_DECL",
          "CODE": "PUBLIC function handleRequest(this)"
        }
      },
      {
        "id": 1546,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476749,
          "NAME": "action",
          "ARGUMENT_INDEX": 1,
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 1,
          "LINE_NUMBER": 42,
          "CODE": "$action"
        }
      },
      {
        "id": 2003,
        "labels": [
          "LOCAL"
        ],
        "properties": {
          "id": 94489280518,
          "NAME": "action",
          "GENERIC_SIGNATURE": "<empty>",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 4,
          "LINE_NUMBER": 38,
          "CODE": "$action"
        }
      },
      {
        "id": 1544,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476747,
          "NAME": "action",
          "ARGUMENT_INDEX": 1,
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 1,
          "LINE_NUMBER": 40,
          "CODE": "$action"
        }
      },
      {
        "id": 157,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771089,
          "NAME": "<operator>.identical",
          "ARGUMENT_INDEX": -1,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 1,
          "LINE_NUMBER": 40,
          "METHOD_FULL_NAME": "<operator>.identical",
          "CODE": "$action === "upload""
        }
      },
      {
        "id": 2421,
        "labels": [
          "METHOD"
        ],
        "properties": {
          "id": 111669149722,
          "FULL_NAME": "<operator>.identical",
          "NAME": "<operator>.identical",
          "GENERIC_SIGNATURE": "<empty>",
          "ORDER": 0,
          "FILENAME": "<empty>",
          "AST_PARENT_FULL_NAME": "<global>",
          "IS_EXTERNAL": true,
          "AST_PARENT_TYPE": "NAMESPACE_BLOCK",
          "CODE": "<empty>"
        }
      },
      {
        "id": 259,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771191,
          "NAME": "<operator>.identical",
          "ARGUMENT_INDEX": 1,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 1,
          "LINE_NUMBER": 115,
          "METHOD_FULL_NAME": "<operator>.identical",
          "CODE": "$_SERVER["REQUEST_METHOD"] === "POST""
        }
      },
      {
        "id": 258,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771190,
          "NAME": "<operator>.logicalOr",
          "ARGUMENT_INDEX": -1,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 1,
          "LINE_NUMBER": 115,
          "METHOD_FULL_NAME": "<operator>.logicalOr",
          "CODE": "$_SERVER["REQUEST_METHOD"] === "POST" || !empty($_GET["action"])"
        }
      },
      {
        "id": 261,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771193,
          "NAME": "<operator>.logicalNot",
          "ARGUMENT_INDEX": 2,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 2,
          "LINE_NUMBER": 115,
          "METHOD_FULL_NAME": "<operator>.logicalNot",
          "CODE": "!empty($_GET["action"])"
        }
      },
      {
        "id": 262,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771194,
          "NAME": "empty",
          "ARGUMENT_INDEX": 1,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "bool",
          "ORDER": 1,
          "LINE_NUMBER": 115,
          "METHOD_FULL_NAME": "empty",
          "DYNAMIC_TYPE_HINT_FULL_NAME": [
            "empty"
          ],
          "CODE": "empty($_GET["action"])"
        }
      },
      {
        "id": 263,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771195,
          "NAME": "<operator>.indexAccess",
          "ARGUMENT_INDEX": 1,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 1,
          "LINE_NUMBER": 115,
          "METHOD_FULL_NAME": "<operator>.indexAccess",
          "CODE": "$_GET["action"]"
        }
      },
      {
        "id": 1621,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476824,
          "NAME": "_GET",
          "ARGUMENT_INDEX": 1,
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 1,
          "LINE_NUMBER": 115,
          "CODE": "$_GET"
        }
      },
      {
        "id": 2395,
        "labels": [
          "METHOD"
        ],
        "properties": {
          "id": 111669149696,
          "NAME": "<global>",
          "FULL_NAME": "FileUploadHandler.php:<global>",
          "SIGNATURE": "<unresolvedSignature>(0)",
          "GENERIC_SIGNATURE": "<empty>",
          "ORDER": 1,
          "FILENAME": "FileUploadHandler.php",
          "LINE_NUMBER": 9,
          "IS_EXTERNAL": false,
          "AST_PARENT_FULL_NAME": "FileUploadHandler.php:<global>",
          "AST_PARENT_TYPE": "TYPE_DECL",
          "CODE": "VIRTUAL PUBLIC STATIC function <global>()"
        }
      },
      {
        "id": 1628,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476831,
          "NAME": "controller",
          "ARGUMENT_INDEX": 0,
          "TYPE_FULL_NAME": "SecurityDemo\\FileUpload\\FileUploadController",
          "ORDER": 1,
          "LINE_NUMBER": 118,
          "CODE": "$controller"
        }
      },
      {
        "id": 270,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771202,
          "NAME": "sendResponse",
          "ARGUMENT_INDEX": -1,
          "SIGNATURE": "<unresolvedSignature>(1)",
          "DISPATCH_TYPE": "DYNAMIC_DISPATCH",
          "TYPE_FULL_NAME": "SecurityDemo\\FileUpload\\FileUploadController->sendResponse-><returnValue>",
          "ORDER": 3,
          "LINE_NUMBER": 118,
          "METHOD_FULL_NAME": "SecurityDemo\\FileUpload\\FileUploadController->sendResponse",
          "CODE": "$controller->sendResponse($result)"
        }
      },
      {
        "id": 1629,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476832,
          "NAME": "result",
          "ARGUMENT_INDEX": 1,
          "TYPE_FULL_NAME": "array",
          "ORDER": 2,
          "LINE_NUMBER": 118,
          "CODE": "$result"
        }
      },
      {
        "id": 1626,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476829,
          "NAME": "result",
          "ARGUMENT_INDEX": 1,
          "TYPE_FULL_NAME": "array",
          "ORDER": 1,
          "LINE_NUMBER": 117,
          "CODE": "$result"
        }
      },
      {
        "id": 268,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771200,
          "NAME": "<operator>.assignment",
          "ARGUMENT_INDEX": -1,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 2,
          "LINE_NUMBER": 117,
          "METHOD_FULL_NAME": "<operator>.assignment",
          "CODE": "$result = $controller->handleRequest()"
        }
      },
      {
        "id": 269,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771201,
          "NAME": "handleRequest",
          "ARGUMENT_INDEX": 2,
          "SIGNATURE": "<unresolvedSignature>(0)",
          "DISPATCH_TYPE": "DYNAMIC_DISPATCH",
          "TYPE_FULL_NAME": "SecurityDemo\\FileUpload\\FileUploadController->handleRequest-><returnValue>",
          "ORDER": 2,
          "LINE_NUMBER": 117,
          "METHOD_FULL_NAME": "SecurityDemo\\FileUpload\\FileUploadController->handleRequest",
          "CODE": "$controller->handleRequest()"
        }
      },
      {
        "id": 1627,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476830,
          "NAME": "controller",
          "ARGUMENT_INDEX": 0,
          "TYPE_FULL_NAME": "SecurityDemo\\FileUpload\\FileUploadController",
          "ORDER": 1,
          "LINE_NUMBER": 117,
          "CODE": "$controller"
        }
      },
      {
        "id": 1622,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476825,
          "NAME": "controller",
          "ARGUMENT_INDEX": 1,
          "TYPE_FULL_NAME": "SecurityDemo\\FileUpload\\FileUploadController",
          "ORDER": 1,
          "LINE_NUMBER": 116,
          "CODE": "$controller"
        }
      },
      {
        "id": 264,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771196,
          "NAME": "<operator>.assignment",
          "ARGUMENT_INDEX": -1,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 1,
          "LINE_NUMBER": 116,
          "METHOD_FULL_NAME": "<operator>.assignment",
          "CODE": "$controller = "
        }
      },
      {
        "id": 2416,
        "labels": [
          "METHOD"
        ],
        "properties": {
          "id": 111669149717,
          "FULL_NAME": "<operator>.assignment",
          "NAME": "<operator>.assignment",
          "GENERIC_SIGNATURE": "<empty>",
          "ORDER": 0,
          "FILENAME": "<empty>",
          "AST_PARENT_FULL_NAME": "<global>",
          "IS_EXTERNAL": true,
          "AST_PARENT_TYPE": "NAMESPACE_BLOCK",
          "CODE": "<empty>"
        }
      },
      {
        "id": 144,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771076,
          "NAME": "<operator>.assignment",
          "ARGUMENT_INDEX": -1,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "SecurityDemo\\FileUpload\\VulnerableFileUploader",
          "ORDER": 1,
          "LINE_NUMBER": 27,
          "METHOD_FULL_NAME": "<operator>.assignment",
          "CODE": "$SecurityDemo\\FileUpload\\FileUploadController->__construct@tmp-0 = SecurityDemo\\FileUpload\\VulnerableFileUploader.<alloc>()"
        }
      },
      {
        "id": 145,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771077,
          "NAME": "<operator>.alloc",
          "ARGUMENT_INDEX": 2,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "SecurityDemo\\FileUpload\\VulnerableFileUploader",
          "ORDER": 2,
          "LINE_NUMBER": 27,
          "METHOD_FULL_NAME": "<operator>.alloc",
          "CODE": "SecurityDemo\\FileUpload\\VulnerableFileUploader.<alloc>()"
        }
      },
      {
        "id": 2396,
        "labels": [
          "METHOD"
        ],
        "properties": {
          "id": 111669149697,
          "NAME": "__construct",
          "FULL_NAME": "SecurityDemo\\FileUpload\\FileUploadController->__construct",
          "SIGNATURE": "<unresolvedSignature>(0)",
          "GENERIC_SIGNATURE": "<empty>",
          "ORDER": 3,
          "FILENAME": "FileUploadHandler.php",
          "LINE_NUMBER": 25,
          "IS_EXTERNAL": false,
          "AST_PARENT_FULL_NAME": "SecurityDemo\\FileUpload\\FileUploadController",
          "AST_PARENT_TYPE": "TYPE_DECL",
          "CODE": "PUBLIC function __construct(this)"
        }
      },
      {
        "id": 150,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771082,
          "NAME": "<operator>.alloc",
          "ARGUMENT_INDEX": 2,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "SecurityDemo\\FileUpload\\FileValidator",
          "ORDER": 2,
          "LINE_NUMBER": 28,
          "METHOD_FULL_NAME": "<operator>.alloc",
          "CODE": "SecurityDemo\\FileUpload\\FileValidator.<alloc>()"
        }
      },
      {
        "id": 149,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771081,
          "NAME": "<operator>.assignment",
          "ARGUMENT_INDEX": -1,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "SecurityDemo\\FileUpload\\FileValidator",
          "ORDER": 1,
          "LINE_NUMBER": 28,
          "METHOD_FULL_NAME": "<operator>.assignment",
          "CODE": "$SecurityDemo\\FileUpload\\FileUploadController->__construct@tmp-1 = SecurityDemo\\FileUpload\\FileValidator.<alloc>()"
        }
      },
      {
        "id": 1538,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476741,
          "NAME": "SecurityDemo\\FileUpload\\FileUploadController->__construct@tmp-1",
          "ARGUMENT_INDEX": 1,
          "TYPE_FULL_NAME": "SecurityDemo\\FileUpload\\FileValidator",
          "ORDER": 1,
          "LINE_NUMBER": 28,
          "CODE": "$SecurityDemo\\FileUpload\\FileUploadController->__construct@tmp-1"
        }
      },
      {
        "id": 1999,
        "labels": [
          "LOCAL"
        ],
        "properties": {
          "id": 94489280514,
          "NAME": "SecurityDemo\\FileUpload\\FileUploadController->__construct@tmp-1",
          "GENERIC_SIGNATURE": "<empty>",
          "TYPE_FULL_NAME": "SecurityDemo\\FileUpload\\FileValidator",
          "ORDER": 2,
          "LINE_NUMBER": 28,
          "CODE": "$SecurityDemo\\FileUpload\\FileUploadController->__construct@tmp-1"
        }
      },
      {
        "id": 1539,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476742,
          "NAME": "SecurityDemo\\FileUpload\\FileUploadController->__construct@tmp-1",
          "ARGUMENT_INDEX": 0,
          "TYPE_FULL_NAME": "SecurityDemo\\FileUpload\\FileValidator",
          "ORDER": 1,
          "LINE_NUMBER": 28,
          "CODE": "$SecurityDemo\\FileUpload\\FileUploadController->__construct@tmp-1"
        }
      },
      {
        "id": 151,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771083,
          "NAME": "__construct",
          "ARGUMENT_INDEX": -1,
          "SIGNATURE": "<unresolvedSignature>(0)",
          "DISPATCH_TYPE": "DYNAMIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 2,
          "LINE_NUMBER": 28,
          "METHOD_FULL_NAME": "SecurityDemo\\FileUpload\\FileValidator->__construct",
          "DYNAMIC_TYPE_HINT_FULL_NAME": [
            "SecurityDemo\\FileUpload\\FileValidator->__construct"
          ],
          "CODE": "SecurityDemo\\FileUpload\\FileValidator->__construct()"
        }
      },
      {
        "id": 2408,
        "labels": [
          "METHOD"
        ],
        "properties": {
          "id": 111669149709,
          "NAME": "__construct",
          "FULL_NAME": "SecurityDemo\\FileUpload\\FileValidator->__construct",
          "SIGNATURE": "<unresolvedSignature>(0)",
          "GENERIC_SIGNATURE": "<empty>",
          "ORDER": 3,
          "FILENAME": "FileValidator.php",
          "LINE_NUMBER": 26,
          "IS_EXTERNAL": false,
          "AST_PARENT_FULL_NAME": "SecurityDemo\\FileUpload\\FileValidator",
          "AST_PARENT_TYPE": "TYPE_DECL",
          "CODE": "PUBLIC function __construct(this)"
        }
      },
      {
        "id": 1739,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476942,
          "NAME": "SecurityDemo\\FileUpload\\FileValidator->__construct@tmp-4",
          "ARGUMENT_INDEX": 1,
          "TYPE_FULL_NAME": "array",
          "ORDER": 1,
          "LINE_NUMBER": 42,
          "DYNAMIC_TYPE_HINT_FULL_NAME": [
            "array",
            "array-><returnValue>"
          ],
          "CODE": "$SecurityDemo\\FileUpload\\FileValidator->__construct@tmp-4"
        }
      },
      {
        "id": 2042,
        "labels": [
          "LOCAL"
        ],
        "properties": {
          "id": 94489280557,
          "NAME": "SecurityDemo\\FileUpload\\FileValidator->__construct@tmp-4",
          "GENERIC_SIGNATURE": "<empty>",
          "TYPE_FULL_NAME": "array",
          "ORDER": 5,
          "LINE_NUMBER": 36,
          "DYNAMIC_TYPE_HINT_FULL_NAME": [
            "array",
            "array-><returnValue>"
          ],
          "CODE": "$SecurityDemo\\FileUpload\\FileValidator->__construct@tmp-4"
        }
      },
      {
        "id": 1733,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476936,
          "NAME": "SecurityDemo\\FileUpload\\FileValidator->__construct@tmp-4",
          "ARGUMENT_INDEX": 1,
          "TYPE_FULL_NAME": "array",
          "ORDER": 1,
          "LINE_NUMBER": 36,
          "DYNAMIC_TYPE_HINT_FULL_NAME": [
            "array",
            "array-><returnValue>"
          ],
          "CODE": "$SecurityDemo\\FileUpload\\FileValidator->__construct@tmp-4"
        }
      },
      {
        "id": 1377,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771350,
          "NAME": "<operator>.assignment",
          "ARGUMENT_INDEX": -1,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 1,
          "LINE_NUMBER": 36,
          "METHOD_FULL_NAME": "<operator>.assignment",
          "CODE": "$SecurityDemo\\FileUpload\\FileValidator->__construct@tmp-4 = array()"
        }
      },
      {
        "id": 1378,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771351,
          "NAME": "array",
          "ARGUMENT_INDEX": 2,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "SIGNATURE": "array()",
          "TYPE_FULL_NAME": "array",
          "ORDER": 2,
          "LINE_NUMBER": 36,
          "METHOD_FULL_NAME": "array",
          "DYNAMIC_TYPE_HINT_FULL_NAME": [
            "array"
          ],
          "CODE": "array()"
        }
      },
      {
        "id": 2424,
        "labels": [
          "METHOD"
        ],
        "properties": {
          "id": 111669149725,
          "FULL_NAME": "array",
          "NAME": "array",
          "SIGNATURE": "array()",
          "GENERIC_SIGNATURE": "<empty>",
          "ORDER": 0,
          "FILENAME": "<empty>",
          "AST_PARENT_FULL_NAME": "<global>",
          "IS_EXTERNAL": true,
          "AST_PARENT_TYPE": "NAMESPACE_BLOCK",
          "CODE": "<empty>"
        }
      },
      {
        "id": 249,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771181,
          "NAME": "array",
          "ARGUMENT_INDEX": 2,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "SIGNATURE": "array()",
          "TYPE_FULL_NAME": "array",
          "ORDER": 2,
          "LINE_NUMBER": 101,
          "METHOD_FULL_NAME": "array",
          "DYNAMIC_TYPE_HINT_FULL_NAME": [
            "array"
          ],
          "CODE": "array()"
        }
      },
      {
        "id": 248,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771180,
          "NAME": "<operator>.assignment",
          "ARGUMENT_INDEX": -1,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 1,
          "LINE_NUMBER": 101,
          "METHOD_FULL_NAME": "<operator>.assignment",
          "CODE": "$SecurityDemo\\FileUpload\\FileUploadController->processBatchUpload@tmp-3 = array()"
        }
      },
      {
        "id": 1613,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476816,
          "NAME": "SecurityDemo\\FileUpload\\FileUploadController->processBatchUpload@tmp-3",
          "ARGUMENT_INDEX": 1,
          "TYPE_FULL_NAME": "array",
          "ORDER": 1,
          "LINE_NUMBER": 101,
          "DYNAMIC_TYPE_HINT_FULL_NAME": [
            "array",
            "array-><returnValue>"
          ],
          "CODE": "$SecurityDemo\\FileUpload\\FileUploadController->processBatchUpload@tmp-3"
        }
      },
      {
        "id": 2014,
        "labels": [
          "LOCAL"
        ],
        "properties": {
          "id": 94489280529,
          "NAME": "SecurityDemo\\FileUpload\\FileUploadController->processBatchUpload@tmp-3",
          "GENERIC_SIGNATURE": "<empty>",
          "TYPE_FULL_NAME": "array",
          "ORDER": 4,
          "LINE_NUMBER": 101,
          "DYNAMIC_TYPE_HINT_FULL_NAME": [
            "array",
            "array-><returnValue>"
          ],
          "CODE": "$SecurityDemo\\FileUpload\\FileUploadController->processBatchUpload@tmp-3"
        }
      },
      {
        "id": 1614,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476817,
          "NAME": "SecurityDemo\\FileUpload\\FileUploadController->processBatchUpload@tmp-3",
          "ARGUMENT_INDEX": 1,
          "TYPE_FULL_NAME": "array",
          "ORDER": 1,
          "LINE_NUMBER": 101,
          "DYNAMIC_TYPE_HINT_FULL_NAME": [
            "array",
            "array-><returnValue>"
          ],
          "CODE": "$SecurityDemo\\FileUpload\\FileUploadController->processBatchUpload@tmp-3"
        }
      },
      {
        "id": 251,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771183,
          "NAME": "<operator>.indexAccess",
          "ARGUMENT_INDEX": 1,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 1,
          "LINE_NUMBER": 101,
          "METHOD_FULL_NAME": "<operator>.indexAccess",
          "CODE": "$SecurityDemo\FileUpload\FileUploadController->processBatchUpload@tmp-3["success"]"
        }
      },
      {
        "id": 1615,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476818,
          "NAME": "SecurityDemo\\FileUpload\\FileUploadController->processBatchUpload@tmp-3",
          "ARGUMENT_INDEX": 1,
          "TYPE_FULL_NAME": "array",
          "ORDER": 1,
          "LINE_NUMBER": 101,
          "DYNAMIC_TYPE_HINT_FULL_NAME": [
            "array",
            "array-><returnValue>"
          ],
          "CODE": "$SecurityDemo\\FileUpload\\FileUploadController->processBatchUpload@tmp-3"
        }
      },
      {
        "id": 253,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771185,
          "NAME": "<operator>.indexAccess",
          "ARGUMENT_INDEX": 1,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 1,
          "LINE_NUMBER": 101,
          "METHOD_FULL_NAME": "<operator>.indexAccess",
          "CODE": "$SecurityDemo\FileUpload\FileUploadController->processBatchUpload@tmp-3["results"]"
        }
      },
      {
        "id": 252,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771184,
          "NAME": "<operator>.assignment",
          "ARGUMENT_INDEX": -1,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 3,
          "LINE_NUMBER": 101,
          "METHOD_FULL_NAME": "<operator>.assignment",
          "CODE": "$SecurityDemo\FileUpload\FileUploadController->processBatchUpload@tmp-3["results"] = $results"
        }
      },
      {
        "id": 1616,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476819,
          "NAME": "results",
          "ARGUMENT_INDEX": 2,
          "TYPE_FULL_NAME": "array",
          "ORDER": 2,
          "LINE_NUMBER": 101,
          "DYNAMIC_TYPE_HINT_FULL_NAME": [
            "array",
            "array-><returnValue>"
          ],
          "CODE": "$results"
        }
      },
      {
        "id": 2022,
        "labels": [
          "LOCAL"
        ],
        "properties": {
          "id": 94489280537,
          "NAME": "results",
          "GENERIC_SIGNATURE": "<empty>",
          "TYPE_FULL_NAME": "array",
          "ORDER": 12,
          "LINE_NUMBER": 81,
          "DYNAMIC_TYPE_HINT_FULL_NAME": [
            "array",
            "array-><returnValue>"
          ],
          "CODE": "$results"
        }
      },
      {
        "id": 1577,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476780,
          "NAME": "results",
          "ARGUMENT_INDEX": 1,
          "TYPE_FULL_NAME": "array",
          "ORDER": 1,
          "LINE_NUMBER": 81,
          "DYNAMIC_TYPE_HINT_FULL_NAME": [
            "array",
            "array-><returnValue>"
          ],
          "CODE": "$results"
        }
      },
      {
        "id": 1611,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476814,
          "NAME": "results",
          "ARGUMENT_INDEX": 1,
          "TYPE_FULL_NAME": "array",
          "ORDER": 1,
          "LINE_NUMBER": 98,
          "DYNAMIC_TYPE_HINT_FULL_NAME": [
            "array",
            "array-><returnValue>"
          ],
          "CODE": "$results"
        }
      },
      {
        "id": 247,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771179,
          "NAME": "array_push",
          "ARGUMENT_INDEX": -1,
          "SIGNATURE": "<unresolvedSignature>(2)",
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 4,
          "LINE_NUMBER": 98,
          "METHOD_FULL_NAME": "array_push",
          "DYNAMIC_TYPE_HINT_FULL_NAME": [
            "array_push"
          ],
          "CODE": "$results[] = $result"
        }
      },
      {
        "id": 1612,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476815,
          "NAME": "result",
          "ARGUMENT_INDEX": 2,
          "TYPE_FULL_NAME": "array",
          "ORDER": 2,
          "LINE_NUMBER": 98,
          "CODE": "$result"
        }
      },
      {
        "id": 2021,
        "labels": [
          "LOCAL"
        ],
        "properties": {
          "id": 94489280536,
          "NAME": "result",
          "GENERIC_SIGNATURE": "<empty>",
          "TYPE_FULL_NAME": "array",
          "ORDER": 11,
          "LINE_NUMBER": 97,
          "CODE": "$result"
        }
      },
      {
        "id": 1607,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476810,
          "NAME": "result",
          "ARGUMENT_INDEX": 1,
          "TYPE_FULL_NAME": "array",
          "ORDER": 1,
          "LINE_NUMBER": 97,
          "CODE": "$result"
        }
      },
      {
        "id": 244,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771176,
          "NAME": "<operator>.assignment",
          "ARGUMENT_INDEX": -1,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 3,
          "LINE_NUMBER": 97,
          "METHOD_FULL_NAME": "<operator>.assignment",
          "CODE": "$result = $this->uploader->handleUpload($fileData,$customName)"
        }
      },
      {
        "id": 245,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771177,
          "NAME": "handleUpload",
          "ARGUMENT_INDEX": 2,
          "SIGNATURE": "<unresolvedSignature>(2)",
          "DISPATCH_TYPE": "DYNAMIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 2,
          "LINE_NUMBER": 97,
          "METHOD_FULL_NAME": "SecurityDemo\\FileUpload\\VulnerableFileUploader->handleUpload-><returnValue>",
          "CODE": "$this->uploader->handleUpload($fileData,$customName)"
        }
      },
      {
        "id": 246,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771178,
          "NAME": "<operator>.fieldAccess",
          "ARGUMENT_INDEX": 0,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 1,
          "LINE_NUMBER": 97,
          "METHOD_FULL_NAME": "<operator>.fieldAccess",
          "CODE": "$this->uploader"
        }
      },
      {
        "id": 1608,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476811,
          "NAME": "this",
          "ARGUMENT_INDEX": 1,
          "TYPE_FULL_NAME": "SecurityDemo\\FileUpload\\FileUploadController",
          "ORDER": 1,
          "LINE_NUMBER": 97,
          "CODE": "$this"
        }
      },
      {
        "id": 2070,
        "labels": [
          "METHOD_PARAMETER_IN"
        ],
        "properties": {
          "IS_VARIADIC": false,
          "id": 115964116995,
          "INDEX": 0,
          "NAME": "this",
          "TYPE_FULL_NAME": "SecurityDemo\\FileUpload\\FileUploadController",
          "ORDER": 0,
          "EVALUATION_STRATEGY": "BY_SHARING",
          "LINE_NUMBER": 75,
          "DYNAMIC_TYPE_HINT_FULL_NAME": [
            "SecurityDemo\\FileUpload\\FileUploadController"
          ],
          "CODE": "this"
        }
      },
      {
        "id": 2399,
        "labels": [
          "METHOD"
        ],
        "properties": {
          "id": 111669149700,
          "NAME": "processBatchUpload",
          "FULL_NAME": "SecurityDemo\\FileUpload\\FileUploadController->processBatchUpload",
          "SIGNATURE": "<unresolvedSignature>(0)",
          "GENERIC_SIGNATURE": "<empty>",
          "ORDER": 6,
          "FILENAME": "FileUploadHandler.php",
          "LINE_NUMBER": 75,
          "IS_EXTERNAL": false,
          "AST_PARENT_FULL_NAME": "SecurityDemo\\FileUpload\\FileUploadController",
          "AST_PARENT_TYPE": "TYPE_DECL",
          "CODE": "PRIVATE function processBatchUpload(this)"
        }
      },
      {
        "id": 1584,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476787,
          "NAME": "files",
          "ARGUMENT_INDEX": 1,
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 1,
          "LINE_NUMBER": 85,
          "CODE": "$files"
        }
      },
      {
        "id": 2019,
        "labels": [
          "LOCAL"
        ],
        "properties": {
          "id": 94489280534,
          "NAME": "files",
          "GENERIC_SIGNATURE": "<empty>",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 9,
          "LINE_NUMBER": 82,
          "CODE": "$files"
        }
      },
      {
        "id": 1595,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476798,
          "NAME": "files",
          "ARGUMENT_INDEX": 1,
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 1,
          "LINE_NUMBER": 89,
          "CODE": "$files"
        }
      },
      {
        "id": 231,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771163,
          "NAME": "<operator>.indexAccess",
          "ARGUMENT_INDEX": 1,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 1,
          "LINE_NUMBER": 89,
          "METHOD_FULL_NAME": "<operator>.indexAccess",
          "CODE": "$files["tmp_name"]"
        }
      },
      {
        "id": 230,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771162,
          "NAME": "<operator>.indexAccess",
          "ARGUMENT_INDEX": 2,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 2,
          "LINE_NUMBER": 89,
          "METHOD_FULL_NAME": "<operator>.indexAccess",
          "CODE": "$files["tmp_name"][$i]"
        }
      },
      {
        "id": 1596,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476799,
          "NAME": "i",
          "ARGUMENT_INDEX": 2,
          "TYPE_FULL_NAME": "int",
          "ORDER": 2,
          "LINE_NUMBER": 89,
          "CODE": "$i"
        }
      },
      {
        "id": 2020,
        "labels": [
          "LOCAL"
        ],
        "properties": {
          "id": 94489280535,
          "NAME": "i",
          "GENERIC_SIGNATURE": "<empty>",
          "TYPE_FULL_NAME": "int",
          "ORDER": 10,
          "LINE_NUMBER": 85,
          "CODE": "$i"
        }
      },
      {
        "id": 1582,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476785,
          "NAME": "i",
          "ARGUMENT_INDEX": 1,
          "TYPE_FULL_NAME": "int",
          "ORDER": 1,
          "LINE_NUMBER": 85,
          "CODE": "$i"
        }
      },
      {
        "id": 1583,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476786,
          "NAME": "i",
          "ARGUMENT_INDEX": 1,
          "TYPE_FULL_NAME": "int",
          "ORDER": 1,
          "LINE_NUMBER": 85,
          "CODE": "$i"
        }
      },
      {
        "id": 213,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771145,
          "NAME": "<operator>.lessThan",
          "ARGUMENT_INDEX": -1,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 2,
          "LINE_NUMBER": 85,
          "METHOD_FULL_NAME": "<operator>.lessThan",
          "CODE": "$i < count($files["name"])"
        }
      },
      {
        "id": 214,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771146,
          "NAME": "count",
          "ARGUMENT_INDEX": 2,
          "SIGNATURE": "<unresolvedSignature>(1)",
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 2,
          "LINE_NUMBER": 85,
          "METHOD_FULL_NAME": "count",
          "DYNAMIC_TYPE_HINT_FULL_NAME": [
            "count"
          ],
          "CODE": "count($files["name"])"
        }
      },
      {
        "id": 215,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771147,
          "NAME": "<operator>.indexAccess",
          "ARGUMENT_INDEX": 1,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 1,
          "LINE_NUMBER": 85,
          "METHOD_FULL_NAME": "<operator>.indexAccess",
          "CODE": "$files["name"]"
        }
      },
      {
        "id": 1601,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476804,
          "NAME": "files",
          "ARGUMENT_INDEX": 1,
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 1,
          "LINE_NUMBER": 91,
          "CODE": "$files"
        }
      },
      {
        "id": 239,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771171,
          "NAME": "<operator>.indexAccess",
          "ARGUMENT_INDEX": 1,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 1,
          "LINE_NUMBER": 91,
          "METHOD_FULL_NAME": "<operator>.indexAccess",
          "CODE": "$files["size"]"
        }
      },
      {
        "id": 238,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771170,
          "NAME": "<operator>.indexAccess",
          "ARGUMENT_INDEX": 2,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 2,
          "LINE_NUMBER": 91,
          "METHOD_FULL_NAME": "<operator>.indexAccess",
          "CODE": "$files["size"][$i]"
        }
      },
      {
        "id": 236,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771168,
          "NAME": "<operator>.assignment",
          "ARGUMENT_INDEX": -1,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 6,
          "LINE_NUMBER": 91,
          "METHOD_FULL_NAME": "<operator>.assignment",
          "CODE": "$SecurityDemo\FileUpload\FileUploadController->processBatchUpload@tmp-2["size"] = $files["size"][$i]"
        }
      },
      {
        "id": 237,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771169,
          "NAME": "<operator>.indexAccess",
          "ARGUMENT_INDEX": 1,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 1,
          "LINE_NUMBER": 91,
          "METHOD_FULL_NAME": "<operator>.indexAccess",
          "CODE": "$SecurityDemo\FileUpload\FileUploadController->processBatchUpload@tmp-2["size"]"
        }
      },
      {
        "id": 1600,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476803,
          "NAME": "SecurityDemo\\FileUpload\\FileUploadController->processBatchUpload@tmp-2",
          "ARGUMENT_INDEX": 1,
          "TYPE_FULL_NAME": "array",
          "ORDER": 1,
          "LINE_NUMBER": 91,
          "DYNAMIC_TYPE_HINT_FULL_NAME": [
            "array",
            "array-><returnValue>"
          ],
          "CODE": "$SecurityDemo\\FileUpload\\FileUploadController->processBatchUpload@tmp-2"
        }
      },
      {
        "id": 2013,
        "labels": [
          "LOCAL"
        ],
        "properties": {
          "id": 94489280528,
          "NAME": "SecurityDemo\\FileUpload\\FileUploadController->processBatchUpload@tmp-2",
          "GENERIC_SIGNATURE": "<empty>",
          "TYPE_FULL_NAME": "array",
          "ORDER": 3,
          "LINE_NUMBER": 86,
          "DYNAMIC_TYPE_HINT_FULL_NAME": [
            "array",
            "array-><returnValue>"
          ],
          "CODE": "$SecurityDemo\\FileUpload\\FileUploadController->processBatchUpload@tmp-2"
        }
      },
      {
        "id": 1597,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476800,
          "NAME": "SecurityDemo\\FileUpload\\FileUploadController->processBatchUpload@tmp-2",
          "ARGUMENT_INDEX": 1,
          "TYPE_FULL_NAME": "array",
          "ORDER": 1,
          "LINE_NUMBER": 90,
          "DYNAMIC_TYPE_HINT_FULL_NAME": [
            "array",
            "array-><returnValue>"
          ],
          "CODE": "$SecurityDemo\\FileUpload\\FileUploadController->processBatchUpload@tmp-2"
        }
      },
      {
        "id": 233,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771165,
          "NAME": "<operator>.indexAccess",
          "ARGUMENT_INDEX": 1,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 1,
          "LINE_NUMBER": 90,
          "METHOD_FULL_NAME": "<operator>.indexAccess",
          "CODE": "$SecurityDemo\FileUpload\FileUploadController->processBatchUpload@tmp-2["error"]"
        }
      },
      {
        "id": 232,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771164,
          "NAME": "<operator>.assignment",
          "ARGUMENT_INDEX": -1,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 5,
          "LINE_NUMBER": 90,
          "METHOD_FULL_NAME": "<operator>.assignment",
          "CODE": "$SecurityDemo\FileUpload\FileUploadController->processBatchUpload@tmp-2["error"] = $files["error"][$i]"
        }
      },
      {
        "id": 234,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771166,
          "NAME": "<operator>.indexAccess",
          "ARGUMENT_INDEX": 2,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 2,
          "LINE_NUMBER": 90,
          "METHOD_FULL_NAME": "<operator>.indexAccess",
          "CODE": "$files["error"][$i]"
        }
      },
      {
        "id": 235,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771167,
          "NAME": "<operator>.indexAccess",
          "ARGUMENT_INDEX": 1,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 1,
          "LINE_NUMBER": 90,
          "METHOD_FULL_NAME": "<operator>.indexAccess",
          "CODE": "$files["error"]"
        }
      },
      {
        "id": 1598,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476801,
          "NAME": "files",
          "ARGUMENT_INDEX": 1,
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 1,
          "LINE_NUMBER": 90,
          "CODE": "$files"
        }
      },
      {
        "id": 1580,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476783,
          "NAME": "files",
          "ARGUMENT_INDEX": 1,
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 1,
          "LINE_NUMBER": 82,
          "CODE": "$files"
        }
      },
      {
        "id": 210,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771142,
          "NAME": "<operator>.assignment",
          "ARGUMENT_INDEX": -1,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 15,
          "LINE_NUMBER": 82,
          "METHOD_FULL_NAME": "<operator>.assignment",
          "CODE": "$files = $_FILES["files"]"
        }
      },
      {
        "id": 211,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771143,
          "NAME": "<operator>.indexAccess",
          "ARGUMENT_INDEX": 2,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 2,
          "LINE_NUMBER": 82,
          "METHOD_FULL_NAME": "<operator>.indexAccess",
          "CODE": "$_FILES["files"]"
        }
      },
      {
        "id": 1581,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476784,
          "NAME": "_FILES",
          "ARGUMENT_INDEX": 1,
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 1,
          "LINE_NUMBER": 82,
          "CODE": "$_FILES"
        }
      },
      {
        "id": 2015,
        "labels": [
          "LOCAL"
        ],
        "properties": {
          "id": 94489280530,
          "NAME": "_FILES",
          "GENERIC_SIGNATURE": "<empty>",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 5,
          "LINE_NUMBER": 77,
          "CODE": "$_FILES"
        }
      },
      {
        "id": 1572,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476775,
          "NAME": "_FILES",
          "ARGUMENT_INDEX": 1,
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 1,
          "LINE_NUMBER": 77,
          "CODE": "$_FILES"
        }
      },
      {
        "id": 200,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771132,
          "NAME": "<operator>.indexAccess",
          "ARGUMENT_INDEX": 1,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 1,
          "LINE_NUMBER": 77,
          "METHOD_FULL_NAME": "<operator>.indexAccess",
          "CODE": "$_FILES["files"]"
        }
      },
      {
        "id": 199,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771131,
          "NAME": "isset",
          "ARGUMENT_INDEX": 1,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "bool",
          "ORDER": 1,
          "LINE_NUMBER": 77,
          "METHOD_FULL_NAME": "isset",
          "DYNAMIC_TYPE_HINT_FULL_NAME": [
            "isset"
          ],
          "CODE": "isset($_FILES["files"])"
        }
      },
      {
        "id": 198,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771130,
          "NAME": "<operator>.logicalNot",
          "ARGUMENT_INDEX": -1,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 1,
          "LINE_NUMBER": 77,
          "METHOD_FULL_NAME": "<operator>.logicalNot",
          "CODE": "!isset($_FILES["files"])"
        }
      },
      {
        "id": 2425,
        "labels": [
          "METHOD"
        ],
        "properties": {
          "id": 111669149726,
          "FULL_NAME": "<operator>.logicalNot",
          "NAME": "<operator>.logicalNot",
          "GENERIC_SIGNATURE": "<empty>",
          "ORDER": 0,
          "FILENAME": "<empty>",
          "AST_PARENT_FULL_NAME": "<global>",
          "IS_EXTERNAL": true,
          "AST_PARENT_TYPE": "NAMESPACE_BLOCK",
          "CODE": "<empty>"
        }
      },
      {
        "id": 353,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771285,
          "NAME": "<operator>.logicalNot",
          "ARGUMENT_INDEX": 1,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 1,
          "LINE_NUMBER": 122,
          "METHOD_FULL_NAME": "<operator>.logicalNot",
          "CODE": "!empty($customName)"
        }
      },
      {
        "id": 352,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771284,
          "NAME": "<operator>.conditional",
          "ARGUMENT_INDEX": 2,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 2,
          "LINE_NUMBER": 122,
          "METHOD_FULL_NAME": "<operator>.conditional",
          "CODE": "!empty($customName) ? $customName : $originalName"
        }
      },
      {
        "id": 1687,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476890,
          "NAME": "originalName",
          "ARGUMENT_INDEX": 3,
          "TYPE_FULL_NAME": "string",
          "ORDER": 3,
          "LINE_NUMBER": 122,
          "CODE": "$originalName"
        }
      },
      {
        "id": 2082,
        "labels": [
          "METHOD_PARAMETER_IN"
        ],
        "properties": {
          "IS_VARIADIC": false,
          "id": 115964117007,
          "INDEX": 1,
          "NAME": "originalName",
          "TYPE_FULL_NAME": "string",
          "ORDER": 1,
          "EVALUATION_STRATEGY": "BY_VALUE",
          "LINE_NUMBER": 120,
          "CODE": "$originalName"
        }
      },
      {
        "id": 2406,
        "labels": [
          "METHOD"
        ],
        "properties": {
          "id": 111669149707,
          "NAME": "processFileName",
          "FULL_NAME": "SecurityDemo\\FileUpload\\VulnerableFileUploader->processFileName",
          "SIGNATURE": "<unresolvedSignature>(2)",
          "GENERIC_SIGNATURE": "<empty>",
          "ORDER": 9,
          "FILENAME": "FileUploadVulnerability.php",
          "LINE_NUMBER": 120,
          "IS_EXTERNAL": false,
          "AST_PARENT_FULL_NAME": "SecurityDemo\\FileUpload\\VulnerableFileUploader",
          "AST_PARENT_TYPE": "TYPE_DECL",
          "CODE": "PRIVATE function processFileName(this,$originalName,$customName)"
        }
      },
      {
        "id": 1708,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476911,
          "NAME": "fileName",
          "ARGUMENT_INDEX": 1,
          "TYPE_FULL_NAME": "string",
          "ORDER": 1,
          "LINE_NUMBER": 139,
          "DYNAMIC_TYPE_HINT_FULL_NAME": [
            "string",
            "str_replace-><returnValue>"
          ],
          "CODE": "$fileName"
        }
      },
      {
        "id": 2037,
        "labels": [
          "LOCAL"
        ],
        "properties": {
          "id": 94489280552,
          "NAME": "fileName",
          "GENERIC_SIGNATURE": "<empty>",
          "TYPE_FULL_NAME": "string",
          "ORDER": 6,
          "LINE_NUMBER": 122,
          "DYNAMIC_TYPE_HINT_FULL_NAME": [
            "string",
            "str_replace-><returnValue>"
          ],
          "CODE": "$fileName"
        }
      },
      {
        "id": 1707,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476910,
          "NAME": "fileName",
          "ARGUMENT_INDEX": 1,
          "TYPE_FULL_NAME": "string",
          "ORDER": 1,
          "LINE_NUMBER": 134,
          "DYNAMIC_TYPE_HINT_FULL_NAME": [
            "string",
            "str_replace-><returnValue>"
          ],
          "CODE": "$fileName"
        }
      },
      {
        "id": 1695,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476898,
          "NAME": "fileName",
          "ARGUMENT_INDEX": 1,
          "TYPE_FULL_NAME": "string",
          "ORDER": 1,
          "LINE_NUMBER": 128,
          "DYNAMIC_TYPE_HINT_FULL_NAME": [
            "string",
            "str_replace-><returnValue>"
          ],
          "CODE": "$fileName"
        }
      },
      {
        "id": 1324,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771297,
          "NAME": "pathinfo",
          "ARGUMENT_INDEX": 1,
          "SIGNATURE": "<unresolvedSignature>(2)",
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 1,
          "LINE_NUMBER": 128,
          "METHOD_FULL_NAME": "pathinfo",
          "DYNAMIC_TYPE_HINT_FULL_NAME": [
            "pathinfo"
          ],
          "CODE": "pathinfo($fileName,PATHINFO_EXTENSION)"
        }
      },
      {
        "id": 1325,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771298,
          "NAME": "<operator>.fieldAccess",
          "ARGUMENT_INDEX": 2,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 2,
          "LINE_NUMBER": 128,
          "METHOD_FULL_NAME": "<operator>.fieldAccess",
          "CODE": "PATHINFO_EXTENSION"
        }
      },
      {
        "id": 2417,
        "labels": [
          "METHOD"
        ],
        "properties": {
          "id": 111669149718,
          "FULL_NAME": "<operator>.fieldAccess",
          "NAME": "<operator>.fieldAccess",
          "GENERIC_SIGNATURE": "<empty>",
          "ORDER": 0,
          "FILENAME": "<empty>",
          "AST_PARENT_FULL_NAME": "<global>",
          "IS_EXTERNAL": true,
          "AST_PARENT_TYPE": "NAMESPACE_BLOCK",
          "CODE": "<empty>"
        }
      },
      {
        "id": 273,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771205,
          "NAME": "<operator>.fieldAccess",
          "ARGUMENT_INDEX": 1,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 1,
          "LINE_NUMBER": 34,
          "METHOD_FULL_NAME": "<operator>.fieldAccess",
          "CODE": "$this->uploadDir"
        }
      },
      {
        "id": 272,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771204,
          "NAME": "<operator>.assignment",
          "ARGUMENT_INDEX": -1,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 3,
          "LINE_NUMBER": 34,
          "METHOD_FULL_NAME": "<operator>.assignment",
          "CODE": "$this->uploadDir = $uploadDir"
        }
      },
      {
        "id": 1631,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476834,
          "NAME": "uploadDir",
          "ARGUMENT_INDEX": 2,
          "TYPE_FULL_NAME": "string",
          "ORDER": 2,
          "LINE_NUMBER": 34,
          "CODE": "$uploadDir"
        }
      },
      {
        "id": 2074,
        "labels": [
          "METHOD_PARAMETER_IN"
        ],
        "properties": {
          "IS_VARIADIC": false,
          "id": 115964116999,
          "INDEX": 1,
          "NAME": "uploadDir",
          "TYPE_FULL_NAME": "string",
          "ORDER": 1,
          "EVALUATION_STRATEGY": "BY_VALUE",
          "LINE_NUMBER": 32,
          "CODE": "$uploadDir"
        }
      },
      {
        "id": 2402,
        "labels": [
          "METHOD"
        ],
        "properties": {
          "id": 111669149703,
          "NAME": "__construct",
          "FULL_NAME": "SecurityDemo\\FileUpload\\VulnerableFileUploader->__construct",
          "SIGNATURE": "<unresolvedSignature>(1)",
          "GENERIC_SIGNATURE": "<empty>",
          "ORDER": 5,
          "FILENAME": "FileUploadVulnerability.php",
          "LINE_NUMBER": 32,
          "IS_EXTERNAL": false,
          "AST_PARENT_FULL_NAME": "SecurityDemo\\FileUpload\\VulnerableFileUploader",
          "AST_PARENT_TYPE": "TYPE_DECL",
          "CODE": "PUBLIC function __construct(this,$uploadDir)"
        }
      },
      {
        "id": 1634,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476837,
          "NAME": "SecurityDemo\\FileUpload\\VulnerableFileUploader->__construct@tmp-0",
          "ARGUMENT_INDEX": 1,
          "TYPE_FULL_NAME": "array",
          "ORDER": 1,
          "LINE_NUMBER": 37,
          "DYNAMIC_TYPE_HINT_FULL_NAME": [
            "array",
            "array-><returnValue>"
          ],
          "CODE": "$SecurityDemo\\FileUpload\\VulnerableFileUploader->__construct@tmp-0"
        }
      },
      {
        "id": 2024,
        "labels": [
          "LOCAL"
        ],
        "properties": {
          "id": 94489280539,
          "NAME": "SecurityDemo\\FileUpload\\VulnerableFileUploader->__construct@tmp-0",
          "GENERIC_SIGNATURE": "<empty>",
          "TYPE_FULL_NAME": "array",
          "ORDER": 1,
          "LINE_NUMBER": 36,
          "DYNAMIC_TYPE_HINT_FULL_NAME": [
            "array",
            "array-><returnValue>"
          ],
          "CODE": "$SecurityDemo\\FileUpload\\VulnerableFileUploader->__construct@tmp-0"
        }
      },
      {
        "id": 1637,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476840,
          "NAME": "SecurityDemo\\FileUpload\\VulnerableFileUploader->__construct@tmp-0",
          "ARGUMENT_INDEX": 1,
          "TYPE_FULL_NAME": "array",
          "ORDER": 1,
          "LINE_NUMBER": 40,
          "DYNAMIC_TYPE_HINT_FULL_NAME": [
            "array",
            "array-><returnValue>"
          ],
          "CODE": "$SecurityDemo\\FileUpload\\VulnerableFileUploader->__construct@tmp-0"
        }
      },
      {
        "id": 285,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771217,
          "NAME": "<operator>.indexAccess",
          "ARGUMENT_INDEX": 1,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 1,
          "LINE_NUMBER": 40,
          "METHOD_FULL_NAME": "<operator>.indexAccess",
          "CODE": "$SecurityDemo\\FileUpload\\VulnerableFileUploader->__construct@tmp-0[3]"
        }
      },
      {
        "id": 2420,
        "labels": [
          "METHOD"
        ],
        "properties": {
          "id": 111669149721,
          "FULL_NAME": "<operator>.indexAccess",
          "NAME": "<operator>.indexAccess",
          "GENERIC_SIGNATURE": "<empty>",
          "ORDER": 0,
          "FILENAME": "<empty>",
          "AST_PARENT_FULL_NAME": "<global>",
          "IS_EXTERNAL": true,
          "AST_PARENT_TYPE": "NAMESPACE_BLOCK",
          "CODE": "<empty>"
        }
      },
      {
        "id": 1428,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771401,
          "NAME": "<operator>.indexAccess",
          "ARGUMENT_INDEX": 2,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 2,
          "LINE_NUMBER": 88,
          "METHOD_FULL_NAME": "<operator>.indexAccess",
          "CODE": "$bytes[$i]"
        }
      },
      {
        "id": 1425,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771398,
          "NAME": "<operator>.notIdentical",
          "ARGUMENT_INDEX": 2,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 2,
          "LINE_NUMBER": 88,
          "METHOD_FULL_NAME": "<operator>.notIdentical",
          "CODE": "ord($header[$i]) !== $bytes[$i]"
        }
      },
      {
        "id": 1421,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771394,
          "NAME": "<operator>.logicalOr",
          "ARGUMENT_INDEX": -1,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 1,
          "LINE_NUMBER": 88,
          "METHOD_FULL_NAME": "<operator>.logicalOr",
          "CODE": "!isset($header[$i]) || ord($header[$i]) !== $bytes[$i]"
        }
      },
      {
        "id": 1422,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771395,
          "NAME": "<operator>.logicalNot",
          "ARGUMENT_INDEX": 1,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 1,
          "LINE_NUMBER": 88,
          "METHOD_FULL_NAME": "<operator>.logicalNot",
          "CODE": "!isset($header[$i])"
        }
      },
      {
        "id": 1423,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771396,
          "NAME": "isset",
          "ARGUMENT_INDEX": 1,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "bool",
          "ORDER": 1,
          "LINE_NUMBER": 88,
          "METHOD_FULL_NAME": "isset",
          "DYNAMIC_TYPE_HINT_FULL_NAME": [
            "isset"
          ],
          "CODE": "isset($header[$i])"
        }
      },
      {
        "id": 1424,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771397,
          "NAME": "<operator>.indexAccess",
          "ARGUMENT_INDEX": 1,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 1,
          "LINE_NUMBER": 88,
          "METHOD_FULL_NAME": "<operator>.indexAccess",
          "CODE": "$header[$i]"
        }
      },
      {
        "id": 1770,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476973,
          "NAME": "i",
          "ARGUMENT_INDEX": 2,
          "TYPE_FULL_NAME": "int",
          "ORDER": 2,
          "LINE_NUMBER": 88,
          "CODE": "$i"
        }
      },
      {
        "id": 2047,
        "labels": [
          "LOCAL"
        ],
        "properties": {
          "id": 94489280562,
          "NAME": "i",
          "GENERIC_SIGNATURE": "<empty>",
          "TYPE_FULL_NAME": "int",
          "ORDER": 5,
          "LINE_NUMBER": 87,
          "CODE": "$i"
        }
      },
      {
        "id": 1772,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476975,
          "NAME": "i",
          "ARGUMENT_INDEX": 2,
          "TYPE_FULL_NAME": "int",
          "ORDER": 2,
          "LINE_NUMBER": 88,
          "CODE": "$i"
        }
      },
      {
        "id": 1427,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771400,
          "NAME": "<operator>.indexAccess",
          "ARGUMENT_INDEX": 1,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 1,
          "LINE_NUMBER": 88,
          "METHOD_FULL_NAME": "<operator>.indexAccess",
          "CODE": "$header[$i]"
        }
      },
      {
        "id": 1771,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476974,
          "NAME": "header",
          "ARGUMENT_INDEX": 1,
          "TYPE_FULL_NAME": "fread-><returnValue>",
          "ORDER": 1,
          "LINE_NUMBER": 88,
          "CODE": "$header"
        }
      },
      {
        "id": 2046,
        "labels": [
          "LOCAL"
        ],
        "properties": {
          "id": 94489280561,
          "NAME": "header",
          "GENERIC_SIGNATURE": "<empty>",
          "TYPE_FULL_NAME": "fread-><returnValue>",
          "ORDER": 4,
          "LINE_NUMBER": 81,
          "CODE": "$header"
        }
      },
      {
        "id": 1749,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476952,
          "NAME": "header",
          "ARGUMENT_INDEX": 1,
          "TYPE_FULL_NAME": "fread-><returnValue>",
          "ORDER": 1,
          "LINE_NUMBER": 81,
          "CODE": "$header"
        }
      },
      {
        "id": 1400,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771373,
          "NAME": "<operator>.assignment",
          "ARGUMENT_INDEX": -1,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 10,
          "LINE_NUMBER": 81,
          "METHOD_FULL_NAME": "<operator>.assignment",
          "CODE": "$header = fread($handle,8)"
        }
      },
      {
        "id": 1401,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771374,
          "NAME": "fread",
          "ARGUMENT_INDEX": 2,
          "SIGNATURE": "<unresolvedSignature>(2)",
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 2,
          "LINE_NUMBER": 81,
          "METHOD_FULL_NAME": "fread",
          "DYNAMIC_TYPE_HINT_FULL_NAME": [
            "fread"
          ],
          "CODE": "fread($handle,8)"
        }
      },
      {
        "id": 1750,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476953,
          "NAME": "handle",
          "ARGUMENT_INDEX": 1,
          "TYPE_FULL_NAME": "fopen-><returnValue>",
          "ORDER": 1,
          "LINE_NUMBER": 81,
          "CODE": "$handle"
        }
      },
      {
        "id": 2045,
        "labels": [
          "LOCAL"
        ],
        "properties": {
          "id": 94489280560,
          "NAME": "handle",
          "GENERIC_SIGNATURE": "<empty>",
          "TYPE_FULL_NAME": "fopen-><returnValue>",
          "ORDER": 3,
          "LINE_NUMBER": 76,
          "CODE": "$handle"
        }
      },
      {
        "id": 1751,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476954,
          "NAME": "handle",
          "ARGUMENT_INDEX": 1,
          "TYPE_FULL_NAME": "fopen-><returnValue>",
          "ORDER": 1,
          "LINE_NUMBER": 82,
          "CODE": "$handle"
        }
      },
      {
        "id": 2410,
        "labels": [
          "METHOD"
        ],
        "properties": {
          "id": 111669149711,
          "NAME": "checkMagicBytes",
          "FULL_NAME": "SecurityDemo\\FileUpload\\FileValidator->checkMagicBytes",
          "SIGNATURE": "<unresolvedSignature>(1)",
          "GENERIC_SIGNATURE": "<empty>",
          "ORDER": 5,
          "FILENAME": "FileValidator.php",
          "LINE_NUMBER": 74,
          "IS_EXTERNAL": false,
          "AST_PARENT_FULL_NAME": "SecurityDemo\\FileUpload\\FileValidator",
          "AST_PARENT_TYPE": "TYPE_DECL",
          "CODE": "PRIVATE function checkMagicBytes(this,$filePath)"
        }
      },
      {
        "id": 1767,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476970,
          "NAME": "bytes",
          "ARGUMENT_INDEX": 1,
          "TYPE_FULL_NAME": "Iterator.current-><returnValue>",
          "ORDER": 1,
          "LINE_NUMBER": 87,
          "CODE": "$bytes"
        }
      },
      {
        "id": 2044,
        "labels": [
          "LOCAL"
        ],
        "properties": {
          "id": 94489280559,
          "NAME": "bytes",
          "GENERIC_SIGNATURE": "<empty>",
          "TYPE_FULL_NAME": "Iterator.current-><returnValue>",
          "ORDER": 2,
          "LINE_NUMBER": 85,
          "CODE": "$bytes"
        }
      },
      {
        "id": 1762,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476965,
          "NAME": "bytes",
          "ARGUMENT_INDEX": 1,
          "TYPE_FULL_NAME": "Iterator.current-><returnValue>",
          "ORDER": 1,
          "LINE_NUMBER": 85,
          "CODE": "$bytes"
        }
      },
      {
        "id": 1414,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771387,
          "NAME": "<operator>.assignment",
          "ARGUMENT_INDEX": -1,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 2,
          "LINE_NUMBER": 85,
          "METHOD_FULL_NAME": "<operator>.assignment",
          "CODE": "$bytes = $SecurityDemo\\FileUpload\\FileValidator->checkMagicBytes@iter_tmp-0->current()"
        }
      },
      {
        "id": 1415,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771388,
          "NAME": "current",
          "ARGUMENT_INDEX": 2,
          "SIGNATURE": "<unresolvedSignature>(0)",
          "DISPATCH_TYPE": "DYNAMIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 2,
          "LINE_NUMBER": 85,
          "METHOD_FULL_NAME": "Iterator.current",
          "DYNAMIC_TYPE_HINT_FULL_NAME": [
            "Iterator.current"
          ],
          "CODE": "$SecurityDemo\\FileUpload\\FileValidator->checkMagicBytes@iter_tmp-0->current()"
        }
      },
      {
        "id": 1763,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476966,
          "NAME": "SecurityDemo\\FileUpload\\FileValidator->checkMagicBytes@iter_tmp-0",
          "ARGUMENT_INDEX": 0,
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 1,
          "LINE_NUMBER": 85,
          "DYNAMIC_TYPE_HINT_FULL_NAME": [
            "array",
            "array-><returnValue>"
          ],
          "CODE": "$SecurityDemo\\FileUpload\\FileValidator->checkMagicBytes@iter_tmp-0"
        }
      },
      {
        "id": 2043,
        "labels": [
          "LOCAL"
        ],
        "properties": {
          "id": 94489280558,
          "NAME": "SecurityDemo\\FileUpload\\FileValidator->checkMagicBytes@iter_tmp-0",
          "GENERIC_SIGNATURE": "<empty>",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 1,
          "LINE_NUMBER": 85,
          "DYNAMIC_TYPE_HINT_FULL_NAME": [
            "array",
            "array-><returnValue>"
          ],
          "CODE": "$SecurityDemo\\FileUpload\\FileValidator->checkMagicBytes@iter_tmp-0"
        }
      },
      {
        "id": 1752,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476955,
          "NAME": "SecurityDemo\\FileUpload\\FileValidator->checkMagicBytes@iter_tmp-0",
          "ARGUMENT_INDEX": 1,
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 1,
          "LINE_NUMBER": 85,
          "DYNAMIC_TYPE_HINT_FULL_NAME": [
            "array",
            "array-><returnValue>"
          ],
          "CODE": "$SecurityDemo\\FileUpload\\FileValidator->checkMagicBytes@iter_tmp-0"
        }
      },
      {
        "id": 1755,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476958,
          "NAME": "SecurityDemo\\FileUpload\\FileValidator->checkMagicBytes@iter_tmp-0",
          "ARGUMENT_INDEX": 0,
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 1,
          "LINE_NUMBER": 85,
          "DYNAMIC_TYPE_HINT_FULL_NAME": [
            "array",
            "array-><returnValue>"
          ],
          "CODE": "$SecurityDemo\\FileUpload\\FileValidator->checkMagicBytes@iter_tmp-0"
        }
      },
      {
        "id": 1406,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771379,
          "NAME": "key",
          "ARGUMENT_INDEX": 2,
          "SIGNATURE": "<unresolvedSignature>(0)",
          "DISPATCH_TYPE": "DYNAMIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 2,
          "LINE_NUMBER": 85,
          "METHOD_FULL_NAME": "Iterator.key",
          "DYNAMIC_TYPE_HINT_FULL_NAME": [
            "Iterator.key"
          ],
          "CODE": "$SecurityDemo\\FileUpload\\FileValidator->checkMagicBytes@iter_tmp-0->key()"
        }
      },
      {
        "id": 1405,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771378,
          "NAME": "<operator>.assignment",
          "ARGUMENT_INDEX": -1,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 1,
          "LINE_NUMBER": 85,
          "METHOD_FULL_NAME": "<operator>.assignment",
          "CODE": "$type = $SecurityDemo\\FileUpload\\FileValidator->checkMagicBytes@iter_tmp-0->key()"
        }
      },
      {
        "id": 1754,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476957,
          "NAME": "type",
          "ARGUMENT_INDEX": 1,
          "TYPE_FULL_NAME": "Iterator.key-><returnValue>",
          "ORDER": 1,
          "LINE_NUMBER": 85,
          "CODE": "$type"
        }
      },
      {
        "id": 2049,
        "labels": [
          "LOCAL"
        ],
        "properties": {
          "id": 94489280564,
          "NAME": "type",
          "GENERIC_SIGNATURE": "<empty>",
          "TYPE_FULL_NAME": "Iterator.key-><returnValue>",
          "ORDER": 7,
          "LINE_NUMBER": 85,
          "CODE": "$type"
        }
      },
      {
        "id": 1760,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476963,
          "NAME": "type",
          "ARGUMENT_INDEX": 1,
          "TYPE_FULL_NAME": "Iterator.key-><returnValue>",
          "ORDER": 1,
          "LINE_NUMBER": 85,
          "CODE": "$type"
        }
      },
      {
        "id": 1412,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771385,
          "NAME": "<operator>.assignment",
          "ARGUMENT_INDEX": -1,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 1,
          "LINE_NUMBER": 85,
          "METHOD_FULL_NAME": "<operator>.assignment",
          "CODE": "$type = $SecurityDemo\\FileUpload\\FileValidator->checkMagicBytes@iter_tmp-0->key()"
        }
      },
      {
        "id": 1413,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771386,
          "NAME": "key",
          "ARGUMENT_INDEX": 2,
          "SIGNATURE": "<unresolvedSignature>(0)",
          "DISPATCH_TYPE": "DYNAMIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 2,
          "LINE_NUMBER": 85,
          "METHOD_FULL_NAME": "Iterator.key",
          "DYNAMIC_TYPE_HINT_FULL_NAME": [
            "Iterator.key"
          ],
          "CODE": "$SecurityDemo\\FileUpload\\FileValidator->checkMagicBytes@iter_tmp-0->key()"
        }
      },
      {
        "id": 1761,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476964,
          "NAME": "SecurityDemo\\FileUpload\\FileValidator->checkMagicBytes@iter_tmp-0",
          "ARGUMENT_INDEX": 0,
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 1,
          "LINE_NUMBER": 85,
          "DYNAMIC_TYPE_HINT_FULL_NAME": [
            "array",
            "array-><returnValue>"
          ],
          "CODE": "$SecurityDemo\\FileUpload\\FileValidator->checkMagicBytes@iter_tmp-0"
        }
      },
      {
        "id": 1759,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476962,
          "NAME": "SecurityDemo\\FileUpload\\FileValidator->checkMagicBytes@iter_tmp-0",
          "ARGUMENT_INDEX": 0,
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 1,
          "LINE_NUMBER": 85,
          "DYNAMIC_TYPE_HINT_FULL_NAME": [
            "array",
            "array-><returnValue>"
          ],
          "CODE": "$SecurityDemo\\FileUpload\\FileValidator->checkMagicBytes@iter_tmp-0"
        }
      },
      {
        "id": 1411,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771384,
          "NAME": "next",
          "ARGUMENT_INDEX": -1,
          "DISPATCH_TYPE": "DYNAMIC_DISPATCH",
          "SIGNATURE": "void()",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 1,
          "LINE_NUMBER": 85,
          "METHOD_FULL_NAME": "Iterator.next",
          "DYNAMIC_TYPE_HINT_FULL_NAME": [
            "Iterator.next"
          ],
          "CODE": "$SecurityDemo\\FileUpload\\FileValidator->checkMagicBytes@iter_tmp-0->next()"
        }
      },
      {
        "id": 2456,
        "labels": [
          "METHOD"
        ],
        "properties": {
          "id": 111669149757,
          "FULL_NAME": "Iterator.next",
          "NAME": "next",
          "SIGNATURE": "void()",
          "GENERIC_SIGNATURE": "<empty>",
          "ORDER": 0,
          "FILENAME": "<empty>",
          "AST_PARENT_FULL_NAME": "<global>",
          "IS_EXTERNAL": true,
          "AST_PARENT_TYPE": "NAMESPACE_BLOCK",
          "CODE": "<empty>"
        }
      },
      {
        "id": 1332,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771305,
          "NAME": "next",
          "ARGUMENT_INDEX": -1,
          "DISPATCH_TYPE": "DYNAMIC_DISPATCH",
          "SIGNATURE": "void()",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 1,
          "LINE_NUMBER": 131,
          "METHOD_FULL_NAME": "Iterator.next",
          "DYNAMIC_TYPE_HINT_FULL_NAME": [
            "Iterator.next"
          ],
          "CODE": "$SecurityDemo\\FileUpload\\VulnerableFileUploader->processFileName@iter_tmp-1->next()"
        }
      },
      {
        "id": 1702,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476905,
          "NAME": "SecurityDemo\\FileUpload\\VulnerableFileUploader->processFileName@iter_tmp-1",
          "ARGUMENT_INDEX": 0,
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 1,
          "LINE_NUMBER": 131,
          "DYNAMIC_TYPE_HINT_FULL_NAME": [
            "array",
            "array-><returnValue>"
          ],
          "CODE": "$SecurityDemo\\FileUpload\\VulnerableFileUploader->processFileName@iter_tmp-1"
        }
      },
      {
        "id": 2033,
        "labels": [
          "LOCAL"
        ],
        "properties": {
          "id": 94489280548,
          "NAME": "SecurityDemo\\FileUpload\\VulnerableFileUploader->processFileName@iter_tmp-1",
          "GENERIC_SIGNATURE": "<empty>",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 2,
          "LINE_NUMBER": 131,
          "DYNAMIC_TYPE_HINT_FULL_NAME": [
            "array",
            "array-><returnValue>"
          ],
          "CODE": "$SecurityDemo\\FileUpload\\VulnerableFileUploader->processFileName@iter_tmp-1"
        }
      },
      {
        "id": 1704,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476907,
          "NAME": "SecurityDemo\\FileUpload\\VulnerableFileUploader->processFileName@iter_tmp-1",
          "ARGUMENT_INDEX": 0,
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 1,
          "LINE_NUMBER": 131,
          "DYNAMIC_TYPE_HINT_FULL_NAME": [
            "array",
            "array-><returnValue>"
          ],
          "CODE": "$SecurityDemo\\FileUpload\\VulnerableFileUploader->processFileName@iter_tmp-1"
        }
      },
      {
        "id": 1334,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771307,
          "NAME": "current",
          "ARGUMENT_INDEX": 2,
          "SIGNATURE": "<unresolvedSignature>(0)",
          "DISPATCH_TYPE": "DYNAMIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 2,
          "LINE_NUMBER": 131,
          "METHOD_FULL_NAME": "Iterator.current",
          "DYNAMIC_TYPE_HINT_FULL_NAME": [
            "Iterator.current"
          ],
          "CODE": "$SecurityDemo\\FileUpload\\VulnerableFileUploader->processFileName@iter_tmp-1->current()"
        }
      },
      {
        "id": 1333,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771306,
          "NAME": "<operator>.assignment",
          "ARGUMENT_INDEX": -1,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 2,
          "LINE_NUMBER": 131,
          "METHOD_FULL_NAME": "<operator>.assignment",
          "CODE": "$blocked = $SecurityDemo\\FileUpload\\VulnerableFileUploader->processFileName@iter_tmp-1->current()"
        }
      },
      {
        "id": 1703,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476906,
          "NAME": "blocked",
          "ARGUMENT_INDEX": 1,
          "TYPE_FULL_NAME": "Iterator.current-><returnValue>",
          "ORDER": 1,
          "LINE_NUMBER": 131,
          "CODE": "$blocked"
        }
      },
      {
        "id": 2035,
        "labels": [
          "LOCAL"
        ],
        "properties": {
          "id": 94489280550,
          "NAME": "blocked",
          "GENERIC_SIGNATURE": "<empty>",
          "TYPE_FULL_NAME": "Iterator.current-><returnValue>",
          "ORDER": 4,
          "LINE_NUMBER": 131,
          "CODE": "$blocked"
        }
      },
      {
        "id": 1701,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476904,
          "NAME": "blocked",
          "ARGUMENT_INDEX": 1,
          "TYPE_FULL_NAME": "Iterator.current-><returnValue>",
          "ORDER": 1,
          "LINE_NUMBER": 131,
          "CODE": "$blocked"
        }
      },
      {
        "id": 1331,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771304,
          "NAME": "is_null",
          "ARGUMENT_INDEX": 1,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "bool",
          "ORDER": 1,
          "LINE_NUMBER": 131,
          "METHOD_FULL_NAME": "is_null",
          "DYNAMIC_TYPE_HINT_FULL_NAME": [
            "is_null"
          ],
          "CODE": "is_null($blocked)"
        }
      },
      {
        "id": 2455,
        "labels": [
          "METHOD"
        ],
        "properties": {
          "id": 111669149756,
          "FULL_NAME": "is_null",
          "NAME": "is_null",
          "GENERIC_SIGNATURE": "<empty>",
          "ORDER": 0,
          "FILENAME": "<empty>",
          "AST_PARENT_FULL_NAME": "<global>",
          "IS_EXTERNAL": true,
          "AST_PARENT_TYPE": "NAMESPACE_BLOCK",
          "CODE": "<empty>"
        }
      },
      {
        "id": 1438,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771411,
          "NAME": "is_null",
          "ARGUMENT_INDEX": 1,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "bool",
          "ORDER": 1,
          "LINE_NUMBER": 112,
          "METHOD_FULL_NAME": "is_null",
          "DYNAMIC_TYPE_HINT_FULL_NAME": [
            "is_null"
          ],
          "CODE": "is_null($pattern)"
        }
      },
      {
        "id": 1784,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476987,
          "NAME": "pattern",
          "ARGUMENT_INDEX": 1,
          "TYPE_FULL_NAME": "Iterator.current-><returnValue>",
          "ORDER": 1,
          "LINE_NUMBER": 112,
          "CODE": "$pattern"
        }
      },
      {
        "id": 2052,
        "labels": [
          "LOCAL"
        ],
        "properties": {
          "id": 94489280567,
          "NAME": "pattern",
          "GENERIC_SIGNATURE": "<empty>",
          "TYPE_FULL_NAME": "Iterator.current-><returnValue>",
          "ORDER": 3,
          "LINE_NUMBER": 112,
          "CODE": "$pattern"
        }
      },
      {
        "id": 1789,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476992,
          "NAME": "pattern",
          "ARGUMENT_INDEX": 2,
          "TYPE_FULL_NAME": "Iterator.current-><returnValue>",
          "ORDER": 2,
          "LINE_NUMBER": 114,
          "CODE": "$pattern"
        }
      },
      {
        "id": 1443,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771416,
          "NAME": "strpos",
          "ARGUMENT_INDEX": 1,
          "SIGNATURE": "<unresolvedSignature>(2)",
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 1,
          "LINE_NUMBER": 114,
          "METHOD_FULL_NAME": "strpos",
          "DYNAMIC_TYPE_HINT_FULL_NAME": [
            "strpos"
          ],
          "CODE": "strpos($content,$pattern)"
        }
      },
      {
        "id": 1442,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771415,
          "NAME": "<operator>.notIdentical",
          "ARGUMENT_INDEX": -1,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 1,
          "LINE_NUMBER": 114,
          "METHOD_FULL_NAME": "<operator>.notIdentical",
          "CODE": "strpos($content,$pattern) !== false"
        }
      },
      {
        "id": 2449,
        "labels": [
          "METHOD"
        ],
        "properties": {
          "id": 111669149750,
          "FULL_NAME": "<operator>.notIdentical",
          "NAME": "<operator>.notIdentical",
          "GENERIC_SIGNATURE": "<empty>",
          "ORDER": 0,
          "FILENAME": "<empty>",
          "AST_PARENT_FULL_NAME": "<global>",
          "IS_EXTERNAL": true,
          "AST_PARENT_TYPE": "NAMESPACE_BLOCK",
          "CODE": "<empty>"
        }
      },
      {
        "id": 345,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771277,
          "NAME": "<operator>.notIdentical",
          "ARGUMENT_INDEX": -1,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 1,
          "LINE_NUMBER": 104,
          "METHOD_FULL_NAME": "<operator>.notIdentical",
          "CODE": "$fileData["error"] !== UPLOAD_ERR_OK"
        }
      },
      {
        "id": 346,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771278,
          "NAME": "<operator>.indexAccess",
          "ARGUMENT_INDEX": 1,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 1,
          "LINE_NUMBER": 104,
          "METHOD_FULL_NAME": "<operator>.indexAccess",
          "CODE": "$fileData["error"]"
        }
      },
      {
        "id": 1680,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476883,
          "NAME": "fileData",
          "ARGUMENT_INDEX": 1,
          "TYPE_FULL_NAME": "array",
          "ORDER": 1,
          "LINE_NUMBER": 104,
          "CODE": "$fileData"
        }
      },
      {
        "id": 2080,
        "labels": [
          "METHOD_PARAMETER_IN"
        ],
        "properties": {
          "IS_VARIADIC": false,
          "id": 115964117005,
          "INDEX": 1,
          "NAME": "fileData",
          "TYPE_FULL_NAME": "array",
          "ORDER": 1,
          "EVALUATION_STRATEGY": "BY_VALUE",
          "LINE_NUMBER": 101,
          "CODE": "$fileData"
        }
      },
      {
        "id": 1682,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476885,
          "NAME": "fileData",
          "ARGUMENT_INDEX": 1,
          "TYPE_FULL_NAME": "array",
          "ORDER": 1,
          "LINE_NUMBER": 109,
          "CODE": "$fileData"
        }
      },
      {
        "id": 349,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771281,
          "NAME": "<operator>.indexAccess",
          "ARGUMENT_INDEX": 1,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 1,
          "LINE_NUMBER": 109,
          "METHOD_FULL_NAME": "<operator>.indexAccess",
          "CODE": "$fileData["size"]"
        }
      },
      {
        "id": 348,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771280,
          "NAME": "<operator>.greaterThan",
          "ARGUMENT_INDEX": -1,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 1,
          "LINE_NUMBER": 109,
          "METHOD_FULL_NAME": "<operator>.greaterThan",
          "CODE": "$fileData["size"] > $this->maxFileSize"
        }
      },
      {
        "id": 350,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771282,
          "NAME": "<operator>.fieldAccess",
          "ARGUMENT_INDEX": 2,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 2,
          "LINE_NUMBER": 109,
          "METHOD_FULL_NAME": "<operator>.fieldAccess",
          "CODE": "$this->maxFileSize"
        }
      },
      {
        "id": 1683,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476886,
          "NAME": "this",
          "ARGUMENT_INDEX": 1,
          "TYPE_FULL_NAME": "SecurityDemo\\FileUpload\\VulnerableFileUploader",
          "ORDER": 1,
          "LINE_NUMBER": 109,
          "CODE": "$this"
        }
      },
      {
        "id": 2079,
        "labels": [
          "METHOD_PARAMETER_IN"
        ],
        "properties": {
          "IS_VARIADIC": false,
          "id": 115964117004,
          "INDEX": 0,
          "NAME": "this",
          "TYPE_FULL_NAME": "SecurityDemo\\FileUpload\\VulnerableFileUploader",
          "ORDER": 0,
          "EVALUATION_STRATEGY": "BY_SHARING",
          "LINE_NUMBER": 101,
          "DYNAMIC_TYPE_HINT_FULL_NAME": [
            "SecurityDemo\\FileUpload\\VulnerableFileUploader"
          ],
          "CODE": "this"
        }
      },
      {
        "id": 2405,
        "labels": [
          "METHOD"
        ],
        "properties": {
          "id": 111669149706,
          "NAME": "validateFileUpload",
          "FULL_NAME": "SecurityDemo\\FileUpload\\VulnerableFileUploader->validateFileUpload",
          "SIGNATURE": "<unresolvedSignature>(1)",
          "GENERIC_SIGNATURE": "<empty>",
          "ORDER": 8,
          "FILENAME": "FileUploadVulnerability.php",
          "LINE_NUMBER": 101,
          "IS_EXTERNAL": false,
          "AST_PARENT_FULL_NAME": "SecurityDemo\\FileUpload\\VulnerableFileUploader",
          "AST_PARENT_TYPE": "TYPE_DECL",
          "CODE": "PRIVATE function validateFileUpload(this,$fileData)"
        }
      },
      {
        "id": 305,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771237,
          "NAME": "validateFileUpload",
          "ARGUMENT_INDEX": 1,
          "SIGNATURE": "<unresolvedSignature>(1)",
          "DISPATCH_TYPE": "DYNAMIC_DISPATCH",
          "TYPE_FULL_NAME": "bool",
          "ORDER": 1,
          "LINE_NUMBER": 71,
          "METHOD_FULL_NAME": "SecurityDemo\\FileUpload\\VulnerableFileUploader->validateFileUpload",
          "CODE": "$this->validateFileUpload($fileData)"
        }
      },
      {
        "id": 1649,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476852,
          "NAME": "fileData",
          "ARGUMENT_INDEX": 1,
          "TYPE_FULL_NAME": "array",
          "ORDER": 2,
          "LINE_NUMBER": 71,
          "CODE": "$fileData"
        }
      },
      {
        "id": 2077,
        "labels": [
          "METHOD_PARAMETER_IN"
        ],
        "properties": {
          "IS_VARIADIC": false,
          "id": 115964117002,
          "INDEX": 1,
          "NAME": "fileData",
          "TYPE_FULL_NAME": "array",
          "ORDER": 1,
          "EVALUATION_STRATEGY": "BY_VALUE",
          "LINE_NUMBER": 67,
          "CODE": "$fileData"
        }
      },
      {
        "id": 1661,
        "labels": [
          "IDENTIFIER"
        ],
        "properties": {
          "id": 68719476864,
          "NAME": "fileData",
          "ARGUMENT_INDEX": 1,
          "TYPE_FULL_NAME": "array",
          "ORDER": 1,
          "LINE_NUMBER": 82,
          "CODE": "$fileData"
        }
      },
      {
        "id": 319,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771251,
          "NAME": "<operator>.indexAccess",
          "ARGUMENT_INDEX": 1,
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 1,
          "LINE_NUMBER": 82,
          "METHOD_FULL_NAME": "<operator>.indexAccess",
          "CODE": "$fileData["tmp_name"]"
        }
      },
      {
        "id": 318,
        "labels": [
          "CALL"
        ],
        "properties": {
          "id": 30064771250,
          "NAME": "move_uploaded_file",
          "ARGUMENT_INDEX": -1,
          "SIGNATURE": "<unresolvedSignature>(2)",
          "DISPATCH_TYPE": "STATIC_DISPATCH",
          "TYPE_FULL_NAME": "ANY",
          "ORDER": 1,
          "LINE_NUMBER": 82,
          "METHOD_FULL_NAME": "move_uploaded_file",
          "DYNAMIC_TYPE_HINT_FULL_NAME": [
            "move_uploaded_file"
          ],
          "CODE": "move_uploaded_file($fileData["tmp_name"],$targetPath)"
        }
      }
    ]
  }
]