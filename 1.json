[
    {id: 1160, properties: {GENERIC_SIGNATURE: "<empty>", ORDER: 1, CODE: "$_GET", TYPE_FULL_NAME: "ANY", LINE_NUMBER: 90, id: 94489280543, NAME: "_GET"
        }, labels: [
            "LOCAL"
        ]
    },
    {id: 947, properties: {ORDER: 1, ARGUMENT_INDEX: 1, CODE: "$_GET", TYPE_FULL_NAME: "ANY", LINE_NUMBER: 90, id: 68719476992, NAME: "_GET"
        }, labels: [
            "IDENTIFIER"
        ]
    },
    {id: 1613, properties: {LINE_NUMBER: 2, IS_EXTERNAL: FALSE, SIGNATURE: "<unresolvedSignature>(0)", NAME: "<global>", GENERIC_SIGNATURE: "<empty>", AST_PARENT_TYPE: "TYPE_DECL", ORDER: 1, AST_PARENT_FULL_NAME: "index.php:<global>", CODE: "VIRTUAL PUBLIC STATIC function <global>()", FULL_NAME: "index.php:<global>", id: 111669149732, FILENAME: "index.php"
        }, labels: [
            "METHOD"
        ]
    },
    {id: 1618, properties: {LINE_NUMBER: 74, IS_EXTERNAL: FALSE, SIGNATURE: "<unresolvedSignature>(2)", NAME: "processTransformation", GENERIC_SIGNATURE: "<empty>", AST_PARENT_TYPE: "METHOD", ORDER: 9, AST_PARENT_FULL_NAME: "index.php:<global>", CODE: "function processTransformation($target,$params)", FULL_NAME: "processTransformation", id: 111669149737, FILENAME: "index.php"
        }, labels: [
            "METHOD"
        ]
    },
    {id: 600, properties: {ORDER: 1, ARGUMENT_INDEX: 1, CODE: "processTransformation($target,$params)", METHOD_FULL_NAME: "processTransformation", TYPE_FULL_NAME: "ANY", LINE_NUMBER: 25, id: 30064771359, SIGNATURE: "<unresolvedSignature>(2)", DISPATCH_TYPE: "STATIC_DISPATCH", DYNAMIC_TYPE_HINT_FULL_NAME: [
                "processTransformation"
            ], NAME: "processTransformation"
        }, labels: [
            "CALL"
        ]
    },
    {id: 628, properties: {ORDER: 1, ARGUMENT_INDEX: 1, CODE: "$processor->execute($target,$params)", METHOD_FULL_NAME: "CoreProcessor->execute", TYPE_FULL_NAME: "CoreProcessor->execute-><returnValue>", LINE_NUMBER: 86, id: 30064771387, SIGNATURE: "<unresolvedSignature>(2)", DISPATCH_TYPE: "DYNAMIC_DISPATCH", NAME: "execute"
        }, labels: [
            "CALL"
        ]
    },
    {id: 1526, properties: {ORDER: 5, CODE: "RET", TYPE_FULL_NAME: "ANY", LINE_NUMBER: 74, EVALUATION_STRATEGY: "BY_VALUE", id: 128849018920, DYNAMIC_TYPE_HINT_FULL_NAME: [
                "CoreProcessor->execute-><returnValue>"
            ]
        }, labels: [
            "METHOD_RETURN"
        ]
    },
    {id: 619, properties: {ORDER: 1, ARGUMENT_INDEX: 1, CODE: "strpos($target,\"safe_\")", METHOD_FULL_NAME: "strpos", TYPE_FULL_NAME: "ANY", LINE_NUMBER: 78, id: 30064771378, SIGNATURE: "<unresolvedSignature>(2)", DISPATCH_TYPE: "STATIC_DISPATCH", DYNAMIC_TYPE_HINT_FULL_NAME: [
                "strpos"
            ], NAME: "strpos"
        }, labels: [
            "CALL"
        ]
    },
    {id: 1631, properties: {GENERIC_SIGNATURE: "<empty>", AST_PARENT_TYPE: "NAMESPACE_BLOCK", ORDER: 0, AST_PARENT_FULL_NAME: "<global>", CODE: "<empty>", FULL_NAME: "strpos", id: 111669149750, IS_EXTERNAL: TRUE, SIGNATURE: "<unresolvedSignature>(2)", FILENAME: "<empty>", NAME: "strpos"
        }, labels: [
            "METHOD"
        ]
    },
    {id: 466, properties: {ORDER: 1, ARGUMENT_INDEX: 1, CODE: "strpos($actualFunction,\"bypass_\")", METHOD_FULL_NAME: "strpos", TYPE_FULL_NAME: "ANY", LINE_NUMBER: 104, id: 30064771225, SIGNATURE: "<unresolvedSignature>(2)", DISPATCH_TYPE: "STATIC_DISPATCH", DYNAMIC_TYPE_HINT_FULL_NAME: [
                "strpos"
            ], NAME: "strpos"
        }, labels: [
            "CALL"
        ]
    },
    {id: 1599, properties: {LINE_NUMBER: 93, IS_EXTERNAL: FALSE, SIGNATURE: "<unresolvedSignature>(2)", NAME: "processSecureTarget", GENERIC_SIGNATURE: "<empty>", AST_PARENT_TYPE: "TYPE_DECL", ORDER: 6, AST_PARENT_FULL_NAME: "CoreProcessor", CODE: "PRIVATE function processSecureTarget(this,$target,$params)", FULL_NAME: "CoreProcessor->processSecureTarget", id: 111669149718, FILENAME: "Processor.php"
        }, labels: [
            "METHOD"
        ]
    },
    {id: 427, properties: {ORDER: 1, ARGUMENT_INDEX: 1, CODE: "$this->processSecureTarget($target,$params)", METHOD_FULL_NAME: "CoreProcessor->processSecureTarget", TYPE_FULL_NAME: "ANY", LINE_NUMBER: 42, id: 30064771186, SIGNATURE: "<unresolvedSignature>(2)", DISPATCH_TYPE: "DYNAMIC_DISPATCH", NAME: "processSecureTarget"
        }, labels: [
            "CALL"
        ]
    },
    {id: 815, properties: {ORDER: 1, ARGUMENT_INDEX: 1, CODE: "$actualFunction", TYPE_FULL_NAME: "string", LINE_NUMBER: 113, id: 68719476860, NAME: "actualFunction"
        }, labels: [
            "IDENTIFIER"
        ]
    },
    {id: 470, properties: {ORDER: 1, ARGUMENT_INDEX: 1, CODE: "call_user_func($actualFunction,$params)", METHOD_FULL_NAME: "call_user_func", TYPE_FULL_NAME: "ANY", LINE_NUMBER: 113, id: 30064771229, SIGNATURE: "<unresolvedSignature>(2)", DISPATCH_TYPE: "STATIC_DISPATCH", DYNAMIC_TYPE_HINT_FULL_NAME: [
                "call_user_func"
            ], NAME: "call_user_func"
        }, labels: [
            "CALL"
        ]
    }
]