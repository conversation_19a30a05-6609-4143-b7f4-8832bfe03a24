// Four-segment stitched taint path with nodes-array output
// Segments: A source->processTransformation, B processTransformation->execute, C execute->processSecureTarget, D processSecureTarget->sink-arg
// Relationship filters unchanged: REACHING_DEF/ARGUMENT/PARAMETER_LINK/CALL/CROSS_FILE_CALL/RECEIVER

WITH $srcName AS srcName, $snkName AS snkName

// Anchors (call sites and sink arguments, and sink CALL node for appending)
MATCH (callPT:CALL)
WHERE callPT.NAME='processTransformation' OR callPT.METHOD_FULL_NAME='processTransformation'
WITH collect(callPT) AS ptCalls, srcName, snkName
MATCH (execCall:CALL {METHOD_FULL_NAME:'CoreProcessor->execute'})
WITH ptCalls, collect(execCall) AS execCalls, srcName, snkName
MATCH (pstCall:CALL {METHOD_FULL_NAME:'CoreProcessor->processSecureTarget'})
WITH ptCalls, execCalls, collect(pstCall) AS pstCalls, srcName, snkName
MATCH (snk:CALL)
WHERE snk.NAME=snkName OR snk.METHOD_FULL_NAME=snkName
WITH ptCalls, execCalls, pstCalls, collect(snk) AS snkCalls, srcName, snkName
MATCH (snk2:CALL)
WHERE snk2.NAME=snkName OR snk2.METHOD_FULL_NAME=snkName
MATCH (snk2)-[:ARGUMENT]->(sinkArg)
WHERE sinkArg.ARGUMENT_INDEX IN [1,2] AND NOT 'LITERAL' IN labels(sinkArg)
WITH ptCalls, execCalls, pstCalls, snkCalls, collect(sinkArg) AS sinkArgs, srcName, snkName

// Source candidates inside handleRequest
MATCH (mHR:METHOD {FULL_NAME:'handleRequest'})-[:CONTAINS]->(src)
WHERE (src:CALL AND src.METHOD_FULL_NAME IN ['<operator>.indexAccess','<operator>.indirectIndexAccess'] AND src.CODE CONTAINS '$_GET[')
   OR (src:IDENTIFIER AND src.NAME=srcName)
WITH collect(src) AS sources, ptCalls, execCalls, pstCalls, sinkArgs, snkCalls, srcName, snkName

UNWIND sources AS s

// Segment A: source -> processTransformation call site
CALL apoc.path.expandConfig(s, {
  endNodes: ptCalls,
  minLevel: 1,
  maxLevel: 120,
  bfs: true,
  uniqueness: 'NODE_GLOBAL',
  relationshipFilter: 'REACHING_DEF>|ARGUMENT>|PARAMETER_LINK>|CALL>|CROSS_FILE_CALL>|RECEIVER>' ,
  labelFilter: '+IDENTIFIER|+LOCAL|+CALL|+METHOD|+METHOD_PARAMETER_IN|+METHOD_PARAMETER_OUT|+METHOD_RETURN|+LITERAL'
}) YIELD path AS pA
WITH s, pA, last(nodes(pA)) AS ptCallNode, execCalls, pstCalls, sinkArgs, snkCalls
WHERE any(r IN relationships(pA) WHERE type(r)='REACHING_DEF')

// Segment B: processTransformation call site -> CoreProcessor->execute call
CALL apoc.path.expandConfig(ptCallNode, {
  endNodes: execCalls,
  minLevel: 1,
  maxLevel: 120,
  bfs: true,
  uniqueness: 'NODE_GLOBAL',
  relationshipFilter: 'PARAMETER_LINK>|REACHING_DEF>|ARGUMENT>|CALL>|CROSS_FILE_CALL>|RECEIVER>' ,
  labelFilter: '+IDENTIFIER|+LOCAL|+CALL|+METHOD|+METHOD_PARAMETER_IN|+METHOD_PARAMETER_OUT|+METHOD_RETURN|+LITERAL'
}) YIELD path AS pB
WITH s, pA, pB, last(nodes(pB)) AS execCallNode, pstCalls, sinkArgs, snkCalls
WHERE any(r IN relationships(pB) WHERE type(r)='REACHING_DEF')

// Segment C: execute call -> processSecureTarget call
CALL apoc.path.expandConfig(execCallNode, {
  endNodes: pstCalls,
  minLevel: 1,
  maxLevel: 120,
  bfs: true,
  uniqueness: 'NODE_GLOBAL',
  relationshipFilter: 'REACHING_DEF>|ARGUMENT>|PARAMETER_LINK>|CALL>|CROSS_FILE_CALL>|RECEIVER>' ,
  labelFilter: '+IDENTIFIER|+LOCAL|+CALL|+METHOD|+METHOD_PARAMETER_IN|+METHOD_PARAMETER_OUT|+METHOD_RETURN|+LITERAL'
}) YIELD path AS pC
WITH s, pA, pB, pC, last(nodes(pC)) AS pstCallNode, sinkArgs, snkCalls
WHERE any(r IN relationships(pC) WHERE type(r)='REACHING_DEF')

// Segment D: from processSecureTarget call -> sink args (inside method body)
CALL apoc.path.expandConfig(pstCallNode, {
  endNodes: sinkArgs,
  minLevel: 1,
  maxLevel: 160,
  bfs: true,
  uniqueness: 'NODE_GLOBAL',
  relationshipFilter: 'CALL>|REACHING_DEF>|ARGUMENT>|PARAMETER_LINK>|RECEIVER>' ,
  labelFilter: '+IDENTIFIER|+LOCAL|+CALL|+METHOD|+METHOD_PARAMETER_IN|+METHOD_PARAMETER_OUT|+METHOD_RETURN|+LITERAL'
}) YIELD path AS pD
WITH s, pA, pB, pC, pD, snkCalls
WHERE any(r IN relationships(pD) WHERE type(r)='REACHING_DEF')

// Merge all segment nodes and append sink CALL node
WITH nodes(pA)+nodes(pB)+nodes(pC)+nodes(pD) AS mergedNodes, snkCalls
WITH mergedNodes + [head(snkCalls)] AS allNodes
RETURN [n IN allNodes | { id: id(n), labels: labels(n), properties: properties(n) }] AS nodes
LIMIT 5;
