<?php
/**
 * 文件上传处理入口 - 用于安全研究和教学目的
 * 
 * 警告：此代码包含故意设计的安全漏洞，仅用于教学演示
 * 请勿在生产环境中使用此代码
 */

namespace SecurityDemo\FileUpload;

require_once 'FileUploadVulnerability.php';
require_once 'FileValidator.php';

#declare(strict_types=1);

/**
 * 文件上传控制器类
 * 处理HTTP请求并协调文件上传流程
 */
class FileUploadController
{
    private VulnerableFileUploader $uploader;
    private FileValidator $validator;

    public function __construct()
    {
        $this->uploader = new VulnerableFileUploader();
        $this->validator = new FileValidator();
    }

    /**
     * 处理POST请求的文件上传
     * Source Points: $_POST, $_GET, $_FILES
     */
    public function handleRequest(): array
    {
        // Source Point 1: $_POST数据
        $action = $_POST['action'] ?? $_GET['action'] ?? 'upload';
        
        if ($action === 'upload') {
            return $this->processUpload();
        } elseif ($action === 'batch') {
            return $this->processBatchUpload();
        }
        
        return ['success' => false, 'message' => '未知操作'];
    }

    /**
     * 处理单文件上传
     */
    private function processUpload(): array
    {
        if (!isset($_FILES['file'])) {
            return ['success' => false, 'message' => '未找到上传文件'];
        }

        // Source Point 2: 用户可控的文件名参数
        $customName = $_POST['filename'] ?? $_GET['filename'] ?? '';
        
        // Source Point 3: 用户可控的目录参数（路径遍历风险）
        $subDir = $_POST['subdir'] ?? $_GET['subdir'] ?? '';
        
        if (!empty($subDir)) {
            // 漏洞：子目录参数未充分过滤
            $this->uploader = new VulnerableFileUploader('./uploads/' . $subDir . '/');
        }

        return $this->uploader->handleUpload($_FILES['file'], $customName);
    }

    /**
     * 处理批量文件上传
     */
    private function processBatchUpload(): array
    {
        if (!isset($_FILES['files'])) {
            return ['success' => false, 'message' => '未找到批量上传文件'];
        }

        $results = [];
        $files = $_FILES['files'];
        
        // 处理多文件上传
        for ($i = 0; $i < count($files['name']); $i++) {
            $fileData = [
                'name' => $files['name'][$i],
                'type' => $files['type'][$i],
                'tmp_name' => $files['tmp_name'][$i],
                'error' => $files['error'][$i],
                'size' => $files['size'][$i]
            ];
            
            // Source Point 4: 批量上传中的自定义文件名
            $customName = $_POST['filenames'][$i] ?? '';
            
            $result = $this->uploader->handleUpload($fileData, $customName);
            $results[] = $result;
        }
        
        return ['success' => true, 'results' => $results];
    }

    /**
     * 输出JSON响应
     */
    public function sendResponse(array $data): void
    {
        header('Content-Type: application/json');
        echo json_encode($data, JSON_UNESCAPED_UNICODE);
    }
}

// 处理请求
if ($_SERVER['REQUEST_METHOD'] === 'POST' || !empty($_GET['action'])) {
    $controller = new FileUploadController();
    $result = $controller->handleRequest();
    $controller->sendResponse($result);
} else {
    // 显示简单的上传表单用于测试
    echo '<!DOCTYPE html>
<html>
<head><title>文件上传测试 - 仅用于安全研究</title></head>
<body>
<h2>文件上传漏洞演示</h2>
<p style="color:red;">警告：此页面包含安全漏洞，仅用于教学目的</p>

<h3>单文件上传</h3>
<form method="post" enctype="multipart/form-data">
    <input type="hidden" name="action" value="upload">
    文件: <input type="file" name="file"><br>
    自定义文件名: <input type="text" name="filename" placeholder="可选"><br>
    子目录: <input type="text" name="subdir" placeholder="可选"><br>
    <input type="submit" value="上传">
</form>

<h3>批量上传</h3>
<form method="post" enctype="multipart/form-data">
    <input type="hidden" name="action" value="batch">
    文件: <input type="file" name="files[]" multiple><br>
    <input type="submit" value="批量上传">
</form>
</body>
</html>';
}
