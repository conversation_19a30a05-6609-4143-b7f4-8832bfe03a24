<?php
/**
 * 文件上传漏洞示例 - 用于安全研究和教学目的
 * 
 * 警告：此代码包含故意设计的安全漏洞，仅用于教学演示
 * 请勿在生产环境中使用此代码
 * 
 * <AUTHOR> Research Team
 * @purpose Educational demonstration of file upload vulnerabilities
 */

namespace SecurityDemo\FileUpload;

#declare(strict_types=1);

/**
 * 存在漏洞的文件上传处理类
 * 
 * 漏洞类型：
 * 1. 文件类型验证绕过（仅检查MIME类型，可伪造）
 * 2. 文件扩展名过滤不完整
 * 3. 文件名未充分过滤，存在路径遍历风险
 * 4. 上传目录可执行PHP文件
 */
class VulnerableFileUploader
{
    private string $uploadDir;
    private array $allowedMimeTypes;
    private array $blockedExtensions;
    private int $maxFileSize;

    public function __construct(string $uploadDir = './uploads/')
    {
        $this->uploadDir = $uploadDir;
        // 漏洞1：仅依赖MIME类型验证，可以被伪造
        $this->allowedMimeTypes = [
            'image/jpeg',
            'image/png', 
            'image/gif',
            'text/plain'
        ];
        // 漏洞2：黑名单不完整，遗漏了.phtml, .php5等扩展名
        $this->blockedExtensions = ['exe', 'bat'];
        $this->maxFileSize = 5 * 1024 * 1024; // 5MB
        
        $this->createUploadDirectory();
    }

    /**
     * 创建上传目录（漏洞：目录可执行PHP）
     */
    private function createUploadDirectory(): void
    {
        if (!is_dir($this->uploadDir)) {
            mkdir($this->uploadDir, 0755, true);
            // 漏洞3：未创建.htaccess禁止PHP执行
        }
    }

    /**
     * 处理文件上传 - 主要漏洞入口点
     * 
     * @param array $fileData $_FILES数组数据
     * @param string $customName 自定义文件名（用户可控）
     * @return array 上传结果
     */
    public function handleUpload(array $fileData, string $customName = ''): array
    {
        try {
            // Source Point 1: $_FILES数据
            if (!$this->validateFileUpload($fileData)) {
                return ['success' => false, 'message' => '文件验证失败'];
            }

            // Source Point 2: 用户可控的自定义文件名
            $fileName = $this->processFileName($fileData['name'], $customName);
            
            // 漏洞4：文件名过滤不充分，存在路径遍历
            $targetPath = $this->uploadDir . $fileName;
            
            // Sink Point: move_uploaded_file - 危险函数调用点
            if (move_uploaded_file($fileData['tmp_name'], $targetPath)) {
                return [
                    'success' => true, 
                    'message' => '文件上传成功',
                    'path' => $targetPath,
                    'url' => str_replace('./', '', $targetPath)
                ];
            }
            
            return ['success' => false, 'message' => '文件移动失败'];
            
        } catch (\Exception $e) {
            return ['success' => false, 'message' => '上传异常: ' . $e->getMessage()];
        }
    }

    /**
     * 验证上传文件（存在绕过漏洞）
     */
    private function validateFileUpload(array $fileData): bool
    {
        // 基本检查
        if ($fileData['error'] !== UPLOAD_ERR_OK) {
            return false;
        }
        
        // 文件大小检查
        if ($fileData['size'] > $this->maxFileSize) {
            return false;
        }
        
        
        return true;
    }

    /**
     * 处理文件名（存在多个漏洞）
     */
    private function processFileName(string $originalName, string $customName): string
    {
        $fileName = !empty($customName) ? $customName : $originalName;
        
        // 漏洞：路径遍历过滤不完整
        $fileName = str_replace(['../', '..\\'], '', $fileName);
        
        // 漏洞：扩展名黑名单绕过
        $extension = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));
        
        // 简单的黑名单检查（可绕过）
        foreach ($this->blockedExtensions as $blocked) {
            if ($extension === $blocked) {
                // 漏洞：仅添加.txt后缀，双扩展名绕过
                $fileName .= '.txt';
                break;
            }
        }
        
        return $fileName;
    }
}
