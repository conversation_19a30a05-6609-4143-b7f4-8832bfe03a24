<?php
/**
 * 文件验证类 - 用于安全研究和教学目的
 * 
 * 警告：此类包含故意设计的验证绕过漏洞
 * 仅用于教学演示，请勿在生产环境使用
 */

namespace SecurityDemo\FileUpload;

#declare(strict_types=1);

/**
 * 存在漏洞的文件验证器
 * 
 * 设计的漏洞：
 * 1. 魔术字节检查不完整
 * 2. 文件内容检查可绕过
 * 3. 扩展名验证存在逻辑缺陷
 */
class FileValidator
{
    private array $imageMagicBytes;
    private array $dangerousPatterns;

    public function __construct()
    {
        // 图片文件的魔术字节
        $this->imageMagicBytes = [
            'jpeg' => [0xFF, 0xD8, 0xFF],
            'png' => [0x89, 0x50, 0x4E, 0x47],
            'gif' => [0x47, 0x49, 0x46, 0x38]
        ];
        
        // 危险内容模式（检查不完整）
        $this->dangerousPatterns = [
            '<?php',
            '<?=',
            '<script',
            'eval(',
            'system(',
            'exec('
        ];
    }

    /**
     * 验证文件是否为合法图片（存在绕过漏洞）
     * 
     * @param string $filePath 文件路径
     * @return bool 验证结果
     */
    public function validateImageFile(string $filePath): bool
    {
        if (!file_exists($filePath)) {
            return false;
        }

        // 漏洞1：魔术字节检查可以被绕过
        if (!$this->checkMagicBytes($filePath)) {
            return false;
        }

        // 漏洞2：内容检查不完整，可以通过编码绕过
        if (!$this->checkFileContent($filePath)) {
            return false;
        }

        return true;
    }

    /**
     * 检查文件魔术字节（存在绕过漏洞）
     */
    private function checkMagicBytes(string $filePath): bool
    {
        $handle = fopen($filePath, 'rb');
        if (!$handle) {
            return false;
        }

        $header = fread($handle, 8);
        fclose($handle);

        // 漏洞：只检查前几个字节，攻击者可以在合法图片头后插入恶意代码
        foreach ($this->imageMagicBytes as $type => $bytes) {
            $match = true;
            for ($i = 0; $i < count($bytes); $i++) {
                if (!isset($header[$i]) || ord($header[$i]) !== $bytes[$i]) {
                    $match = false;
                    break;
                }
            }
            if ($match) {
                return true;
            }
        }

        return false;
    }

    /**
     * 检查文件内容（存在多个绕过方法）
     */
    private function checkFileContent(string $filePath): bool
    {
        $content = file_get_contents($filePath);
        if ($content === false) {
            return false;
        }

        // 漏洞：简单的字符串匹配，可以通过多种方式绕过
        foreach ($this->dangerousPatterns as $pattern) {
            // 漏洞1：区分大小写，可以用大写绕过
            if (strpos($content, $pattern) !== false) {
                return false;
            }
        }

        // 漏洞2：未检查base64编码的恶意代码
        // 漏洞3：未检查十六进制编码的恶意代码
        // 漏洞4：未检查注释中的恶意代码

        return true;
    }

    /**
     * 验证文件扩展名（存在逻辑漏洞）
     * 
     * @param string $fileName 文件名
     * @return bool 验证结果
     */
    public function validateFileExtension(string $fileName): bool
    {
        $extension = $this->getFileExtension($fileName);
        
        // 漏洞：允许的扩展名列表不完整
        $allowedExtensions = ['jpg', 'jpeg', 'png', 'gif', 'txt'];
        
        return in_array($extension, $allowedExtensions);
    }

    /**
     * 获取文件扩展名（存在解析漏洞）
     */
    private function getFileExtension(string $fileName): string
    {
        // 漏洞：只取最后一个点后的内容，忽略双扩展名攻击
        // 例如：shell.php.jpg 会被识别为 jpg
        $parts = explode('.', $fileName);
        return strtolower(end($parts));
    }

    /**
     * 生成安全的文件名（存在不完整过滤）
     * 
     * @param string $originalName 原始文件名
     * @return string 处理后的文件名
     */
    public function sanitizeFileName(string $originalName): string
    {
        // 漏洞：过滤不完整，存在多种绕过方式
        $fileName = $originalName;
        
        // 移除一些危险字符（但不完整）
        $fileName = str_replace(['../', '..\\', '<', '>', '|'], '', $fileName);
        
        // 漏洞：未过滤空字节、特殊Unicode字符等
        
        return $fileName;
    }
}
