WITH "_GET" AS pSrc, "move_uploaded_file" AS pSnk
CALL apoc.cypher.runTimeboxed("
WITH $srcName AS srcName, $snkName AS snkName

// 1) sink 与所有参数候选（排除 RETURN / METHOD_RETURN / LITERAL）
MATCH (snk:CALL)
WHERE snk.NAME = snkName OR snk.METHOD_FULL_NAME = snkName
MATCH (snk)-[:ARGUMENT]-(cand)
WITH snk, srcName, [a IN collect(DISTINCT cand) WHERE NOT 'LITERAL' IN labels(a)
                                                AND NOT 'RETURN' IN labels(a)
                                                AND NOT 'METHOD_RETURN' IN labels(a)] AS sinkArgs

// 2) 源集合（保持不变）
MATCH (base)
WHERE (base:IDENTIFIER AND base.NAME = srcName)
   OR (base:LOCAL AND base.NAME = srcName)
   OR (base:CALL AND base.METHOD_FULL_NAME = '<operator>.indexAccess'
       AND exists( (base)-[:ARGUMENT]->(:IDENTIFIER {NAME: srcName}) ))
WITH snk, sinkArgs, collect(base) AS sources

// 3) 每个 sinkArg 独立搜索（DFS + endNodes: [arg]）
UNWIND sinkArgs AS arg
UNWIND sources AS s
CALL apoc.path.expandConfig(s, {
  endNodes: [arg],
  relationshipFilter: 'REF|ARGUMENT|PARAMETER_LINK|REACHING_DEF|RECEIVER|CALL|CROSS_FILE_CALL>',
  minLevel: 1,
  maxLevel: 800,
  bfs: false,                       // DFS
  uniqueness: 'NODE_GLOBAL',
  labelFilter: '+IDENTIFIER|+LOCAL|+CALL|+METHOD|+METHOD_PARAMETER_IN|+METHOD_PARAMETER_OUT|-METHOD_RETURN|-LITERAL'
}) YIELD path

// 4) 必须终到该 arg，且路径中至少含一次 REACHING_DEF
WITH snk, arg, path
WHERE last(nodes(path)) = arg
  AND any(r IN relationships(path) WHERE type(r)='REACHING_DEF')

// 5) 对“每个 sink”合并所有参数路径，去重后选代表路径（以 snk 为分组键；代表路径取最长）
ORDER BY length(path) DESC
WITH snk, collect(path) AS allPaths
// 5.1) 基于节点集合签名去重
UNWIND allPaths AS p
WITH snk, p, apoc.coll.sort([n IN nodes(p) | id(n)]) AS nodeIdsSorted
WITH snk, apoc.text.join([x IN nodeIdsSorted | toString(x)], '|') AS sig, p
WITH snk, sig, collect(p) AS groupPaths
WITH snk, collect({sig: sig, path: head(groupPaths)}) AS uniquePaths
// 5.2) 从去重后的路径中选择最长的作为代表路径
UNWIND uniquePaths AS up
WITH snk, up.path AS p, size(nodes(up.path)) AS plen
WITH snk, max(plen) AS maxLen, collect({p: p, len: plen}) AS pathEntries
WITH snk, [e IN pathEntries WHERE e.len = maxLen][0].p AS bestForSink

// 6) 输出：每个 sink 一行，nodes 追加 sink CALL 节点
WITH [n IN nodes(bestForSink) | { id:id(n), labels:labels(n), properties:properties(n) }] AS pathNodes, snk
RETURN pathNodes + [{ id:id(snk), labels:labels(snk), properties:properties(snk) }] AS nodes
", {srcName: pSrc, snkName: pSnk}, 12000) YIELD value
RETURN value.nodes AS nodes;