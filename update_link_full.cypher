WITH "_POST" AS pSrc, "move_uploaded_file" AS pSnk
CALL apoc.cypher.runTimeboxed("
WITH $srcName AS srcName, $snkName AS snkName

// 1) sink 与所有参数候选（排除 RETURN / METHOD_RETURN / LITERAL）
MATCH (snk:CALL)
WHERE snk.NAME = snkName OR snk.METHOD_FULL_NAME = snkName
MATCH (snk)-[:ARGUMENT]->(cand)
WITH snk, srcName, [a IN collect(DISTINCT cand) WHERE NOT 'LITERAL' IN labels(a)
                                                AND NOT 'RETURN' IN labels(a)
                                                AND NOT 'METHOD_RETURN' IN labels(a)] AS sinkArgs

// 2) 源集合（保持不变）
MATCH (base)
WHERE (base:IDENTIFIER AND base.NAME = srcName)
   OR (base:LOCAL AND base.NAME = srcName)
   OR (base:CALL AND base.METHOD_FULL_NAME = '<operator>.indexAccess'
       AND exists( (base)-[:ARGUMENT]->(:IDENTIFIER {NAME: srcName}) ))
WITH snk, sinkArgs, collect(base) AS sources

// 3) 每个 sinkArg 独立搜索（DFS + endNodes: [arg]），并避免路径跨越到其他参数或 sink 本身
UNWIND sinkArgs AS arg
WITH snk, sinkArgs, sources, arg, [x IN sinkArgs WHERE x <> arg] AS otherArgs
UNWIND sources AS s
CALL apoc.path.expandConfig(s, {
  endNodes: [arg],
  terminatorNodes: otherArgs,
  blacklistNodes: [snk],
  relationshipFilter: 'REF|ARGUMENT|PARAMETER_LINK|REACHING_DEF|RECEIVER|CALL|CROSS_FILE_CALL>',
  minLevel: 1,
  maxLevel: 800,
  bfs: false,                       // DFS
  uniqueness: 'NODE_GLOBAL',
  labelFilter: '+IDENTIFIER|+LOCAL|+CALL|+METHOD|+METHOD_PARAMETER_IN|+METHOD_PARAMETER_OUT|-METHOD_RETURN|-LITERAL'
}) YIELD path

// 4) 必须终到该 arg，且路径中至少含一次 REACHING_DEF
WITH snk, arg, s, path
WHERE last(nodes(path)) = arg
  AND any(r IN relationships(path) WHERE type(r)='REACHING_DEF')
  AND none(n IN nodes(path) WHERE id(n) = id(snk))
  AND none(x IN nodes(path) WHERE x <> arg AND exists( (snk)-[:ARGUMENT]->(x) ))

// 5) 每个 arg 仅保留一条最长路径（不使用 ORDER BY / 无 lambda，显式计算 max 长度）
WITH arg, collect(path) AS paths
WITH arg, [p IN paths WHERE p IS NOT NULL] AS paths
WHERE size(paths) > 0
UNWIND paths AS p
WITH arg, p, size(nodes(p)) AS plen
WITH arg, max(plen) AS maxLen, collect({p: p, plen: plen}) AS entries
WITH arg, [e IN entries WHERE e.plen = maxLen][0].p AS bestPath
MATCH (sinkCall:CALL)-[:ARGUMENT]->(arg)
WITH [n IN nodes(bestPath) | { id:id(n), labels:labels(n), properties:properties(n) }] AS pathNodes, sinkCall
RETURN pathNodes + [{ id:id(sinkCall), labels:labels(sinkCall), properties:properties(sinkCall) }] AS nodes
", {srcName: pSrc, snkName: pSnk}, 12000) YIELD value
RETURN value.nodes AS nodes;