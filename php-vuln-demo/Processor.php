<?php

/**
 * 核心处理器类 - 包含唯一真正的漏洞点
 * 这个类是整个调用链的最后一环，包含真正可被利用的call_user_func
 */
class CoreProcessor {
    private $transformationMap;
    private $securityBypass = false;
    
    public function __construct() {
        // 预定义的转换映射表，看似安全
        $this->transformationMap = [
            'safe_demo' => 'performSafeDemo',
            'safe_info' => 'performSafeInfo',
            'safe_format' => 'performSafeFormat',
            'transform_data' => 'transformData',
            'transform_string' => 'transformString',
            'transform_array' => 'transformArray'
        ];
    }
    
    /**
     * 主执行方法 - 最终的漏洞触发点
     * 这里包含复杂的绕过逻辑，攻击者需要理解多层绕过机制
     */
    public function execute($target, $params) {
        #echo  "[CORE] CoreProcessor executing: $target<br>";
        
        // 第一层防护：检查预定义映射
        if (isset($this->transformationMap[$target])) {
            $method = $this->transformationMap[$target];
            #echo  "[CORE] Using mapped method: $method<br>";
            return $this->$method($params);
        }
        
        // 第二层：看似安全的前缀检查，但存在绕过可能
        if ($this->isSecureTarget($target)) {
            #echo  "[CORE] Target passed security check<br>";
            
            // 关键漏洞点：这里可能被绕过导致任意函数调用
            return $this->processSecureTarget($target, $params);
        }
        
        return "Target not allowed by security policy: $target";
    }
    
    /**
     * 安全目标检查 - 看似严格但存在逻辑漏洞
     * 攻击者可以通过理解这个逻辑来构造绕过
     */
    private function isSecureTarget($target) {
        #echo  "[SECURITY] Checking secure target: $target<br>";
        
        // 检查1：必须以safe_开头（但在index.php中已经被添加了前缀）
        if (strpos($target, 'safe_') !== 0) {
            #echo  "[SECURITY] Missing safe_ prefix<br>";
            return false;
        }
        
        // 检查2：不能包含危险关键词（但bypass模式下跳过此检查）
        if (strpos($target, 'safe_bypass_') !== 0) {
            $dangerousKeywords = ['exec', 'system', 'shell', 'eval', 'file_get_contents'];
            foreach ($dangerousKeywords as $keyword) {
                if (strpos($target, $keyword) !== false) {
                    #echo  "[SECURITY] Dangerous keyword detected: $keyword<br>";
                    return false;
                }
            }
        } else {
            #echo  "[SECURITY] Bypass mode - skipping keyword check<br>";
        }
        
        // 检查3：长度限制
        if (strlen($target) > 100) {
            #echo  "[SECURITY] Target too long<br>";
            return false;
        }
        
        // 关键的绕过点：如果目标包含特定的"bypass"标记，启用旁路模式
        if (strpos($target, 'safe_bypass_') === 0) {
            #echo  "[SECURITY] Bypass mode detected - enabling advanced processing<br><br>";
            $this->securityBypass = true;
        }
        
        return true;
    }
    
    /**
     * 处理安全目标 - 包含真正的漏洞点！
     * 这是整个项目中唯一真正危险的call_user_func调用
     */
    private function processSecureTarget($target, $params) {
        #echo  "[PROCESS] Processing secure target: $target<br>";
        
        // 移除safe_前缀获取实际的函数名
        $actualFunction = substr($target, 5); // 移除'safe_'前缀
        
        // 如果启用了bypass模式，进行更复杂的处理
        if ($this->securityBypass) {
            #echo  "[BYPASS] Advanced processing mode enabled<br>";
            
            // 进一步移除bypass_前缀
            if (strpos($actualFunction, 'bypass_') === 0) {
                $actualFunction = substr($actualFunction, 7); // 移除'bypass_' (7个字符)
                #echo  "[BYPASS] Final function name: $actualFunction<br>";
                
                // 这里是真正的漏洞点！
                // 攻击者可以通过构造 safe_bypass_system 这样的target来执行system函数
                if (function_exists($actualFunction)) {
                    #echo  "[CRITICAL] Executing function: $actualFunction<br>";
                    // *** 这就是唯一真正的漏洞点 ***
                    return call_user_func($actualFunction, $params);
                } else {
                    return "Function does not exist: $actualFunction";
                }
            }
        }
        
        // 默认的安全处理
        return $this->performDefaultProcessing($actualFunction, $params);
    }
    
    /**
     * 默认处理 - 安全的实现
     */
    private function performDefaultProcessing($funcName, $params) {
        #echo  "[DEFAULT] Default processing for: $funcName<br>";
        return "Default processing completed for $funcName with params: " . htmlspecialchars($params);
    }
    
    /**
     * 安全演示方法
     */
    private function performSafeDemo($params) {
        return "Safe demo executed with: " . htmlspecialchars($params);
    }
    
    /**
     * 安全信息方法
     */
    private function performSafeInfo($params) {
        return "Safe info: PHP " . PHP_VERSION . ", Params: " . htmlspecialchars($params);
    }
    
    /**
     * 安全格式化方法
     */
    private function performSafeFormat($params) {
        return "Safely formatted: " . strtoupper(htmlspecialchars($params));
    }
    
    /**
     * 数据转换方法
     */
    private function transformData($params) {
        return "Data transformed: " . base64_encode($params);
    }
}

?>
