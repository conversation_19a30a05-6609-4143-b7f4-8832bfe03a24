WITH "_GET" AS pSrc, "call_user_func" AS pSnk
CALL apoc.cypher.runTimeboxed("
WITH $srcName AS srcName, $snkName AS snkName

// sink 与其第1参数（非字面量）
MATCH (snk:CALL)
WHERE snk.NAME = snkName OR snk.METHOD_FULL_NAME = snkName
MATCH (snk)-[:ARGUMENT]->(arg)
WHERE arg.ARGUMENT_INDEX = 1 AND NOT 'LITERAL' IN labels(arg)
WITH arg, srcName, snkName, snk

// 源集合（保持不变）
MATCH (base)
WHERE (base:IDENTIFIER AND base.NAME = srcName)
   OR (base:LOCAL AND base.NAME = srcName)
   OR (base:CALL AND base.METHOD_FULL_NAME = '<operator>.indexAccess'
       AND exists( (base)-[:ARGUMENT]->(:IDENTIFIER {NAME: srcName}) ))

// 路径扩展：加入 CALL / CONTAINS，打通跨方法与包含关系
CALL apoc.path.expandConfig(base, {
  endNodes: [arg],
  relationshipFilter: 'REF>|ARGUMENT>|PARAMETER_LINK>|REACHING_DEF>|RECEIVER>|CALL|CONTAINS>|POST_DOMINATE',
  minLevel: 1,
  maxLevel: 400,
  bfs: false,
  uniqueness: 'NODE_GLOBAL',
  labelFilter: '+IDENTIFIER|+LOCAL|+CALL|+METHOD|+METHOD_PARAMETER_IN|+METHOD_PARAMETER_OUT|+METHOD_RETURN|'
}) YIELD path

// 至少含一次 REACHING_DEF 且终点为 sink 实参
WITH arg, path, snkName
WHERE last(nodes(path)) = arg
  AND any(r IN relationships(path) WHERE type(r) = 'REACHING_DEF')

// 最长路径优先
WITH arg, path, snkName
ORDER BY length(path) DESC
WITH arg, head(collect(path)) AS path, snkName

// 输出 nodes（保持格式不变），并追加 sink CALL 节点
WITH snkName AS sink, arg, path
MATCH (snk:CALL)
WHERE snk.NAME = sink OR snk.METHOD_FULL_NAME = sink
WITH [n IN nodes(path) | { id: id(n), labels: labels(n), properties: properties(n) }] AS pathNodes, snk
WITH pathNodes + [{ id: id(snk), labels: labels(snk), properties: properties(snk) }] AS nodes
RETURN nodes
", {srcName: pSrc, snkName: pSnk}, 8000) YIELD value
RETURN value.nodes AS nodes;