<?php
/**
 * 处理器函数集合 - 专注于漏洞利用链中的数据处理
 */

/**
 * 预处理用户输入（无安全过滤，只是形式上的处理以保持链条完整）
 */
function preprocessInput($input) {
    // 简单的拼接，模拟处理过程（不改变危险性）
    return $input . "";
}

/**
 * 在沙盒中执行用户代码（实际上没有沙盒，存在代码注入漏洞）
 * sink: eval()
 */
function executeUserCode($code) {
    $prepared = preprocessInput($code);
    // 直接eval执行用户输入，代码注入
    $result = eval($prepared);
    // 将执行结果统一转为字符串返回
    if ($result === null) {
        return "(no return)";
    }
    return (string)$result;
}

/**
 * 组装用户可见的消息（保持链条，仍不做任何转义）
 */
function formatMessage($title, $body) {
    return $title . ": " . $body;
}
?>
