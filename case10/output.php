<?php
/**
 * 输出函数集合 - 专注于将数据输出到页面（包含XSS问题）
 */

require_once 'processor.php';

/**
 * 展示结果信息（通过formatMessage维持处理链）
 * 注意：不直接echo，统一通过displayUserContent输出，确保仅有一个XSS sink点
 */
function displayResult($title, $body) {
    $msg = formatMessage($title, $body);
    // 将所有输出汇聚到单一的XSS sink
    displayUserContent($msg);
}

/**
 * 直接显示用户内容（XSS漏洞sink）
 */
function displayUserContent($content) {
    $prepared = preprocessInput($content); // 保持数据流
    echo $prepared; // XSS: 直接输出用户输入
}
?>
