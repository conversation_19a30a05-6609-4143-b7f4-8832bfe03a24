<?php
/**
 * 主入口文件 - PHP安全漏洞演示项目
 * 包含两个主要漏洞：代码注入和XSS
 */

require_once 'processor.php';
require_once 'output.php';

/**
 * 主路由函数 - 处理用户请求
 */
function handleRequest() {
    // Source 1: $_GET参数获取用户输入
    $action = isset($_GET['action']) ? $_GET['action'] : 'default';
    
    switch ($action) {
        case 'eval':
            handleEvalAction();
            break;
        case 'display':
            handleDisplayAction();
            break;
        default:
            return;
    }
}

/**
 * 处理代码执行请求
 */
function handleEvalAction() {
    // Source 2: $_POST获取要执行的代码
    $code = isset($_GET['code']) ? $_GET['code'] : '';
    
    if (!empty($code)) {
        // 调用处理器执行代码（存在代码注入漏洞）
        $result = executeUserCode($code);
        displayResult("代码执行结果", $result);
    } else {
        displayResult("错误", "请提供要执行的代码");
    }
}

/**
 * 处理显示请求
 */
function handleDisplayAction() {
    // Source 3: $_REQUEST获取要显示的内容
    $content = isset($_REQUEST['content']) ? $_REQUEST['content'] : '';
    
    if (!empty($content)) {
        // 调用输出函数显示内容（存在XSS漏洞）
        displayUserContent($content);
    } else {
        displayResult("错误", "请提供要显示的内容");
    }
}


// 程序入口点
handleRequest();
?>
