LOAD CSV FROM 'file:/nodes_CALL_data.csv' AS line
CREATE (:CALL {
id: toInteger(line[0]),
ARGUMENT_INDEX: toInteger(line[2]),
ARGUMENT_NAME: line[3],
CODE: line[4],
COLUMN_NUMBER: line[5],
DISPATCH_TYPE: line[6],
DYNAMIC_TYPE_HINT_FULL_NAME: toStringList(split(line[7], ";")),
LINE_NUMBER: toInteger(line[8]),
METHOD_FULL_NAME: line[9],
NAME: line[10],
OFFSET: line[11],
OFFSET_END: line[12],
ORDER: toInteger(line[13]),
POSSIBLE_TYPES: split(line[14], ";"),
SIGNATURE: line[15],
TYPE_FULL_NAME: line[16]
});
