LOAD CSV FROM 'file:/nodes_TYPE_DECL_data.csv' AS line
CREATE (:TYPE_DECL {
id: toInteger(line[0]),
ALIAS_TYPE_FULL_NAME: line[2],
AST_PARENT_FULL_NAME: line[3],
AST_PARENT_TYPE: line[4],
CODE: line[5],
COLUMN_NUMBER: line[6],
FILENAME: line[7],
FULL_NAME: line[8],
GENERIC_SIGNATURE: line[9],
INHERITS_FROM_TYPE_FULL_NAME: split(line[10], ";"),
IS_EXTERNAL: toBoolean(line[11]),
LINE_NUMBER: toInteger(line[12]),
NAME: line[13],
OFFSET: line[14],
OFFSET_END: line[15],
ORDER: toInteger(line[16])
});
