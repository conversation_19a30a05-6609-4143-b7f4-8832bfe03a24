30064771072,CALL,-1,,"require_once ""FileUploadVulnerability.php""",,STATIC_DISPATCH,require_once,11,require_once,require_once,,,1,,,ANY
30064771073,CALL,-1,,"require_once ""FileValidator.php""",,STATIC_DISPATCH,require_once,12,require_once,require_once,,,2,,,ANY
30064771074,CALL,-1,,$this->uploader = ,,STATIC_DISPATCH,,27,<operator>.assignment,<operator>.assignment,,,3,,,ANY
30064771075,CALL,1,,$this->uploader,,STATIC_DISPATCH,,27,<operator>.fieldAccess,<operator>.fieldAccess,,,1,,,ANY
30064771076,CALL,-1,,$SecurityDemo\\FileUpload\\FileUploadController->__construct@tmp-0 = SecurityDemo\\FileUpload\\VulnerableFileUploader.<alloc>(),,STATIC_DISPATCH,,27,<operator>.assignment,<operator>.assignment,,,1,,,SecurityDemo\\FileUpload\\VulnerableFileUploader
30064771077,CALL,2,,SecurityDemo\\FileUpload\\VulnerableFileUploader.<alloc>(),,STATIC_DISPATCH,,27,<operator>.alloc,<operator>.alloc,,,2,,,SecurityDemo\\FileUpload\\VulnerableFileUploader
30064771078,CALL,-1,,SecurityDemo\\FileUpload\\VulnerableFileUploader->__construct(),,DYNAMIC_DISPATCH,SecurityDemo\\FileUpload\\VulnerableFileUploader->__construct,27,SecurityDemo\\FileUpload\\VulnerableFileUploader->__construct,__construct,,,2,,<unresolvedSignature>(0),ANY
30064771079,CALL,-1,,$this->validator = ,,STATIC_DISPATCH,,28,<operator>.assignment,<operator>.assignment,,,4,,,ANY
30064771080,CALL,1,,$this->validator,,STATIC_DISPATCH,,28,<operator>.fieldAccess,<operator>.fieldAccess,,,1,,,ANY
30064771081,CALL,-1,,$SecurityDemo\\FileUpload\\FileUploadController->__construct@tmp-1 = SecurityDemo\\FileUpload\\FileValidator.<alloc>(),,STATIC_DISPATCH,,28,<operator>.assignment,<operator>.assignment,,,1,,,SecurityDemo\\FileUpload\\FileValidator
30064771082,CALL,2,,SecurityDemo\\FileUpload\\FileValidator.<alloc>(),,STATIC_DISPATCH,,28,<operator>.alloc,<operator>.alloc,,,2,,,SecurityDemo\\FileUpload\\FileValidator
30064771083,CALL,-1,,SecurityDemo\\FileUpload\\FileValidator->__construct(),,DYNAMIC_DISPATCH,SecurityDemo\\FileUpload\\FileValidator->__construct,28,SecurityDemo\\FileUpload\\FileValidator->__construct,__construct,,,2,,<unresolvedSignature>(0),ANY
30064771084,CALL,-1,,"$action = $_POST[""action""] ?? $_GET[""action""] ?? ""upload""",,STATIC_DISPATCH,,38,<operator>.assignment,<operator>.assignment,,,5,,,ANY
30064771085,CALL,2,,"$_POST[""action""] ?? $_GET[""action""] ?? ""upload""",,STATIC_DISPATCH,,38,<operator>.coalesce,<operator>.coalesce,,,2,,,ANY
30064771086,CALL,1,,"$_POST[""action""]",,STATIC_DISPATCH,,38,<operator>.indexAccess,<operator>.indexAccess,,,1,,,ANY
30064771087,CALL,2,,"$_GET[""action""] ?? ""upload""",,STATIC_DISPATCH,,38,<operator>.coalesce,<operator>.coalesce,,,2,,,ANY
30064771088,CALL,1,,"$_GET[""action""]",,STATIC_DISPATCH,,38,<operator>.indexAccess,<operator>.indexAccess,,,1,,,ANY
30064771089,CALL,-1,,"$action === ""upload""",,STATIC_DISPATCH,,40,<operator>.identical,<operator>.identical,,,1,,,ANY
30064771090,CALL,1,,$this->processUpload(),,DYNAMIC_DISPATCH,,41,SecurityDemo\\FileUpload\\FileUploadController->processUpload,processUpload,,,1,,<unresolvedSignature>(0),array
30064771091,CALL,-1,,"$action === ""batch""",,STATIC_DISPATCH,,42,<operator>.identical,<operator>.identical,,,1,,,ANY
30064771092,CALL,1,,$this->processBatchUpload(),,DYNAMIC_DISPATCH,,43,SecurityDemo\\FileUpload\\FileUploadController->processBatchUpload,processBatchUpload,,,1,,<unresolvedSignature>(0),array
30064771093,CALL,-1,,$SecurityDemo\\FileUpload\\FileUploadController->handleRequest@tmp-0 = array(),,STATIC_DISPATCH,,46,<operator>.assignment,<operator>.assignment,,,1,,,ANY
30064771094,CALL,2,,array(),,STATIC_DISPATCH,array,46,array,array,,,2,,array(),array
30064771095,CALL,-1,,"$SecurityDemo\\FileUpload\\FileUploadController->handleRequest@tmp-0[""success""] = false",,STATIC_DISPATCH,,46,<operator>.assignment,<operator>.assignment,,,2,,,ANY
30064771096,CALL,1,,"$SecurityDemo\\FileUpload\\FileUploadController->handleRequest@tmp-0[""success""]",,STATIC_DISPATCH,,46,<operator>.indexAccess,<operator>.indexAccess,,,1,,,ANY
30064771097,CALL,-1,,"$SecurityDemo\\FileUpload\\FileUploadController->handleRequest@tmp-0[""message""] = ""未知操作""",,STATIC_DISPATCH,,46,<operator>.assignment,<operator>.assignment,,,3,,,ANY
30064771098,CALL,1,,"$SecurityDemo\\FileUpload\\FileUploadController->handleRequest@tmp-0[""message""]",,STATIC_DISPATCH,,46,<operator>.indexAccess,<operator>.indexAccess,,,1,,,ANY
30064771099,CALL,-1,,"!isset($_FILES[""file""])",,STATIC_DISPATCH,,54,<operator>.logicalNot,<operator>.logicalNot,,,1,,,ANY
30064771100,CALL,1,,"isset($_FILES[""file""])",,STATIC_DISPATCH,isset,54,isset,isset,,,1,,,bool
30064771101,CALL,1,,"$_FILES[""file""]",,STATIC_DISPATCH,,54,<operator>.indexAccess,<operator>.indexAccess,,,1,,,ANY
30064771102,CALL,-1,,$SecurityDemo\\FileUpload\\FileUploadController->processUpload@tmp-0 = array(),,STATIC_DISPATCH,,55,<operator>.assignment,<operator>.assignment,,,1,,,ANY
30064771103,CALL,2,,array(),,STATIC_DISPATCH,array,55,array,array,,,2,,array(),array
30064771104,CALL,-1,,"$SecurityDemo\\FileUpload\\FileUploadController->processUpload@tmp-0[""success""] = false",,STATIC_DISPATCH,,55,<operator>.assignment,<operator>.assignment,,,2,,,ANY
30064771105,CALL,1,,"$SecurityDemo\\FileUpload\\FileUploadController->processUpload@tmp-0[""success""]",,STATIC_DISPATCH,,55,<operator>.indexAccess,<operator>.indexAccess,,,1,,,ANY
30064771106,CALL,-1,,"$SecurityDemo\\FileUpload\\FileUploadController->processUpload@tmp-0[""message""] = ""未找到上传文件""",,STATIC_DISPATCH,,55,<operator>.assignment,<operator>.assignment,,,3,,,ANY
30064771107,CALL,1,,"$SecurityDemo\\FileUpload\\FileUploadController->processUpload@tmp-0[""message""]",,STATIC_DISPATCH,,55,<operator>.indexAccess,<operator>.indexAccess,,,1,,,ANY
30064771108,CALL,-1,,"$customName = $_POST[""filename""] ?? $_GET[""filename""] ?? """"",,STATIC_DISPATCH,,59,<operator>.assignment,<operator>.assignment,,,9,,,ANY
30064771109,CALL,2,,"$_POST[""filename""] ?? $_GET[""filename""] ?? """"",,STATIC_DISPATCH,,59,<operator>.coalesce,<operator>.coalesce,,,2,,,ANY
30064771110,CALL,1,,"$_POST[""filename""]",,STATIC_DISPATCH,,59,<operator>.indexAccess,<operator>.indexAccess,,,1,,,ANY
30064771111,CALL,2,,"$_GET[""filename""] ?? """"",,STATIC_DISPATCH,,59,<operator>.coalesce,<operator>.coalesce,,,2,,,ANY
30064771112,CALL,1,,"$_GET[""filename""]",,STATIC_DISPATCH,,59,<operator>.indexAccess,<operator>.indexAccess,,,1,,,ANY
30064771113,CALL,-1,,"$subDir = $_POST[""subdir""] ?? $_GET[""subdir""] ?? """"",,STATIC_DISPATCH,,62,<operator>.assignment,<operator>.assignment,,,10,,,ANY
30064771114,CALL,2,,"$_POST[""subdir""] ?? $_GET[""subdir""] ?? """"",,STATIC_DISPATCH,,62,<operator>.coalesce,<operator>.coalesce,,,2,,,ANY
30064771115,CALL,1,,"$_POST[""subdir""]",,STATIC_DISPATCH,,62,<operator>.indexAccess,<operator>.indexAccess,,,1,,,ANY
30064771116,CALL,2,,"$_GET[""subdir""] ?? """"",,STATIC_DISPATCH,,62,<operator>.coalesce,<operator>.coalesce,,,2,,,ANY
30064771117,CALL,1,,"$_GET[""subdir""]",,STATIC_DISPATCH,,62,<operator>.indexAccess,<operator>.indexAccess,,,1,,,ANY
30064771118,CALL,-1,,!empty($subDir),,STATIC_DISPATCH,,64,<operator>.logicalNot,<operator>.logicalNot,,,1,,,ANY
30064771119,CALL,1,,empty($subDir),,STATIC_DISPATCH,empty,64,empty,empty,,,1,,,bool
30064771120,CALL,-1,,$this->uploader = ,,STATIC_DISPATCH,,66,<operator>.assignment,<operator>.assignment,,,1,,,ANY
30064771121,CALL,1,,$this->uploader,,STATIC_DISPATCH,,66,<operator>.fieldAccess,<operator>.fieldAccess,,,1,,,ANY
30064771122,CALL,-1,,$SecurityDemo\\FileUpload\\FileUploadController->processUpload@tmp-1 = SecurityDemo\\FileUpload\\VulnerableFileUploader.<alloc>(),,STATIC_DISPATCH,,66,<operator>.assignment,<operator>.assignment,,,1,,,SecurityDemo\\FileUpload\\VulnerableFileUploader
30064771123,CALL,2,,SecurityDemo\\FileUpload\\VulnerableFileUploader.<alloc>(),,STATIC_DISPATCH,,66,<operator>.alloc,<operator>.alloc,,,2,,,SecurityDemo\\FileUpload\\VulnerableFileUploader
30064771124,CALL,-1,,"SecurityDemo\\FileUpload\\VulnerableFileUploader->__construct(""./uploads/"" . $subDir . ""/"")",,DYNAMIC_DISPATCH,SecurityDemo\\FileUpload\\VulnerableFileUploader->__construct,66,SecurityDemo\\FileUpload\\VulnerableFileUploader->__construct,__construct,,,2,,<unresolvedSignature>(1),ANY
30064771125,CALL,1,,"""./uploads/"" . $subDir . ""/""",,STATIC_DISPATCH,,66,<operator>.concat,<operator>.concat,,,2,,,ANY
30064771126,CALL,1,,"""./uploads/"" . $subDir",,STATIC_DISPATCH,,66,<operator>.concat,<operator>.concat,,,1,,,ANY
30064771127,CALL,1,,"$this->uploader->handleUpload($_FILES[""file""],$customName)",,DYNAMIC_DISPATCH,,69,SecurityDemo\\FileUpload\\VulnerableFileUploader->handleUpload-><returnValue>,handleUpload,,,1,,<unresolvedSignature>(2),ANY
30064771128,CALL,0,,$this->uploader,,STATIC_DISPATCH,,69,<operator>.fieldAccess,<operator>.fieldAccess,,,1,,,ANY
30064771129,CALL,1,,"$_FILES[""file""]",,STATIC_DISPATCH,,69,<operator>.indexAccess,<operator>.indexAccess,,,2,,,ANY
30064771130,CALL,-1,,"!isset($_FILES[""files""])",,STATIC_DISPATCH,,77,<operator>.logicalNot,<operator>.logicalNot,,,1,,,ANY
30064771131,CALL,1,,"isset($_FILES[""files""])",,STATIC_DISPATCH,isset,77,isset,isset,,,1,,,bool
30064771132,CALL,1,,"$_FILES[""files""]",,STATIC_DISPATCH,,77,<operator>.indexAccess,<operator>.indexAccess,,,1,,,ANY
30064771133,CALL,-1,,$SecurityDemo\\FileUpload\\FileUploadController->processBatchUpload@tmp-0 = array(),,STATIC_DISPATCH,,78,<operator>.assignment,<operator>.assignment,,,1,,,ANY
30064771134,CALL,2,,array(),,STATIC_DISPATCH,array,78,array,array,,,2,,array(),array
30064771135,CALL,-1,,"$SecurityDemo\\FileUpload\\FileUploadController->processBatchUpload@tmp-0[""success""] = false",,STATIC_DISPATCH,,78,<operator>.assignment,<operator>.assignment,,,2,,,ANY
30064771136,CALL,1,,"$SecurityDemo\\FileUpload\\FileUploadController->processBatchUpload@tmp-0[""success""]",,STATIC_DISPATCH,,78,<operator>.indexAccess,<operator>.indexAccess,,,1,,,ANY
30064771137,CALL,-1,,"$SecurityDemo\\FileUpload\\FileUploadController->processBatchUpload@tmp-0[""message""] = ""未找到批量上传文件""",,STATIC_DISPATCH,,78,<operator>.assignment,<operator>.assignment,,,3,,,ANY
30064771138,CALL,1,,"$SecurityDemo\\FileUpload\\FileUploadController->processBatchUpload@tmp-0[""message""]",,STATIC_DISPATCH,,78,<operator>.indexAccess,<operator>.indexAccess,,,1,,,ANY
30064771139,CALL,-1,,$results = ,,STATIC_DISPATCH,,81,<operator>.assignment,<operator>.assignment,,,14,,,ANY
30064771140,CALL,-1,,$SecurityDemo\\FileUpload\\FileUploadController->processBatchUpload@tmp-1 = array(),,STATIC_DISPATCH,,81,<operator>.assignment,<operator>.assignment,,,1,,,ANY
30064771141,CALL,2,,array(),,STATIC_DISPATCH,array,81,array,array,,,2,,array(),array
30064771142,CALL,-1,,"$files = $_FILES[""files""]",,STATIC_DISPATCH,,82,<operator>.assignment,<operator>.assignment,,,15,,,ANY
30064771143,CALL,2,,"$_FILES[""files""]",,STATIC_DISPATCH,,82,<operator>.indexAccess,<operator>.indexAccess,,,2,,,ANY
30064771144,CALL,-1,,$i = 0,,STATIC_DISPATCH,,85,<operator>.assignment,<operator>.assignment,,,1,,,ANY
30064771145,CALL,-1,,"$i < count($files[""name""])",,STATIC_DISPATCH,,85,<operator>.lessThan,<operator>.lessThan,,,2,,,ANY
30064771146,CALL,2,,"count($files[""name""])",,STATIC_DISPATCH,count,85,count,count,,,2,,<unresolvedSignature>(1),ANY
30064771147,CALL,1,,"$files[""name""]",,STATIC_DISPATCH,,85,<operator>.indexAccess,<operator>.indexAccess,,,1,,,ANY
30064771148,CALL,-1,,$i++,,STATIC_DISPATCH,,85,<operator>.postIncrement,<operator>.postIncrement,,,3,,,ANY
30064771149,CALL,-1,,$fileData = ,,STATIC_DISPATCH,,86,<operator>.assignment,<operator>.assignment,,,1,,,ANY
30064771150,CALL,-1,,$SecurityDemo\\FileUpload\\FileUploadController->processBatchUpload@tmp-2 = array(),,STATIC_DISPATCH,,86,<operator>.assignment,<operator>.assignment,,,1,,,ANY
30064771151,CALL,2,,array(),,STATIC_DISPATCH,array,86,array,array,,,2,,array(),array
30064771152,CALL,-1,,"$SecurityDemo\\FileUpload\\FileUploadController->processBatchUpload@tmp-2[""name""] = $files[""name""][$i]",,STATIC_DISPATCH,,87,<operator>.assignment,<operator>.assignment,,,2,,,ANY
30064771153,CALL,1,,"$SecurityDemo\\FileUpload\\FileUploadController->processBatchUpload@tmp-2[""name""]",,STATIC_DISPATCH,,87,<operator>.indexAccess,<operator>.indexAccess,,,1,,,ANY
30064771154,CALL,2,,"$files[""name""][$i]",,STATIC_DISPATCH,,87,<operator>.indexAccess,<operator>.indexAccess,,,2,,,ANY
30064771155,CALL,1,,"$files[""name""]",,STATIC_DISPATCH,,87,<operator>.indexAccess,<operator>.indexAccess,,,1,,,ANY
30064771156,CALL,-1,,"$SecurityDemo\\FileUpload\\FileUploadController->processBatchUpload@tmp-2[""type""] = $files[""type""][$i]",,STATIC_DISPATCH,,88,<operator>.assignment,<operator>.assignment,,,3,,,ANY
30064771157,CALL,1,,"$SecurityDemo\\FileUpload\\FileUploadController->processBatchUpload@tmp-2[""type""]",,STATIC_DISPATCH,,88,<operator>.indexAccess,<operator>.indexAccess,,,1,,,ANY
30064771158,CALL,2,,"$files[""type""][$i]",,STATIC_DISPATCH,,88,<operator>.indexAccess,<operator>.indexAccess,,,2,,,ANY
30064771159,CALL,1,,"$files[""type""]",,STATIC_DISPATCH,,88,<operator>.indexAccess,<operator>.indexAccess,,,1,,,ANY
30064771160,CALL,-1,,"$SecurityDemo\\FileUpload\\FileUploadController->processBatchUpload@tmp-2[""tmp_name""] = $files[""tmp_name""][$i]",,STATIC_DISPATCH,,89,<operator>.assignment,<operator>.assignment,,,4,,,ANY
30064771161,CALL,1,,"$SecurityDemo\\FileUpload\\FileUploadController->processBatchUpload@tmp-2[""tmp_name""]",,STATIC_DISPATCH,,89,<operator>.indexAccess,<operator>.indexAccess,,,1,,,ANY
30064771162,CALL,2,,"$files[""tmp_name""][$i]",,STATIC_DISPATCH,,89,<operator>.indexAccess,<operator>.indexAccess,,,2,,,ANY
30064771163,CALL,1,,"$files[""tmp_name""]",,STATIC_DISPATCH,,89,<operator>.indexAccess,<operator>.indexAccess,,,1,,,ANY
30064771164,CALL,-1,,"$SecurityDemo\\FileUpload\\FileUploadController->processBatchUpload@tmp-2[""error""] = $files[""error""][$i]",,STATIC_DISPATCH,,90,<operator>.assignment,<operator>.assignment,,,5,,,ANY
30064771165,CALL,1,,"$SecurityDemo\\FileUpload\\FileUploadController->processBatchUpload@tmp-2[""error""]",,STATIC_DISPATCH,,90,<operator>.indexAccess,<operator>.indexAccess,,,1,,,ANY
30064771166,CALL,2,,"$files[""error""][$i]",,STATIC_DISPATCH,,90,<operator>.indexAccess,<operator>.indexAccess,,,2,,,ANY
30064771167,CALL,1,,"$files[""error""]",,STATIC_DISPATCH,,90,<operator>.indexAccess,<operator>.indexAccess,,,1,,,ANY
30064771168,CALL,-1,,"$SecurityDemo\\FileUpload\\FileUploadController->processBatchUpload@tmp-2[""size""] = $files[""size""][$i]",,STATIC_DISPATCH,,91,<operator>.assignment,<operator>.assignment,,,6,,,ANY
30064771169,CALL,1,,"$SecurityDemo\\FileUpload\\FileUploadController->processBatchUpload@tmp-2[""size""]",,STATIC_DISPATCH,,91,<operator>.indexAccess,<operator>.indexAccess,,,1,,,ANY
30064771170,CALL,2,,"$files[""size""][$i]",,STATIC_DISPATCH,,91,<operator>.indexAccess,<operator>.indexAccess,,,2,,,ANY
30064771171,CALL,1,,"$files[""size""]",,STATIC_DISPATCH,,91,<operator>.indexAccess,<operator>.indexAccess,,,1,,,ANY
30064771172,CALL,-1,,"$customName = $_POST[""filenames""][$i] ?? """"",,STATIC_DISPATCH,,95,<operator>.assignment,<operator>.assignment,,,2,,,ANY
30064771173,CALL,2,,"$_POST[""filenames""][$i] ?? """"",,STATIC_DISPATCH,,95,<operator>.coalesce,<operator>.coalesce,,,2,,,ANY
30064771174,CALL,1,,"$_POST[""filenames""][$i]",,STATIC_DISPATCH,,95,<operator>.indexAccess,<operator>.indexAccess,,,1,,,ANY
30064771175,CALL,1,,"$_POST[""filenames""]",,STATIC_DISPATCH,,95,<operator>.indexAccess,<operator>.indexAccess,,,1,,,ANY
30064771176,CALL,-1,,"$result = $this->uploader->handleUpload($fileData,$customName)",,STATIC_DISPATCH,,97,<operator>.assignment,<operator>.assignment,,,3,,,ANY
30064771177,CALL,2,,"$this->uploader->handleUpload($fileData,$customName)",,DYNAMIC_DISPATCH,,97,SecurityDemo\\FileUpload\\VulnerableFileUploader->handleUpload-><returnValue>,handleUpload,,,2,,<unresolvedSignature>(2),ANY
30064771178,CALL,0,,$this->uploader,,STATIC_DISPATCH,,97,<operator>.fieldAccess,<operator>.fieldAccess,,,1,,,ANY
30064771179,CALL,-1,,$results[] = $result,,STATIC_DISPATCH,array_push,98,array_push,array_push,,,4,,<unresolvedSignature>(2),ANY
30064771180,CALL,-1,,$SecurityDemo\\FileUpload\\FileUploadController->processBatchUpload@tmp-3 = array(),,STATIC_DISPATCH,,101,<operator>.assignment,<operator>.assignment,,,1,,,ANY
30064771181,CALL,2,,array(),,STATIC_DISPATCH,array,101,array,array,,,2,,array(),array
30064771182,CALL,-1,,"$SecurityDemo\\FileUpload\\FileUploadController->processBatchUpload@tmp-3[""success""] = true",,STATIC_DISPATCH,,101,<operator>.assignment,<operator>.assignment,,,2,,,ANY
30064771183,CALL,1,,"$SecurityDemo\\FileUpload\\FileUploadController->processBatchUpload@tmp-3[""success""]",,STATIC_DISPATCH,,101,<operator>.indexAccess,<operator>.indexAccess,,,1,,,ANY
30064771184,CALL,-1,,"$SecurityDemo\\FileUpload\\FileUploadController->processBatchUpload@tmp-3[""results""] = $results",,STATIC_DISPATCH,,101,<operator>.assignment,<operator>.assignment,,,3,,,ANY
30064771185,CALL,1,,"$SecurityDemo\\FileUpload\\FileUploadController->processBatchUpload@tmp-3[""results""]",,STATIC_DISPATCH,,101,<operator>.indexAccess,<operator>.indexAccess,,,1,,,ANY
30064771186,CALL,-1,,"header(""Content-Type: application/json"")",,STATIC_DISPATCH,header,109,header,header,,,2,,<unresolvedSignature>(1),ANY
30064771187,CALL,-1,,"echo json_encode($data,JSON_UNESCAPED_UNICODE)",,STATIC_DISPATCH,echo,110,echo,echo,,,3,,,ANY
30064771188,CALL,1,,"json_encode($data,JSON_UNESCAPED_UNICODE)",,STATIC_DISPATCH,json_encode,110,json_encode,json_encode,,,1,,<unresolvedSignature>(2),ANY
30064771189,CALL,2,,JSON_UNESCAPED_UNICODE,,STATIC_DISPATCH,,110,<operator>.fieldAccess,<operator>.fieldAccess,,,2,,,ANY
30064771190,CALL,-1,,"$_SERVER[""REQUEST_METHOD""] === ""POST"" || !empty($_GET[""action""])",,STATIC_DISPATCH,,115,<operator>.logicalOr,<operator>.logicalOr,,,1,,,ANY
30064771191,CALL,1,,"$_SERVER[""REQUEST_METHOD""] === ""POST""",,STATIC_DISPATCH,,115,<operator>.identical,<operator>.identical,,,1,,,ANY
30064771192,CALL,1,,"$_SERVER[""REQUEST_METHOD""]",,STATIC_DISPATCH,,115,<operator>.indexAccess,<operator>.indexAccess,,,1,,,ANY
30064771193,CALL,2,,"!empty($_GET[""action""])",,STATIC_DISPATCH,,115,<operator>.logicalNot,<operator>.logicalNot,,,2,,,ANY
30064771194,CALL,1,,"empty($_GET[""action""])",,STATIC_DISPATCH,empty,115,empty,empty,,,1,,,bool
30064771195,CALL,1,,"$_GET[""action""]",,STATIC_DISPATCH,,115,<operator>.indexAccess,<operator>.indexAccess,,,1,,,ANY
30064771196,CALL,-1,,$controller = ,,STATIC_DISPATCH,,116,<operator>.assignment,<operator>.assignment,,,1,,,ANY
30064771197,CALL,-1,,$FileUploadHandler.php:SecurityDemo\\FileUpload@tmp-0 = SecurityDemo\\FileUpload\\FileUploadController.<alloc>(),,STATIC_DISPATCH,,116,<operator>.assignment,<operator>.assignment,,,1,,,SecurityDemo\\FileUpload\\FileUploadController
30064771198,CALL,2,,SecurityDemo\\FileUpload\\FileUploadController.<alloc>(),,STATIC_DISPATCH,,116,<operator>.alloc,<operator>.alloc,,,2,,,SecurityDemo\\FileUpload\\FileUploadController
30064771199,CALL,-1,,SecurityDemo\\FileUpload\\FileUploadController->__construct(),,DYNAMIC_DISPATCH,SecurityDemo\\FileUpload\\FileUploadController->__construct,116,SecurityDemo\\FileUpload\\FileUploadController->__construct,__construct,,,2,,<unresolvedSignature>(0),ANY
30064771200,CALL,-1,,$result = $controller->handleRequest(),,STATIC_DISPATCH,,117,<operator>.assignment,<operator>.assignment,,,2,,,ANY
30064771201,CALL,2,,$controller->handleRequest(),,DYNAMIC_DISPATCH,,117,SecurityDemo\\FileUpload\\FileUploadController->handleRequest,handleRequest,,,2,,<unresolvedSignature>(0),SecurityDemo\\FileUpload\\FileUploadController->handleRequest-><returnValue>
30064771202,CALL,-1,,$controller->sendResponse($result),,DYNAMIC_DISPATCH,,118,SecurityDemo\\FileUpload\\FileUploadController->sendResponse,sendResponse,,,3,,<unresolvedSignature>(1),SecurityDemo\\FileUpload\\FileUploadController->sendResponse-><returnValue>
30064771203,CALL,-1,,"echo ""<!DOCTYPE html>\\n<html>\\n<head><title>文件上传测试 - 仅用于安全研究</title></head>\\n<body>\\n<h2>文件上传漏洞演示</h2>\\n<p style=\\""color:red;\\"">警告：此页面包含安全漏洞，仅用于教学目的</p>\\n\\n<h3>单文件上传</h3>\\n<form method=\\""post\\"" enctype=\\""multipart/form-data\\"">\\n    <input type=\\""hidden\\"" name=\\""action\\"" value=\\""upload\\"">\\n    文件: <input type=\\""file\\"" name=\\""file\\""><br>\\n    自定义文件名: <input type=\\""text\\"" name=\\""filename\\"" placeholder=\\""可选\\""><br>\\n    子目录: <input type=\\""text\\"" name=\\""subdir\\"" placeholder=\\""可选\\""><br>\\n    <input type=\\""submit\\"" value=\\""上传\\"">\\n</form>\\n\\n<h3>批量上传</h3>\\n<form method=\\""post\\"" enctype=\\""multipart/form-data\\"">\\n    <input type=\\""hidden\\"" name=\\""action\\"" value=\\""batch\\"">\\n    文件: <input type=\\""file\\"" name=\\""files[]\\"" multiple><br>\\n    <input type=\\""submit\\"" value=\\""批量上传\\"">\\n</form>\\n</body>\\n</html>""",,STATIC_DISPATCH,echo,121,echo,echo,,,1,,,ANY
30064771204,CALL,-1,,$this->uploadDir = $uploadDir,,STATIC_DISPATCH,,34,<operator>.assignment,<operator>.assignment,,,3,,,ANY
30064771205,CALL,1,,$this->uploadDir,,STATIC_DISPATCH,,34,<operator>.fieldAccess,<operator>.fieldAccess,,,1,,,ANY
30064771206,CALL,-1,,$this->allowedMimeTypes = ,,STATIC_DISPATCH,,36,<operator>.assignment,<operator>.assignment,,,4,,,ANY
30064771207,CALL,1,,$this->allowedMimeTypes,,STATIC_DISPATCH,,36,<operator>.fieldAccess,<operator>.fieldAccess,,,1,,,ANY
30064771208,CALL,-1,,$SecurityDemo\\FileUpload\\VulnerableFileUploader->__construct@tmp-0 = array(),,STATIC_DISPATCH,,36,<operator>.assignment,<operator>.assignment,,,1,,,ANY
30064771209,CALL,2,,array(),,STATIC_DISPATCH,array,36,array,array,,,2,,array(),array
30064771210,CALL,-1,,"$SecurityDemo\\FileUpload\\VulnerableFileUploader->__construct@tmp-0[0] = ""image/jpeg""",,STATIC_DISPATCH,,37,<operator>.assignment,<operator>.assignment,,,2,,,ANY
30064771211,CALL,1,,$SecurityDemo\\FileUpload\\VulnerableFileUploader->__construct@tmp-0[0],,STATIC_DISPATCH,,37,<operator>.indexAccess,<operator>.indexAccess,,,1,,,ANY
30064771212,CALL,-1,,"$SecurityDemo\\FileUpload\\VulnerableFileUploader->__construct@tmp-0[1] = ""image/png""",,STATIC_DISPATCH,,38,<operator>.assignment,<operator>.assignment,,,3,,,ANY
30064771213,CALL,1,,$SecurityDemo\\FileUpload\\VulnerableFileUploader->__construct@tmp-0[1],,STATIC_DISPATCH,,38,<operator>.indexAccess,<operator>.indexAccess,,,1,,,ANY
30064771214,CALL,-1,,"$SecurityDemo\\FileUpload\\VulnerableFileUploader->__construct@tmp-0[2] = ""image/gif""",,STATIC_DISPATCH,,39,<operator>.assignment,<operator>.assignment,,,4,,,ANY
30064771215,CALL,1,,$SecurityDemo\\FileUpload\\VulnerableFileUploader->__construct@tmp-0[2],,STATIC_DISPATCH,,39,<operator>.indexAccess,<operator>.indexAccess,,,1,,,ANY
30064771216,CALL,-1,,"$SecurityDemo\\FileUpload\\VulnerableFileUploader->__construct@tmp-0[3] = ""text/plain""",,STATIC_DISPATCH,,40,<operator>.assignment,<operator>.assignment,,,5,,,ANY
30064771217,CALL,1,,$SecurityDemo\\FileUpload\\VulnerableFileUploader->__construct@tmp-0[3],,STATIC_DISPATCH,,40,<operator>.indexAccess,<operator>.indexAccess,,,1,,,ANY
30064771218,CALL,-1,,$this->blockedExtensions = ,,STATIC_DISPATCH,,43,<operator>.assignment,<operator>.assignment,,,5,,,ANY
30064771219,CALL,1,,$this->blockedExtensions,,STATIC_DISPATCH,,43,<operator>.fieldAccess,<operator>.fieldAccess,,,1,,,ANY
30064771220,CALL,-1,,$SecurityDemo\\FileUpload\\VulnerableFileUploader->__construct@tmp-1 = array(),,STATIC_DISPATCH,,43,<operator>.assignment,<operator>.assignment,,,1,,,ANY
30064771221,CALL,2,,array(),,STATIC_DISPATCH,array,43,array,array,,,2,,array(),array
30064771222,CALL,-1,,"$SecurityDemo\\FileUpload\\VulnerableFileUploader->__construct@tmp-1[0] = ""exe""",,STATIC_DISPATCH,,43,<operator>.assignment,<operator>.assignment,,,2,,,ANY
30064771223,CALL,1,,$SecurityDemo\\FileUpload\\VulnerableFileUploader->__construct@tmp-1[0],,STATIC_DISPATCH,,43,<operator>.indexAccess,<operator>.indexAccess,,,1,,,ANY
30064771224,CALL,-1,,"$SecurityDemo\\FileUpload\\VulnerableFileUploader->__construct@tmp-1[1] = ""bat""",,STATIC_DISPATCH,,43,<operator>.assignment,<operator>.assignment,,,3,,,ANY
30064771225,CALL,1,,$SecurityDemo\\FileUpload\\VulnerableFileUploader->__construct@tmp-1[1],,STATIC_DISPATCH,,43,<operator>.indexAccess,<operator>.indexAccess,,,1,,,ANY
30064771226,CALL,-1,,$this->maxFileSize = 5 * 1024 * 1024,,STATIC_DISPATCH,,44,<operator>.assignment,<operator>.assignment,,,6,,,ANY
30064771227,CALL,1,,$this->maxFileSize,,STATIC_DISPATCH,,44,<operator>.fieldAccess,<operator>.fieldAccess,,,1,,,ANY
30064771228,CALL,2,,5 * 1024 * 1024,,STATIC_DISPATCH,,44,<operator>.multiplication,<operator>.multiplication,,,2,,,ANY
30064771229,CALL,1,,5 * 1024,,STATIC_DISPATCH,,44,<operator>.multiplication,<operator>.multiplication,,,1,,,ANY
30064771230,CALL,-1,,$this->createUploadDirectory(),,DYNAMIC_DISPATCH,,46,SecurityDemo\\FileUpload\\VulnerableFileUploader->createUploadDirectory,createUploadDirectory,,,7,,<unresolvedSignature>(0),void
30064771231,CALL,-1,,!is_dir($this->uploadDir),,STATIC_DISPATCH,,54,<operator>.logicalNot,<operator>.logicalNot,,,1,,,ANY
30064771232,CALL,1,,is_dir($this->uploadDir),,STATIC_DISPATCH,is_dir,54,is_dir,is_dir,,,1,,<unresolvedSignature>(1),ANY
30064771233,CALL,1,,$this->uploadDir,,STATIC_DISPATCH,,54,<operator>.fieldAccess,<operator>.fieldAccess,,,1,,,ANY
30064771234,CALL,-1,,"mkdir($this->uploadDir,493,true)",,STATIC_DISPATCH,mkdir,55,mkdir,mkdir,,,1,,<unresolvedSignature>(3),ANY
30064771235,CALL,1,,$this->uploadDir,,STATIC_DISPATCH,,55,<operator>.fieldAccess,<operator>.fieldAccess,,,1,,,ANY
30064771236,CALL,-1,,!$this->validateFileUpload($fileData),,STATIC_DISPATCH,,71,<operator>.logicalNot,<operator>.logicalNot,,,1,,,ANY
30064771237,CALL,1,,$this->validateFileUpload($fileData),,DYNAMIC_DISPATCH,,71,SecurityDemo\\FileUpload\\VulnerableFileUploader->validateFileUpload,validateFileUpload,,,1,,<unresolvedSignature>(1),bool
30064771238,CALL,-1,,$SecurityDemo\\FileUpload\\VulnerableFileUploader->handleUpload@tmp-0 = array(),,STATIC_DISPATCH,,72,<operator>.assignment,<operator>.assignment,,,1,,,ANY
30064771239,CALL,2,,array(),,STATIC_DISPATCH,array,72,array,array,,,2,,array(),array
30064771240,CALL,-1,,"$SecurityDemo\\FileUpload\\VulnerableFileUploader->handleUpload@tmp-0[""success""] = false",,STATIC_DISPATCH,,72,<operator>.assignment,<operator>.assignment,,,2,,,ANY
30064771241,CALL,1,,"$SecurityDemo\\FileUpload\\VulnerableFileUploader->handleUpload@tmp-0[""success""]",,STATIC_DISPATCH,,72,<operator>.indexAccess,<operator>.indexAccess,,,1,,,ANY
30064771242,CALL,-1,,"$SecurityDemo\\FileUpload\\VulnerableFileUploader->handleUpload@tmp-0[""message""] = ""文件验证失败""",,STATIC_DISPATCH,,72,<operator>.assignment,<operator>.assignment,,,3,,,ANY
30064771243,CALL,1,,"$SecurityDemo\\FileUpload\\VulnerableFileUploader->handleUpload@tmp-0[""message""]",,STATIC_DISPATCH,,72,<operator>.indexAccess,<operator>.indexAccess,,,1,,,ANY
30064771244,CALL,-1,,"$fileName = $this->processFileName($fileData[""name""],$customName)",,STATIC_DISPATCH,,76,<operator>.assignment,<operator>.assignment,,,2,,,ANY
30064771245,CALL,2,,"$this->processFileName($fileData[""name""],$customName)",,DYNAMIC_DISPATCH,,76,SecurityDemo\\FileUpload\\VulnerableFileUploader->processFileName,processFileName,,,2,,<unresolvedSignature>(2),string
30064771246,CALL,1,,"$fileData[""name""]",,STATIC_DISPATCH,,76,<operator>.indexAccess,<operator>.indexAccess,,,2,,,ANY
30064771247,CALL,-1,,$targetPath = $this->uploadDir . $fileName,,STATIC_DISPATCH,,79,<operator>.assignment,<operator>.assignment,,,3,,,ANY
30064771248,CALL,2,,$this->uploadDir . $fileName,,STATIC_DISPATCH,,79,<operator>.concat,<operator>.concat,,,2,,,ANY
30064771249,CALL,1,,$this->uploadDir,,STATIC_DISPATCH,,79,<operator>.fieldAccess,<operator>.fieldAccess,,,1,,,ANY
30064771250,CALL,-1,,"move_uploaded_file($fileData[""tmp_name""],$targetPath)",,STATIC_DISPATCH,move_uploaded_file,82,move_uploaded_file,move_uploaded_file,,,1,,<unresolvedSignature>(2),ANY
30064771251,CALL,1,,"$fileData[""tmp_name""]",,STATIC_DISPATCH,,82,<operator>.indexAccess,<operator>.indexAccess,,,1,,,ANY
30064771252,CALL,-1,,$SecurityDemo\\FileUpload\\VulnerableFileUploader->handleUpload@tmp-1 = array(),,STATIC_DISPATCH,,83,<operator>.assignment,<operator>.assignment,,,1,,,ANY
30064771253,CALL,2,,array(),,STATIC_DISPATCH,array,83,array,array,,,2,,array(),array
30064771254,CALL,-1,,"$SecurityDemo\\FileUpload\\VulnerableFileUploader->handleUpload@tmp-1[""success""] = true",,STATIC_DISPATCH,,84,<operator>.assignment,<operator>.assignment,,,2,,,ANY
30064771255,CALL,1,,"$SecurityDemo\\FileUpload\\VulnerableFileUploader->handleUpload@tmp-1[""success""]",,STATIC_DISPATCH,,84,<operator>.indexAccess,<operator>.indexAccess,,,1,,,ANY
30064771256,CALL,-1,,"$SecurityDemo\\FileUpload\\VulnerableFileUploader->handleUpload@tmp-1[""message""] = ""文件上传成功""",,STATIC_DISPATCH,,85,<operator>.assignment,<operator>.assignment,,,3,,,ANY
30064771257,CALL,1,,"$SecurityDemo\\FileUpload\\VulnerableFileUploader->handleUpload@tmp-1[""message""]",,STATIC_DISPATCH,,85,<operator>.indexAccess,<operator>.indexAccess,,,1,,,ANY
30064771258,CALL,-1,,"$SecurityDemo\\FileUpload\\VulnerableFileUploader->handleUpload@tmp-1[""path""] = $targetPath",,STATIC_DISPATCH,,86,<operator>.assignment,<operator>.assignment,,,4,,,ANY
30064771259,CALL,1,,"$SecurityDemo\\FileUpload\\VulnerableFileUploader->handleUpload@tmp-1[""path""]",,STATIC_DISPATCH,,86,<operator>.indexAccess,<operator>.indexAccess,,,1,,,ANY
30064771260,CALL,-1,,"$SecurityDemo\\FileUpload\\VulnerableFileUploader->handleUpload@tmp-1[""url""] = str_replace(""./"","""",$targetPath)",,STATIC_DISPATCH,,87,<operator>.assignment,<operator>.assignment,,,5,,,ANY
30064771261,CALL,1,,"$SecurityDemo\\FileUpload\\VulnerableFileUploader->handleUpload@tmp-1[""url""]",,STATIC_DISPATCH,,87,<operator>.indexAccess,<operator>.indexAccess,,,1,,,ANY
30064771262,CALL,2,,"str_replace(""./"","""",$targetPath)",,STATIC_DISPATCH,str_replace,87,str_replace,str_replace,,,2,,<unresolvedSignature>(3),ANY
30064771263,CALL,-1,,$SecurityDemo\\FileUpload\\VulnerableFileUploader->handleUpload@tmp-2 = array(),,STATIC_DISPATCH,,91,<operator>.assignment,<operator>.assignment,,,1,,,ANY
30064771264,CALL,2,,array(),,STATIC_DISPATCH,array,91,array,array,,,2,,array(),array
30064771265,CALL,-1,,"$SecurityDemo\\FileUpload\\VulnerableFileUploader->handleUpload@tmp-2[""success""] = false",,STATIC_DISPATCH,,91,<operator>.assignment,<operator>.assignment,,,2,,,ANY
30064771266,CALL,1,,"$SecurityDemo\\FileUpload\\VulnerableFileUploader->handleUpload@tmp-2[""success""]",,STATIC_DISPATCH,,91,<operator>.indexAccess,<operator>.indexAccess,,,1,,,ANY
30064771267,CALL,-1,,"$SecurityDemo\\FileUpload\\VulnerableFileUploader->handleUpload@tmp-2[""message""] = ""文件移动失败""",,STATIC_DISPATCH,,91,<operator>.assignment,<operator>.assignment,,,3,,,ANY
30064771268,CALL,1,,"$SecurityDemo\\FileUpload\\VulnerableFileUploader->handleUpload@tmp-2[""message""]",,STATIC_DISPATCH,,91,<operator>.indexAccess,<operator>.indexAccess,,,1,,,ANY
30064771269,CALL,-1,,$SecurityDemo\\FileUpload\\VulnerableFileUploader->handleUpload@tmp-0 = array(),,STATIC_DISPATCH,,94,<operator>.assignment,<operator>.assignment,,,1,,,ANY
30064771270,CALL,2,,array(),,STATIC_DISPATCH,array,94,array,array,,,2,,array(),array
30064771271,CALL,-1,,"$SecurityDemo\\FileUpload\\VulnerableFileUploader->handleUpload@tmp-0[""success""] = false",,STATIC_DISPATCH,,94,<operator>.assignment,<operator>.assignment,,,2,,,ANY
30064771272,CALL,1,,"$SecurityDemo\\FileUpload\\VulnerableFileUploader->handleUpload@tmp-0[""success""]",,STATIC_DISPATCH,,94,<operator>.indexAccess,<operator>.indexAccess,,,1,,,ANY
30064771273,CALL,-1,,"$SecurityDemo\\FileUpload\\VulnerableFileUploader->handleUpload@tmp-0[""message""] = ""上传异常: "" . $e->getMessage()",,STATIC_DISPATCH,,94,<operator>.assignment,<operator>.assignment,,,3,,,ANY
30064771274,CALL,1,,"$SecurityDemo\\FileUpload\\VulnerableFileUploader->handleUpload@tmp-0[""message""]",,STATIC_DISPATCH,,94,<operator>.indexAccess,<operator>.indexAccess,,,1,,,ANY
30064771275,CALL,2,,"""上传异常: "" . $e->getMessage()",,STATIC_DISPATCH,,94,<operator>.concat,<operator>.concat,,,2,,,ANY
30064771276,CALL,2,,$e->getMessage(),,DYNAMIC_DISPATCH,,94,Exception->getMessage,getMessage,,,2,,<unresolvedSignature>(0),Exception->getMessage-><returnValue>
30064771277,CALL,-1,,"$fileData[""error""] !== UPLOAD_ERR_OK",,STATIC_DISPATCH,,104,<operator>.notIdentical,<operator>.notIdentical,,,1,,,ANY
30064771278,CALL,1,,"$fileData[""error""]",,STATIC_DISPATCH,,104,<operator>.indexAccess,<operator>.indexAccess,,,1,,,ANY
30064771279,CALL,2,,UPLOAD_ERR_OK,,STATIC_DISPATCH,,104,<operator>.fieldAccess,<operator>.fieldAccess,,,2,,,ANY
30064771280,CALL,-1,,"$fileData[""size""] > $this->maxFileSize",,STATIC_DISPATCH,,109,<operator>.greaterThan,<operator>.greaterThan,,,1,,,ANY
30064771281,CALL,1,,"$fileData[""size""]",,STATIC_DISPATCH,,109,<operator>.indexAccess,<operator>.indexAccess,,,1,,,ANY
30064771282,CALL,2,,$this->maxFileSize,,STATIC_DISPATCH,,109,<operator>.fieldAccess,<operator>.fieldAccess,,,2,,,ANY
30064771283,CALL,-1,,$fileName = !empty($customName) ? $customName : $originalName,,STATIC_DISPATCH,,122,<operator>.assignment,<operator>.assignment,,,7,,,ANY
30064771284,CALL,2,,!empty($customName) ? $customName : $originalName,,STATIC_DISPATCH,,122,<operator>.conditional,<operator>.conditional,,,2,,,ANY
30064771285,CALL,1,,!empty($customName),,STATIC_DISPATCH,,122,<operator>.logicalNot,<operator>.logicalNot,,,1,,,ANY
30064771286,CALL,1,,empty($customName),,STATIC_DISPATCH,empty,122,empty,empty,,,1,,,bool
30064771287,CALL,-1,,"$fileName = str_replace(,"""",$fileName)",,STATIC_DISPATCH,,125,<operator>.assignment,<operator>.assignment,,,8,,,ANY
30064771288,CALL,2,,"str_replace(,"""",$fileName)",,STATIC_DISPATCH,str_replace,125,str_replace,str_replace,,,2,,<unresolvedSignature>(3),ANY
30064771289,CALL,-1,,$SecurityDemo\\FileUpload\\VulnerableFileUploader->processFileName@tmp-0 = array(),,STATIC_DISPATCH,,125,<operator>.assignment,<operator>.assignment,,,1,,,ANY
30064771290,CALL,2,,array(),,STATIC_DISPATCH,array,125,array,array,,,2,,array(),array
30064771291,CALL,-1,,"$SecurityDemo\\FileUpload\\VulnerableFileUploader->processFileName@tmp-0[0] = ""../""",,STATIC_DISPATCH,,125,<operator>.assignment,<operator>.assignment,,,2,,,ANY
30064771292,CALL,1,,$SecurityDemo\\FileUpload\\VulnerableFileUploader->processFileName@tmp-0[0],,STATIC_DISPATCH,,125,<operator>.indexAccess,<operator>.indexAccess,,,1,,,ANY
30064771293,CALL,-1,,"$SecurityDemo\\FileUpload\\VulnerableFileUploader->processFileName@tmp-0[1] = ""..\\\\""",,STATIC_DISPATCH,,125,<operator>.assignment,<operator>.assignment,,,3,,,ANY
30064771294,CALL,1,,$SecurityDemo\\FileUpload\\VulnerableFileUploader->processFileName@tmp-0[1],,STATIC_DISPATCH,,125,<operator>.indexAccess,<operator>.indexAccess,,,1,,,ANY
30064771295,CALL,-1,,"$extension = strtolower(pathinfo($fileName,PATHINFO_EXTENSION))",,STATIC_DISPATCH,,128,<operator>.assignment,<operator>.assignment,,,9,,,ANY
30064771296,CALL,2,,"strtolower(pathinfo($fileName,PATHINFO_EXTENSION))",,STATIC_DISPATCH,strtolower,128,strtolower,strtolower,,,2,,<unresolvedSignature>(1),ANY
30064771297,CALL,1,,"pathinfo($fileName,PATHINFO_EXTENSION)",,STATIC_DISPATCH,pathinfo,128,pathinfo,pathinfo,,,1,,<unresolvedSignature>(2),ANY
30064771298,CALL,2,,PATHINFO_EXTENSION,,STATIC_DISPATCH,,128,<operator>.fieldAccess,<operator>.fieldAccess,,,2,,,ANY
30064771299,CALL,-1,,$SecurityDemo\\FileUpload\\VulnerableFileUploader->processFileName@iter_tmp-1 = $this->blockedExtensions,,STATIC_DISPATCH,,131,<operator>.assignment,<operator>.assignment,,,1,,,ANY
30064771300,CALL,2,,$this->blockedExtensions,,STATIC_DISPATCH,,131,<operator>.fieldAccess,<operator>.fieldAccess,,,2,,,ANY
30064771301,CALL,-1,,$blocked = $SecurityDemo\\FileUpload\\VulnerableFileUploader->processFileName@iter_tmp-1->current(),,STATIC_DISPATCH,,131,<operator>.assignment,<operator>.assignment,,,2,,,ANY
30064771302,CALL,2,,$SecurityDemo\\FileUpload\\VulnerableFileUploader->processFileName@iter_tmp-1->current(),,DYNAMIC_DISPATCH,Iterator.current,131,Iterator.current,current,,,2,,<unresolvedSignature>(0),ANY
30064771303,CALL,-1,,!is_null($blocked),,STATIC_DISPATCH,,131,<operator>.logicalNot,<operator>.logicalNot,,,2,,,ANY
30064771304,CALL,1,,is_null($blocked),,STATIC_DISPATCH,is_null,131,is_null,is_null,,,1,,,bool
30064771305,CALL,-1,,$SecurityDemo\\FileUpload\\VulnerableFileUploader->processFileName@iter_tmp-1->next(),,DYNAMIC_DISPATCH,Iterator.next,131,Iterator.next,next,,,1,,void(),ANY
30064771306,CALL,-1,,$blocked = $SecurityDemo\\FileUpload\\VulnerableFileUploader->processFileName@iter_tmp-1->current(),,STATIC_DISPATCH,,131,<operator>.assignment,<operator>.assignment,,,2,,,ANY
30064771307,CALL,2,,$SecurityDemo\\FileUpload\\VulnerableFileUploader->processFileName@iter_tmp-1->current(),,DYNAMIC_DISPATCH,Iterator.current,131,Iterator.current,current,,,2,,<unresolvedSignature>(0),ANY
30064771308,CALL,-1,,$extension === $blocked,,STATIC_DISPATCH,,132,<operator>.identical,<operator>.identical,,,1,,,ANY
30064771309,CALL,-1,,"$fileName .= "".txt""",,STATIC_DISPATCH,,134,<operator>.assignmentConcat,<operator>.assignmentConcat,,,1,,,ANY
30064771310,CALL,-1,,$this->imageMagicBytes = ,,STATIC_DISPATCH,,29,<operator>.assignment,<operator>.assignment,,,6,,,ANY
30064771311,CALL,1,,$this->imageMagicBytes,,STATIC_DISPATCH,,29,<operator>.fieldAccess,<operator>.fieldAccess,,,1,,,ANY
30064771312,CALL,-1,,$SecurityDemo\\FileUpload\\FileValidator->__construct@tmp-0 = array(),,STATIC_DISPATCH,,29,<operator>.assignment,<operator>.assignment,,,1,,,ANY
30064771313,CALL,2,,array(),,STATIC_DISPATCH,array,29,array,array,,,2,,array(),array
30064771314,CALL,-1,,"$SecurityDemo\\FileUpload\\FileValidator->__construct@tmp-0[""jpeg""] = ",,STATIC_DISPATCH,,30,<operator>.assignment,<operator>.assignment,,,2,,,ANY
30064771315,CALL,1,,"$SecurityDemo\\FileUpload\\FileValidator->__construct@tmp-0[""jpeg""]",,STATIC_DISPATCH,,30,<operator>.indexAccess,<operator>.indexAccess,,,1,,,ANY
30064771316,CALL,-1,,$SecurityDemo\\FileUpload\\FileValidator->__construct@tmp-1 = array(),,STATIC_DISPATCH,,30,<operator>.assignment,<operator>.assignment,,,1,,,ANY
30064771317,CALL,2,,array(),,STATIC_DISPATCH,array,30,array,array,,,2,,array(),array
30064771318,CALL,-1,,$SecurityDemo\\FileUpload\\FileValidator->__construct@tmp-1[0] = 255,,STATIC_DISPATCH,,30,<operator>.assignment,<operator>.assignment,,,2,,,ANY
30064771319,CALL,1,,$SecurityDemo\\FileUpload\\FileValidator->__construct@tmp-1[0],,STATIC_DISPATCH,,30,<operator>.indexAccess,<operator>.indexAccess,,,1,,,ANY
30064771320,CALL,-1,,$SecurityDemo\\FileUpload\\FileValidator->__construct@tmp-1[1] = 216,,STATIC_DISPATCH,,30,<operator>.assignment,<operator>.assignment,,,3,,,ANY
30064771321,CALL,1,,$SecurityDemo\\FileUpload\\FileValidator->__construct@tmp-1[1],,STATIC_DISPATCH,,30,<operator>.indexAccess,<operator>.indexAccess,,,1,,,ANY
30064771322,CALL,-1,,$SecurityDemo\\FileUpload\\FileValidator->__construct@tmp-1[2] = 255,,STATIC_DISPATCH,,30,<operator>.assignment,<operator>.assignment,,,4,,,ANY
30064771323,CALL,1,,$SecurityDemo\\FileUpload\\FileValidator->__construct@tmp-1[2],,STATIC_DISPATCH,,30,<operator>.indexAccess,<operator>.indexAccess,,,1,,,ANY
30064771324,CALL,-1,,"$SecurityDemo\\FileUpload\\FileValidator->__construct@tmp-0[""png""] = ",,STATIC_DISPATCH,,31,<operator>.assignment,<operator>.assignment,,,3,,,ANY
30064771325,CALL,1,,"$SecurityDemo\\FileUpload\\FileValidator->__construct@tmp-0[""png""]",,STATIC_DISPATCH,,31,<operator>.indexAccess,<operator>.indexAccess,,,1,,,ANY
30064771326,CALL,-1,,$SecurityDemo\\FileUpload\\FileValidator->__construct@tmp-2 = array(),,STATIC_DISPATCH,,31,<operator>.assignment,<operator>.assignment,,,1,,,ANY
30064771327,CALL,2,,array(),,STATIC_DISPATCH,array,31,array,array,,,2,,array(),array
30064771328,CALL,-1,,$SecurityDemo\\FileUpload\\FileValidator->__construct@tmp-2[0] = 137,,STATIC_DISPATCH,,31,<operator>.assignment,<operator>.assignment,,,2,,,ANY
30064771329,CALL,1,,$SecurityDemo\\FileUpload\\FileValidator->__construct@tmp-2[0],,STATIC_DISPATCH,,31,<operator>.indexAccess,<operator>.indexAccess,,,1,,,ANY
30064771330,CALL,-1,,$SecurityDemo\\FileUpload\\FileValidator->__construct@tmp-2[1] = 80,,STATIC_DISPATCH,,31,<operator>.assignment,<operator>.assignment,,,3,,,ANY
30064771331,CALL,1,,$SecurityDemo\\FileUpload\\FileValidator->__construct@tmp-2[1],,STATIC_DISPATCH,,31,<operator>.indexAccess,<operator>.indexAccess,,,1,,,ANY
30064771332,CALL,-1,,$SecurityDemo\\FileUpload\\FileValidator->__construct@tmp-2[2] = 78,,STATIC_DISPATCH,,31,<operator>.assignment,<operator>.assignment,,,4,,,ANY
30064771333,CALL,1,,$SecurityDemo\\FileUpload\\FileValidator->__construct@tmp-2[2],,STATIC_DISPATCH,,31,<operator>.indexAccess,<operator>.indexAccess,,,1,,,ANY
30064771334,CALL,-1,,$SecurityDemo\\FileUpload\\FileValidator->__construct@tmp-2[3] = 71,,STATIC_DISPATCH,,31,<operator>.assignment,<operator>.assignment,,,5,,,ANY
30064771335,CALL,1,,$SecurityDemo\\FileUpload\\FileValidator->__construct@tmp-2[3],,STATIC_DISPATCH,,31,<operator>.indexAccess,<operator>.indexAccess,,,1,,,ANY
30064771336,CALL,-1,,"$SecurityDemo\\FileUpload\\FileValidator->__construct@tmp-0[""gif""] = ",,STATIC_DISPATCH,,32,<operator>.assignment,<operator>.assignment,,,4,,,ANY
30064771337,CALL,1,,"$SecurityDemo\\FileUpload\\FileValidator->__construct@tmp-0[""gif""]",,STATIC_DISPATCH,,32,<operator>.indexAccess,<operator>.indexAccess,,,1,,,ANY
30064771338,CALL,-1,,$SecurityDemo\\FileUpload\\FileValidator->__construct@tmp-3 = array(),,STATIC_DISPATCH,,32,<operator>.assignment,<operator>.assignment,,,1,,,ANY
30064771339,CALL,2,,array(),,STATIC_DISPATCH,array,32,array,array,,,2,,array(),array
30064771340,CALL,-1,,$SecurityDemo\\FileUpload\\FileValidator->__construct@tmp-3[0] = 71,,STATIC_DISPATCH,,32,<operator>.assignment,<operator>.assignment,,,2,,,ANY
30064771341,CALL,1,,$SecurityDemo\\FileUpload\\FileValidator->__construct@tmp-3[0],,STATIC_DISPATCH,,32,<operator>.indexAccess,<operator>.indexAccess,,,1,,,ANY
30064771342,CALL,-1,,$SecurityDemo\\FileUpload\\FileValidator->__construct@tmp-3[1] = 73,,STATIC_DISPATCH,,32,<operator>.assignment,<operator>.assignment,,,3,,,ANY
30064771343,CALL,1,,$SecurityDemo\\FileUpload\\FileValidator->__construct@tmp-3[1],,STATIC_DISPATCH,,32,<operator>.indexAccess,<operator>.indexAccess,,,1,,,ANY
30064771344,CALL,-1,,$SecurityDemo\\FileUpload\\FileValidator->__construct@tmp-3[2] = 70,,STATIC_DISPATCH,,32,<operator>.assignment,<operator>.assignment,,,4,,,ANY
30064771345,CALL,1,,$SecurityDemo\\FileUpload\\FileValidator->__construct@tmp-3[2],,STATIC_DISPATCH,,32,<operator>.indexAccess,<operator>.indexAccess,,,1,,,ANY
30064771346,CALL,-1,,$SecurityDemo\\FileUpload\\FileValidator->__construct@tmp-3[3] = 56,,STATIC_DISPATCH,,32,<operator>.assignment,<operator>.assignment,,,5,,,ANY
30064771347,CALL,1,,$SecurityDemo\\FileUpload\\FileValidator->__construct@tmp-3[3],,STATIC_DISPATCH,,32,<operator>.indexAccess,<operator>.indexAccess,,,1,,,ANY
30064771348,CALL,-1,,$this->dangerousPatterns = ,,STATIC_DISPATCH,,36,<operator>.assignment,<operator>.assignment,,,7,,,ANY
30064771349,CALL,1,,$this->dangerousPatterns,,STATIC_DISPATCH,,36,<operator>.fieldAccess,<operator>.fieldAccess,,,1,,,ANY
30064771350,CALL,-1,,$SecurityDemo\\FileUpload\\FileValidator->__construct@tmp-4 = array(),,STATIC_DISPATCH,,36,<operator>.assignment,<operator>.assignment,,,1,,,ANY
30064771351,CALL,2,,array(),,STATIC_DISPATCH,array,36,array,array,,,2,,array(),array
30064771352,CALL,-1,,"$SecurityDemo\\FileUpload\\FileValidator->__construct@tmp-4[0] = ""<?php""",,STATIC_DISPATCH,,37,<operator>.assignment,<operator>.assignment,,,2,,,ANY
30064771353,CALL,1,,$SecurityDemo\\FileUpload\\FileValidator->__construct@tmp-4[0],,STATIC_DISPATCH,,37,<operator>.indexAccess,<operator>.indexAccess,,,1,,,ANY
30064771354,CALL,-1,,"$SecurityDemo\\FileUpload\\FileValidator->__construct@tmp-4[1] = ""<?=""",,STATIC_DISPATCH,,38,<operator>.assignment,<operator>.assignment,,,3,,,ANY
30064771355,CALL,1,,$SecurityDemo\\FileUpload\\FileValidator->__construct@tmp-4[1],,STATIC_DISPATCH,,38,<operator>.indexAccess,<operator>.indexAccess,,,1,,,ANY
30064771356,CALL,-1,,"$SecurityDemo\\FileUpload\\FileValidator->__construct@tmp-4[2] = ""<script""",,STATIC_DISPATCH,,39,<operator>.assignment,<operator>.assignment,,,4,,,ANY
30064771357,CALL,1,,$SecurityDemo\\FileUpload\\FileValidator->__construct@tmp-4[2],,STATIC_DISPATCH,,39,<operator>.indexAccess,<operator>.indexAccess,,,1,,,ANY
30064771358,CALL,-1,,"$SecurityDemo\\FileUpload\\FileValidator->__construct@tmp-4[3] = ""eval(""",,STATIC_DISPATCH,,40,<operator>.assignment,<operator>.assignment,,,5,,,ANY
30064771359,CALL,1,,$SecurityDemo\\FileUpload\\FileValidator->__construct@tmp-4[3],,STATIC_DISPATCH,,40,<operator>.indexAccess,<operator>.indexAccess,,,1,,,ANY
30064771360,CALL,-1,,"$SecurityDemo\\FileUpload\\FileValidator->__construct@tmp-4[4] = ""system(""",,STATIC_DISPATCH,,41,<operator>.assignment,<operator>.assignment,,,6,,,ANY
30064771361,CALL,1,,$SecurityDemo\\FileUpload\\FileValidator->__construct@tmp-4[4],,STATIC_DISPATCH,,41,<operator>.indexAccess,<operator>.indexAccess,,,1,,,ANY
30064771362,CALL,-1,,"$SecurityDemo\\FileUpload\\FileValidator->__construct@tmp-4[5] = ""exec(""",,STATIC_DISPATCH,,42,<operator>.assignment,<operator>.assignment,,,7,,,ANY
30064771363,CALL,1,,$SecurityDemo\\FileUpload\\FileValidator->__construct@tmp-4[5],,STATIC_DISPATCH,,42,<operator>.indexAccess,<operator>.indexAccess,,,1,,,ANY
30064771364,CALL,-1,,!file_exists($filePath),,STATIC_DISPATCH,,54,<operator>.logicalNot,<operator>.logicalNot,,,1,,,ANY
30064771365,CALL,1,,file_exists($filePath),,STATIC_DISPATCH,file_exists,54,file_exists,file_exists,,,1,,<unresolvedSignature>(1),ANY
30064771366,CALL,-1,,!$this->checkMagicBytes($filePath),,STATIC_DISPATCH,,59,<operator>.logicalNot,<operator>.logicalNot,,,1,,,ANY
30064771367,CALL,1,,$this->checkMagicBytes($filePath),,DYNAMIC_DISPATCH,,59,SecurityDemo\\FileUpload\\FileValidator->checkMagicBytes,checkMagicBytes,,,1,,<unresolvedSignature>(1),bool
30064771368,CALL,-1,,!$this->checkFileContent($filePath),,STATIC_DISPATCH,,64,<operator>.logicalNot,<operator>.logicalNot,,,1,,,ANY
30064771369,CALL,1,,$this->checkFileContent($filePath),,DYNAMIC_DISPATCH,,64,SecurityDemo\\FileUpload\\FileValidator->checkFileContent,checkFileContent,,,1,,<unresolvedSignature>(1),bool
30064771370,CALL,-1,,"$handle = fopen($filePath,""rb"")",,STATIC_DISPATCH,,76,<operator>.assignment,<operator>.assignment,,,8,,,ANY
30064771371,CALL,2,,"fopen($filePath,""rb"")",,STATIC_DISPATCH,fopen,76,fopen,fopen,,,2,,<unresolvedSignature>(2),ANY
30064771372,CALL,-1,,!$handle,,STATIC_DISPATCH,,77,<operator>.logicalNot,<operator>.logicalNot,,,1,,,ANY
30064771373,CALL,-1,,"$header = fread($handle,8)",,STATIC_DISPATCH,,81,<operator>.assignment,<operator>.assignment,,,10,,,ANY
30064771374,CALL,2,,"fread($handle,8)",,STATIC_DISPATCH,fread,81,fread,fread,,,2,,<unresolvedSignature>(2),ANY
30064771375,CALL,-1,,fclose($handle),,STATIC_DISPATCH,fclose,82,fclose,fclose,,,11,,<unresolvedSignature>(1),ANY
30064771376,CALL,-1,,$SecurityDemo\\FileUpload\\FileValidator->checkMagicBytes@iter_tmp-0 = $this->imageMagicBytes,,STATIC_DISPATCH,,85,<operator>.assignment,<operator>.assignment,,,1,,,ANY
30064771377,CALL,2,,$this->imageMagicBytes,,STATIC_DISPATCH,,85,<operator>.fieldAccess,<operator>.fieldAccess,,,2,,,ANY
30064771378,CALL,-1,,$type = $SecurityDemo\\FileUpload\\FileValidator->checkMagicBytes@iter_tmp-0->key(),,STATIC_DISPATCH,,85,<operator>.assignment,<operator>.assignment,,,1,,,ANY
30064771379,CALL,2,,$SecurityDemo\\FileUpload\\FileValidator->checkMagicBytes@iter_tmp-0->key(),,DYNAMIC_DISPATCH,Iterator.key,85,Iterator.key,key,,,2,,<unresolvedSignature>(0),ANY
30064771380,CALL,-1,,$bytes = $SecurityDemo\\FileUpload\\FileValidator->checkMagicBytes@iter_tmp-0->current(),,STATIC_DISPATCH,,85,<operator>.assignment,<operator>.assignment,,,2,,,ANY
30064771381,CALL,2,,$SecurityDemo\\FileUpload\\FileValidator->checkMagicBytes@iter_tmp-0->current(),,DYNAMIC_DISPATCH,Iterator.current,85,Iterator.current,current,,,2,,<unresolvedSignature>(0),ANY
30064771382,CALL,-1,,!is_null($bytes),,STATIC_DISPATCH,,85,<operator>.logicalNot,<operator>.logicalNot,,,2,,,ANY
30064771383,CALL,1,,is_null($bytes),,STATIC_DISPATCH,is_null,85,is_null,is_null,,,1,,,bool
30064771384,CALL,-1,,$SecurityDemo\\FileUpload\\FileValidator->checkMagicBytes@iter_tmp-0->next(),,DYNAMIC_DISPATCH,Iterator.next,85,Iterator.next,next,,,1,,void(),ANY
30064771385,CALL,-1,,$type = $SecurityDemo\\FileUpload\\FileValidator->checkMagicBytes@iter_tmp-0->key(),,STATIC_DISPATCH,,85,<operator>.assignment,<operator>.assignment,,,1,,,ANY
30064771386,CALL,2,,$SecurityDemo\\FileUpload\\FileValidator->checkMagicBytes@iter_tmp-0->key(),,DYNAMIC_DISPATCH,Iterator.key,85,Iterator.key,key,,,2,,<unresolvedSignature>(0),ANY
30064771387,CALL,-1,,$bytes = $SecurityDemo\\FileUpload\\FileValidator->checkMagicBytes@iter_tmp-0->current(),,STATIC_DISPATCH,,85,<operator>.assignment,<operator>.assignment,,,2,,,ANY
30064771388,CALL,2,,$SecurityDemo\\FileUpload\\FileValidator->checkMagicBytes@iter_tmp-0->current(),,DYNAMIC_DISPATCH,Iterator.current,85,Iterator.current,current,,,2,,<unresolvedSignature>(0),ANY
30064771389,CALL,-1,,$match = true,,STATIC_DISPATCH,,86,<operator>.assignment,<operator>.assignment,,,1,,,ANY
30064771390,CALL,-1,,$i = 0,,STATIC_DISPATCH,,87,<operator>.assignment,<operator>.assignment,,,1,,,ANY
30064771391,CALL,-1,,$i < count($bytes),,STATIC_DISPATCH,,87,<operator>.lessThan,<operator>.lessThan,,,2,,,ANY
30064771392,CALL,2,,count($bytes),,STATIC_DISPATCH,count,87,count,count,,,2,,<unresolvedSignature>(1),ANY
30064771393,CALL,-1,,$i++,,STATIC_DISPATCH,,87,<operator>.postIncrement,<operator>.postIncrement,,,3,,,ANY
30064771394,CALL,-1,,!isset($header[$i]) || ord($header[$i]) !== $bytes[$i],,STATIC_DISPATCH,,88,<operator>.logicalOr,<operator>.logicalOr,,,1,,,ANY
30064771395,CALL,1,,!isset($header[$i]),,STATIC_DISPATCH,,88,<operator>.logicalNot,<operator>.logicalNot,,,1,,,ANY
30064771396,CALL,1,,isset($header[$i]),,STATIC_DISPATCH,isset,88,isset,isset,,,1,,,bool
30064771397,CALL,1,,$header[$i],,STATIC_DISPATCH,,88,<operator>.indexAccess,<operator>.indexAccess,,,1,,,ANY
30064771398,CALL,2,,ord($header[$i]) !== $bytes[$i],,STATIC_DISPATCH,,88,<operator>.notIdentical,<operator>.notIdentical,,,2,,,ANY
30064771399,CALL,1,,ord($header[$i]),,STATIC_DISPATCH,ord,88,ord,ord,,,1,,<unresolvedSignature>(1),ANY
30064771400,CALL,1,,$header[$i],,STATIC_DISPATCH,,88,<operator>.indexAccess,<operator>.indexAccess,,,1,,,ANY
30064771401,CALL,2,,$bytes[$i],,STATIC_DISPATCH,,88,<operator>.indexAccess,<operator>.indexAccess,,,2,,,ANY
30064771402,CALL,-1,,$match = false,,STATIC_DISPATCH,,89,<operator>.assignment,<operator>.assignment,,,1,,,ANY
30064771403,CALL,-1,,$content = file_get_contents($filePath),,STATIC_DISPATCH,,106,<operator>.assignment,<operator>.assignment,,,4,,,ANY
30064771404,CALL,2,,file_get_contents($filePath),,STATIC_DISPATCH,file_get_contents,106,file_get_contents,file_get_contents,,,2,,<unresolvedSignature>(1),ANY
30064771405,CALL,-1,,$content === false,,STATIC_DISPATCH,,107,<operator>.identical,<operator>.identical,,,1,,,ANY
30064771406,CALL,-1,,$SecurityDemo\\FileUpload\\FileValidator->checkFileContent@iter_tmp-0 = $this->dangerousPatterns,,STATIC_DISPATCH,,112,<operator>.assignment,<operator>.assignment,,,1,,,ANY
30064771407,CALL,2,,$this->dangerousPatterns,,STATIC_DISPATCH,,112,<operator>.fieldAccess,<operator>.fieldAccess,,,2,,,ANY
30064771408,CALL,-1,,$pattern = $SecurityDemo\\FileUpload\\FileValidator->checkFileContent@iter_tmp-0->current(),,STATIC_DISPATCH,,112,<operator>.assignment,<operator>.assignment,,,2,,,ANY
30064771409,CALL,2,,$SecurityDemo\\FileUpload\\FileValidator->checkFileContent@iter_tmp-0->current(),,DYNAMIC_DISPATCH,Iterator.current,112,Iterator.current,current,,,2,,<unresolvedSignature>(0),ANY
30064771410,CALL,-1,,!is_null($pattern),,STATIC_DISPATCH,,112,<operator>.logicalNot,<operator>.logicalNot,,,2,,,ANY
30064771411,CALL,1,,is_null($pattern),,STATIC_DISPATCH,is_null,112,is_null,is_null,,,1,,,bool
30064771412,CALL,-1,,$SecurityDemo\\FileUpload\\FileValidator->checkFileContent@iter_tmp-0->next(),,DYNAMIC_DISPATCH,Iterator.next,112,Iterator.next,next,,,1,,void(),ANY
30064771413,CALL,-1,,$pattern = $SecurityDemo\\FileUpload\\FileValidator->checkFileContent@iter_tmp-0->current(),,STATIC_DISPATCH,,112,<operator>.assignment,<operator>.assignment,,,2,,,ANY
30064771414,CALL,2,,$SecurityDemo\\FileUpload\\FileValidator->checkFileContent@iter_tmp-0->current(),,DYNAMIC_DISPATCH,Iterator.current,112,Iterator.current,current,,,2,,<unresolvedSignature>(0),ANY
30064771415,CALL,-1,,"strpos($content,$pattern) !== false",,STATIC_DISPATCH,,114,<operator>.notIdentical,<operator>.notIdentical,,,1,,,ANY
30064771416,CALL,1,,"strpos($content,$pattern)",,STATIC_DISPATCH,strpos,114,strpos,strpos,,,1,,<unresolvedSignature>(2),ANY
30064771417,CALL,-1,,$extension = $this->getFileExtension($fileName),,STATIC_DISPATCH,,134,<operator>.assignment,<operator>.assignment,,,4,,,ANY
30064771418,CALL,2,,$this->getFileExtension($fileName),,DYNAMIC_DISPATCH,,134,SecurityDemo\\FileUpload\\FileValidator->getFileExtension,getFileExtension,,,2,,<unresolvedSignature>(1),string
30064771419,CALL,-1,,$allowedExtensions = ,,STATIC_DISPATCH,,137,<operator>.assignment,<operator>.assignment,,,5,,,ANY
30064771420,CALL,-1,,$SecurityDemo\\FileUpload\\FileValidator->validateFileExtension@tmp-0 = array(),,STATIC_DISPATCH,,137,<operator>.assignment,<operator>.assignment,,,1,,,ANY
30064771421,CALL,2,,array(),,STATIC_DISPATCH,array,137,array,array,,,2,,array(),array
30064771422,CALL,-1,,"$SecurityDemo\\FileUpload\\FileValidator->validateFileExtension@tmp-0[0] = ""jpg""",,STATIC_DISPATCH,,137,<operator>.assignment,<operator>.assignment,,,2,,,ANY
30064771423,CALL,1,,$SecurityDemo\\FileUpload\\FileValidator->validateFileExtension@tmp-0[0],,STATIC_DISPATCH,,137,<operator>.indexAccess,<operator>.indexAccess,,,1,,,ANY
30064771424,CALL,-1,,"$SecurityDemo\\FileUpload\\FileValidator->validateFileExtension@tmp-0[1] = ""jpeg""",,STATIC_DISPATCH,,137,<operator>.assignment,<operator>.assignment,,,3,,,ANY
30064771425,CALL,1,,$SecurityDemo\\FileUpload\\FileValidator->validateFileExtension@tmp-0[1],,STATIC_DISPATCH,,137,<operator>.indexAccess,<operator>.indexAccess,,,1,,,ANY
30064771426,CALL,-1,,"$SecurityDemo\\FileUpload\\FileValidator->validateFileExtension@tmp-0[2] = ""png""",,STATIC_DISPATCH,,137,<operator>.assignment,<operator>.assignment,,,4,,,ANY
30064771427,CALL,1,,$SecurityDemo\\FileUpload\\FileValidator->validateFileExtension@tmp-0[2],,STATIC_DISPATCH,,137,<operator>.indexAccess,<operator>.indexAccess,,,1,,,ANY
30064771428,CALL,-1,,"$SecurityDemo\\FileUpload\\FileValidator->validateFileExtension@tmp-0[3] = ""gif""",,STATIC_DISPATCH,,137,<operator>.assignment,<operator>.assignment,,,5,,,ANY
30064771429,CALL,1,,$SecurityDemo\\FileUpload\\FileValidator->validateFileExtension@tmp-0[3],,STATIC_DISPATCH,,137,<operator>.indexAccess,<operator>.indexAccess,,,1,,,ANY
30064771430,CALL,-1,,"$SecurityDemo\\FileUpload\\FileValidator->validateFileExtension@tmp-0[4] = ""txt""",,STATIC_DISPATCH,,137,<operator>.assignment,<operator>.assignment,,,6,,,ANY
30064771431,CALL,1,,$SecurityDemo\\FileUpload\\FileValidator->validateFileExtension@tmp-0[4],,STATIC_DISPATCH,,137,<operator>.indexAccess,<operator>.indexAccess,,,1,,,ANY
30064771432,CALL,1,,"in_array($extension,$allowedExtensions)",,STATIC_DISPATCH,in_array,139,in_array,in_array,,,1,,<unresolvedSignature>(2),ANY
30064771433,CALL,-1,,"$parts = explode(""."",$fileName)",,STATIC_DISPATCH,,149,<operator>.assignment,<operator>.assignment,,,2,,,ANY
30064771434,CALL,2,,"explode(""."",$fileName)",,STATIC_DISPATCH,explode,149,explode,explode,,,2,,<unresolvedSignature>(2),ANY
30064771435,CALL,1,,strtolower(end($parts)),,STATIC_DISPATCH,strtolower,150,strtolower,strtolower,,,1,,<unresolvedSignature>(1),ANY
30064771436,CALL,1,,end($parts),,STATIC_DISPATCH,end,150,end,end,,,1,,<unresolvedSignature>(1),ANY
30064771437,CALL,-1,,$fileName = $originalName,,STATIC_DISPATCH,,162,<operator>.assignment,<operator>.assignment,,,2,,,ANY
30064771438,CALL,-1,,"$fileName = str_replace(,"""",$fileName)",,STATIC_DISPATCH,,165,<operator>.assignment,<operator>.assignment,,,3,,,ANY
30064771439,CALL,2,,"str_replace(,"""",$fileName)",,STATIC_DISPATCH,str_replace,165,str_replace,str_replace,,,2,,<unresolvedSignature>(3),ANY
30064771440,CALL,-1,,$SecurityDemo\\FileUpload\\FileValidator->sanitizeFileName@tmp-0 = array(),,STATIC_DISPATCH,,165,<operator>.assignment,<operator>.assignment,,,1,,,ANY
30064771441,CALL,2,,array(),,STATIC_DISPATCH,array,165,array,array,,,2,,array(),array
30064771442,CALL,-1,,"$SecurityDemo\\FileUpload\\FileValidator->sanitizeFileName@tmp-0[0] = ""../""",,STATIC_DISPATCH,,165,<operator>.assignment,<operator>.assignment,,,2,,,ANY
30064771443,CALL,1,,$SecurityDemo\\FileUpload\\FileValidator->sanitizeFileName@tmp-0[0],,STATIC_DISPATCH,,165,<operator>.indexAccess,<operator>.indexAccess,,,1,,,ANY
30064771444,CALL,-1,,"$SecurityDemo\\FileUpload\\FileValidator->sanitizeFileName@tmp-0[1] = ""..\\\\""",,STATIC_DISPATCH,,165,<operator>.assignment,<operator>.assignment,,,3,,,ANY
30064771445,CALL,1,,$SecurityDemo\\FileUpload\\FileValidator->sanitizeFileName@tmp-0[1],,STATIC_DISPATCH,,165,<operator>.indexAccess,<operator>.indexAccess,,,1,,,ANY
30064771446,CALL,-1,,"$SecurityDemo\\FileUpload\\FileValidator->sanitizeFileName@tmp-0[2] = ""<""",,STATIC_DISPATCH,,165,<operator>.assignment,<operator>.assignment,,,4,,,ANY
30064771447,CALL,1,,$SecurityDemo\\FileUpload\\FileValidator->sanitizeFileName@tmp-0[2],,STATIC_DISPATCH,,165,<operator>.indexAccess,<operator>.indexAccess,,,1,,,ANY
30064771448,CALL,-1,,"$SecurityDemo\\FileUpload\\FileValidator->sanitizeFileName@tmp-0[3] = "">""",,STATIC_DISPATCH,,165,<operator>.assignment,<operator>.assignment,,,5,,,ANY
30064771449,CALL,1,,$SecurityDemo\\FileUpload\\FileValidator->sanitizeFileName@tmp-0[3],,STATIC_DISPATCH,,165,<operator>.indexAccess,<operator>.indexAccess,,,1,,,ANY
30064771450,CALL,-1,,"$SecurityDemo\\FileUpload\\FileValidator->sanitizeFileName@tmp-0[4] = ""|""",,STATIC_DISPATCH,,165,<operator>.assignment,<operator>.assignment,,,6,,,ANY
30064771451,CALL,1,,$SecurityDemo\\FileUpload\\FileValidator->sanitizeFileName@tmp-0[4],,STATIC_DISPATCH,,165,<operator>.indexAccess,<operator>.indexAccess,,,1,,,ANY
