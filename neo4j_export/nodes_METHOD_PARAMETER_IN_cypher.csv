LOAD CSV FROM 'file:/nodes_METHOD_PARAMETER_IN_data.csv' AS line
CREATE (:METHOD_PARAMETER_IN {
id: toInteger(line[0]),
CLOSURE_BINDING_ID: line[2],
CODE: line[3],
COLUMN_NUMBER: line[4],
DY<PERSON>MIC_TYPE_HINT_FULL_NAME: toStringList(split(line[5], ";")),
EVALUATION_STRATEGY: line[6],
INDEX: toInteger(line[7]),
IS_VARIADIC: toBoolean(line[8]),
LINE_NUMBER: toInteger(line[9]),
NAME: line[10],
OFFSET: line[11],
OFFSET_END: line[12],
ORDER: toInteger(line[13]),
POSSIBLE_TYPES: split(line[14], ";"),
TYPE_FULL_NAME: line[15]
});
