LOAD CSV FROM 'file:/nodes_IDENTIFIER_data.csv' AS line
CREATE (:IDENTIFIER {
id: toInteger(line[0]),
ARGUMENT_INDEX: toInteger(line[2]),
ARGUMENT_NAME: line[3],
CODE: line[4],
COLUMN_NUMBER: line[5],
DYNAMIC_TYPE_HINT_FULL_NAME: toStringList(split(line[6], ";")),
LINE_NUMBER: toInteger(line[7]),
NAME: line[8],
OFFSET: line[9],
OFFSET_END: line[10],
ORDER: toInteger(line[11]),
POSSIBLE_TYPES: split(line[12], ";"),
TYPE_FULL_NAME: line[13]
});
