111669149696,<PERSON><PERSON><PERSON>,FileUploadHandler.php:<global>,TYPE_DECL,VIRTUAL PUBLIC STATIC function <global>(),,,FileUploadHandler.php,FileUploadHandler.php:<global>,<empty>,,false,9,,<global>,,,1,<unresolvedSignature>(0)
111669149697,METHOD,SecurityDemo\\FileUpload\\FileUploadController,TYPE_DECL,PUBLIC function __construct(this),,,FileUploadHandler.php,SecurityDemo\\FileUpload\\FileUploadController->__construct,<empty>,,false,25,,__construct,,,3,<unresolvedSignature>(0)
111669149698,METHOD,SecurityDemo\\FileUpload\\FileUploadController,TYPE_DECL,PUBLIC function handleRequest(this),,,FileUploadHandler.php,SecurityDemo\\FileUpload\\FileUploadController->handleRequest,<empty>,,false,35,,handleRequest,,,4,<unresolvedSignature>(0)
111669149699,METHOD,SecurityDemo\\FileUpload\\FileUploadController,TYPE_DECL,PRIVATE function processUpload(this),,,FileUploadHandler.php,SecurityDemo\\FileUpload\\FileUploadController->processUpload,<empty>,,false,52,,processUpload,,,5,<unresolvedSignature>(0)
111669149700,METHOD,SecurityDemo\\FileUpload\\FileUploadController,TYPE_DECL,PRIVATE function processBatchUpload(this),,,FileUploadHandler.php,SecurityDemo\\FileUpload\\FileUploadController->processBatchUpload,<empty>,,false,75,,processBatchUpload,,,6,<unresolvedSignature>(0)
111669149701,METHOD,SecurityDemo\\FileUpload\\FileUploadController,TYPE_DECL,"PUBLIC function sendResponse(this,$data)",,,FileUploadHandler.php,SecurityDemo\\FileUpload\\FileUploadController->sendResponse,<empty>,,false,107,,sendResponse,,,7,<unresolvedSignature>(1)
111669149702,METHOD,FileUploadVulnerability.php:<global>,TYPE_DECL,VIRTUAL PUBLIC STATIC function <global>(),,,FileUploadVulnerability.php,FileUploadVulnerability.php:<global>,<empty>,,false,12,,<global>,,,1,<unresolvedSignature>(0)
111669149703,METHOD,SecurityDemo\\FileUpload\\VulnerableFileUploader,TYPE_DECL,"PUBLIC function __construct(this,$uploadDir)",,,FileUploadVulnerability.php,SecurityDemo\\FileUpload\\VulnerableFileUploader->__construct,<empty>,,false,32,,__construct,,,5,<unresolvedSignature>(1)
111669149704,METHOD,SecurityDemo\\FileUpload\\VulnerableFileUploader,TYPE_DECL,PRIVATE function createUploadDirectory(this),,,FileUploadVulnerability.php,SecurityDemo\\FileUpload\\VulnerableFileUploader->createUploadDirectory,<empty>,,false,52,,createUploadDirectory,,,6,<unresolvedSignature>(0)
111669149705,METHOD,SecurityDemo\\FileUpload\\VulnerableFileUploader,TYPE_DECL,"PUBLIC function handleUpload(this,$fileData,$customName)",,,FileUploadVulnerability.php,SecurityDemo\\FileUpload\\VulnerableFileUploader->handleUpload,<empty>,,false,67,,handleUpload,,,7,<unresolvedSignature>(2)
111669149706,METHOD,SecurityDemo\\FileUpload\\VulnerableFileUploader,TYPE_DECL,"PRIVATE function validateFileUpload(this,$fileData)",,,FileUploadVulnerability.php,SecurityDemo\\FileUpload\\VulnerableFileUploader->validateFileUpload,<empty>,,false,101,,validateFileUpload,,,8,<unresolvedSignature>(1)
111669149707,METHOD,SecurityDemo\\FileUpload\\VulnerableFileUploader,TYPE_DECL,"PRIVATE function processFileName(this,$originalName,$customName)",,,FileUploadVulnerability.php,SecurityDemo\\FileUpload\\VulnerableFileUploader->processFileName,<empty>,,false,120,,processFileName,,,9,<unresolvedSignature>(2)
111669149708,METHOD,FileValidator.php:<global>,TYPE_DECL,VIRTUAL PUBLIC STATIC function <global>(),,,FileValidator.php,FileValidator.php:<global>,<empty>,,false,9,,<global>,,,1,<unresolvedSignature>(0)
111669149709,METHOD,SecurityDemo\\FileUpload\\FileValidator,TYPE_DECL,PUBLIC function __construct(this),,,FileValidator.php,SecurityDemo\\FileUpload\\FileValidator->__construct,<empty>,,false,26,,__construct,,,3,<unresolvedSignature>(0)
111669149710,METHOD,SecurityDemo\\FileUpload\\FileValidator,TYPE_DECL,"PUBLIC function validateImageFile(this,$filePath)",,,FileValidator.php,SecurityDemo\\FileUpload\\FileValidator->validateImageFile,<empty>,,false,52,,validateImageFile,,,4,<unresolvedSignature>(1)
111669149711,METHOD,SecurityDemo\\FileUpload\\FileValidator,TYPE_DECL,"PRIVATE function checkMagicBytes(this,$filePath)",,,FileValidator.php,SecurityDemo\\FileUpload\\FileValidator->checkMagicBytes,<empty>,,false,74,,checkMagicBytes,,,5,<unresolvedSignature>(1)
111669149712,METHOD,SecurityDemo\\FileUpload\\FileValidator,TYPE_DECL,"PRIVATE function checkFileContent(this,$filePath)",,,FileValidator.php,SecurityDemo\\FileUpload\\FileValidator->checkFileContent,<empty>,,false,104,,checkFileContent,,,6,<unresolvedSignature>(1)
111669149713,METHOD,SecurityDemo\\FileUpload\\FileValidator,TYPE_DECL,"PUBLIC function validateFileExtension(this,$fileName)",,,FileValidator.php,SecurityDemo\\FileUpload\\FileValidator->validateFileExtension,<empty>,,false,132,,validateFileExtension,,,7,<unresolvedSignature>(1)
111669149714,METHOD,SecurityDemo\\FileUpload\\FileValidator,TYPE_DECL,"PRIVATE function getFileExtension(this,$fileName)",,,FileValidator.php,SecurityDemo\\FileUpload\\FileValidator->getFileExtension,<empty>,,false,145,,getFileExtension,,,8,<unresolvedSignature>(1)
111669149715,METHOD,SecurityDemo\\FileUpload\\FileValidator,TYPE_DECL,"PUBLIC function sanitizeFileName(this,$originalName)",,,FileValidator.php,SecurityDemo\\FileUpload\\FileValidator->sanitizeFileName,<empty>,,false,159,,sanitizeFileName,,,9,<unresolvedSignature>(1)
111669149716,METHOD,<global>,NAMESPACE_BLOCK,<empty>,,,<empty>,require_once,<empty>,,true,,,require_once,,,0,
111669149717,METHOD,<global>,NAMESPACE_BLOCK,<empty>,,,<empty>,<operator>.assignment,<empty>,,true,,,<operator>.assignment,,,0,
111669149718,METHOD,<global>,NAMESPACE_BLOCK,<empty>,,,<empty>,<operator>.fieldAccess,<empty>,,true,,,<operator>.fieldAccess,,,0,
111669149719,METHOD,<global>,NAMESPACE_BLOCK,<empty>,,,<empty>,<operator>.alloc,<empty>,,true,,,<operator>.alloc,,,0,
111669149720,METHOD,<global>,NAMESPACE_BLOCK,<empty>,,,<empty>,<operator>.coalesce,<empty>,,true,,,<operator>.coalesce,,,0,
111669149721,METHOD,<global>,NAMESPACE_BLOCK,<empty>,,,<empty>,<operator>.indexAccess,<empty>,,true,,,<operator>.indexAccess,,,0,
111669149722,METHOD,<global>,NAMESPACE_BLOCK,<empty>,,,<empty>,<operator>.identical,<empty>,,true,,,<operator>.identical,,,0,
111669149723,METHOD,<global>,NAMESPACE_BLOCK,<empty>,,,<empty>,<unresolvedNamespace>\\$this->processUpload,<empty>,,true,,,processUpload,,,0,<unresolvedSignature>(0)
111669149724,METHOD,<global>,NAMESPACE_BLOCK,<empty>,,,<empty>,<unresolvedNamespace>\\$this->processBatchUpload,<empty>,,true,,,processBatchUpload,,,0,<unresolvedSignature>(0)
111669149725,METHOD,<global>,NAMESPACE_BLOCK,<empty>,,,<empty>,array,<empty>,,true,,,array,,,0,array()
111669149726,METHOD,<global>,NAMESPACE_BLOCK,<empty>,,,<empty>,<operator>.logicalNot,<empty>,,true,,,<operator>.logicalNot,,,0,
111669149727,METHOD,<global>,NAMESPACE_BLOCK,<empty>,,,<empty>,isset,<empty>,,true,,,isset,,,0,
111669149728,METHOD,<global>,NAMESPACE_BLOCK,<empty>,,,<empty>,empty,<empty>,,true,,,empty,,,0,
111669149729,METHOD,<global>,NAMESPACE_BLOCK,<empty>,,,<empty>,<operator>.concat,<empty>,,true,,,<operator>.concat,,,0,
111669149730,METHOD,<global>,NAMESPACE_BLOCK,<empty>,,,<empty>,<unresolvedNamespace>\\$this->uploader->handleUpload,<empty>,,true,,,handleUpload,,,0,<unresolvedSignature>(2)
111669149731,METHOD,<global>,NAMESPACE_BLOCK,<empty>,,,<empty>,<operator>.lessThan,<empty>,,true,,,<operator>.lessThan,,,0,
111669149732,METHOD,<global>,NAMESPACE_BLOCK,<empty>,,,<empty>,count,<empty>,,true,,,count,,,0,<unresolvedSignature>(1)
111669149733,METHOD,<global>,NAMESPACE_BLOCK,<empty>,,,<empty>,<operator>.postIncrement,<empty>,,true,,,<operator>.postIncrement,,,0,
111669149734,METHOD,<global>,NAMESPACE_BLOCK,<empty>,,,<empty>,array_push,<empty>,,true,,,array_push,,,0,<unresolvedSignature>(2)
111669149735,METHOD,<global>,NAMESPACE_BLOCK,<empty>,,,<empty>,header,<empty>,,true,,,header,,,0,<unresolvedSignature>(1)
111669149736,METHOD,<global>,NAMESPACE_BLOCK,<empty>,,,<empty>,echo,<empty>,,true,,,echo,,,0,
111669149737,METHOD,<global>,NAMESPACE_BLOCK,<empty>,,,<empty>,json_encode,<empty>,,true,,,json_encode,,,0,<unresolvedSignature>(2)
111669149738,METHOD,<global>,NAMESPACE_BLOCK,<empty>,,,<empty>,<operator>.logicalOr,<empty>,,true,,,<operator>.logicalOr,,,0,
111669149739,METHOD,<global>,NAMESPACE_BLOCK,<empty>,,,<empty>,<unresolvedNamespace>\\$controller->handleRequest,<empty>,,true,,,handleRequest,,,0,<unresolvedSignature>(0)
111669149740,METHOD,<global>,NAMESPACE_BLOCK,<empty>,,,<empty>,<unresolvedNamespace>\\$controller->sendResponse,<empty>,,true,,,sendResponse,,,0,<unresolvedSignature>(1)
111669149741,METHOD,<global>,NAMESPACE_BLOCK,<empty>,,,<empty>,<operator>.multiplication,<empty>,,true,,,<operator>.multiplication,,,0,
111669149742,METHOD,<global>,NAMESPACE_BLOCK,<empty>,,,<empty>,<unresolvedNamespace>\\$this->createUploadDirectory,<empty>,,true,,,createUploadDirectory,,,0,<unresolvedSignature>(0)
111669149743,METHOD,<global>,NAMESPACE_BLOCK,<empty>,,,<empty>,is_dir,<empty>,,true,,,is_dir,,,0,<unresolvedSignature>(1)
111669149744,METHOD,<global>,NAMESPACE_BLOCK,<empty>,,,<empty>,mkdir,<empty>,,true,,,mkdir,,,0,<unresolvedSignature>(3)
111669149745,METHOD,<global>,NAMESPACE_BLOCK,<empty>,,,<empty>,<unresolvedNamespace>\\$this->validateFileUpload,<empty>,,true,,,validateFileUpload,,,0,<unresolvedSignature>(1)
111669149746,METHOD,<global>,NAMESPACE_BLOCK,<empty>,,,<empty>,<unresolvedNamespace>\\$this->processFileName,<empty>,,true,,,processFileName,,,0,<unresolvedSignature>(2)
111669149747,METHOD,<global>,NAMESPACE_BLOCK,<empty>,,,<empty>,move_uploaded_file,<empty>,,true,,,move_uploaded_file,,,0,<unresolvedSignature>(2)
111669149748,METHOD,<global>,NAMESPACE_BLOCK,<empty>,,,<empty>,str_replace,<empty>,,true,,,str_replace,,,0,<unresolvedSignature>(3)
111669149749,METHOD,<global>,NAMESPACE_BLOCK,<empty>,,,<empty>,<unresolvedNamespace>\\$e->getMessage,<empty>,,true,,,getMessage,,,0,<unresolvedSignature>(0)
111669149750,METHOD,<global>,NAMESPACE_BLOCK,<empty>,,,<empty>,<operator>.notIdentical,<empty>,,true,,,<operator>.notIdentical,,,0,
111669149751,METHOD,<global>,NAMESPACE_BLOCK,<empty>,,,<empty>,<operator>.greaterThan,<empty>,,true,,,<operator>.greaterThan,,,0,
111669149752,METHOD,<global>,NAMESPACE_BLOCK,<empty>,,,<empty>,<operator>.conditional,<empty>,,true,,,<operator>.conditional,,,0,
111669149753,METHOD,<global>,NAMESPACE_BLOCK,<empty>,,,<empty>,strtolower,<empty>,,true,,,strtolower,,,0,<unresolvedSignature>(1)
111669149754,METHOD,<global>,NAMESPACE_BLOCK,<empty>,,,<empty>,pathinfo,<empty>,,true,,,pathinfo,,,0,<unresolvedSignature>(2)
111669149755,METHOD,<global>,NAMESPACE_BLOCK,<empty>,,,<empty>,Iterator.current,<empty>,,true,,,current,,,0,<unresolvedSignature>(0)
111669149756,METHOD,<global>,NAMESPACE_BLOCK,<empty>,,,<empty>,is_null,<empty>,,true,,,is_null,,,0,
111669149757,METHOD,<global>,NAMESPACE_BLOCK,<empty>,,,<empty>,Iterator.next,<empty>,,true,,,next,,,0,void()
111669149758,METHOD,<global>,NAMESPACE_BLOCK,<empty>,,,<empty>,<operator>.assignmentConcat,<empty>,,true,,,<operator>.assignmentConcat,,,0,
111669149759,METHOD,<global>,NAMESPACE_BLOCK,<empty>,,,<empty>,file_exists,<empty>,,true,,,file_exists,,,0,<unresolvedSignature>(1)
111669149760,METHOD,<global>,NAMESPACE_BLOCK,<empty>,,,<empty>,<unresolvedNamespace>\\$this->checkMagicBytes,<empty>,,true,,,checkMagicBytes,,,0,<unresolvedSignature>(1)
111669149761,METHOD,<global>,NAMESPACE_BLOCK,<empty>,,,<empty>,<unresolvedNamespace>\\$this->checkFileContent,<empty>,,true,,,checkFileContent,,,0,<unresolvedSignature>(1)
111669149762,METHOD,<global>,NAMESPACE_BLOCK,<empty>,,,<empty>,fopen,<empty>,,true,,,fopen,,,0,<unresolvedSignature>(2)
111669149763,METHOD,<global>,NAMESPACE_BLOCK,<empty>,,,<empty>,fread,<empty>,,true,,,fread,,,0,<unresolvedSignature>(2)
111669149764,METHOD,<global>,NAMESPACE_BLOCK,<empty>,,,<empty>,fclose,<empty>,,true,,,fclose,,,0,<unresolvedSignature>(1)
111669149765,METHOD,<global>,NAMESPACE_BLOCK,<empty>,,,<empty>,Iterator.key,<empty>,,true,,,key,,,0,<unresolvedSignature>(0)
111669149766,METHOD,<global>,NAMESPACE_BLOCK,<empty>,,,<empty>,ord,<empty>,,true,,,ord,,,0,<unresolvedSignature>(1)
111669149767,METHOD,<global>,NAMESPACE_BLOCK,<empty>,,,<empty>,file_get_contents,<empty>,,true,,,file_get_contents,,,0,<unresolvedSignature>(1)
111669149768,METHOD,<global>,NAMESPACE_BLOCK,<empty>,,,<empty>,strpos,<empty>,,true,,,strpos,,,0,<unresolvedSignature>(2)
111669149769,METHOD,<global>,NAMESPACE_BLOCK,<empty>,,,<empty>,<unresolvedNamespace>\\$this->getFileExtension,<empty>,,true,,,getFileExtension,,,0,<unresolvedSignature>(1)
111669149770,METHOD,<global>,NAMESPACE_BLOCK,<empty>,,,<empty>,in_array,<empty>,,true,,,in_array,,,0,<unresolvedSignature>(2)
111669149771,METHOD,<global>,NAMESPACE_BLOCK,<empty>,,,<empty>,explode,<empty>,,true,,,explode,,,0,<unresolvedSignature>(2)
111669149772,METHOD,<global>,NAMESPACE_BLOCK,<empty>,,,<empty>,end,<empty>,,true,,,end,,,0,<unresolvedSignature>(1)
111669149773,METHOD,<speculatedMethods>,NAMESPACE_BLOCK,<empty>,,,<empty>,SecurityDemo\\FileUpload\\VulnerableFileUploader->handleUpload-><returnValue>,<empty>,,true,,,<returnValue>,,,0,
