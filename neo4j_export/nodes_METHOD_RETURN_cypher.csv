LOAD CSV FROM 'file:/nodes_METHOD_RETURN_data.csv' AS line
CREATE (:METHOD_RETURN {
id: toInteger(line[0]),
CODE: line[2],
COLUMN_NUMBER: line[3],
DYNAMIC_TYPE_HINT_FULL_NAME: toStringList(split(line[4], ";")),
EVALUATION_STRATEGY: line[5],
LINE_NUMBER: toInteger(line[6]),
OFFSET: line[7],
OFFSET_END: line[8],
ORDER: toInteger(line[9]),
POSSIBLE_TYPES: split(line[10], ";"),
TYPE_FULL_NAME: line[11]
});
