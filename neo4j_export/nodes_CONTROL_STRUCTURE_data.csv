47244640256,CONTROL_STRUCTURE,-1,,"if ($action === ""upload"")",,IF,40,,,6,PhpIfStmt
47244640257,CONTROL_STRUCTURE,-1,,"if ($action === ""batch"")",,IF,42,,,1,PhpIfStmt
47244640258,CONTROL_STRUCTURE,-1,,"if (!isset($_FILES[""file""]))",,IF,54,,,8,PhpIfStmt
47244640259,CONTROL_STRUCTURE,-1,,if (!empty($subDir)),,IF,64,,,11,PhpIfStmt
47244640260,CONTROL_STRUCTURE,-1,,"if (!isset($_FILES[""files""]))",,IF,77,,,13,PhpIfStmt
47244640261,CONTROL_STRUCTURE,-1,,"for ($i = 0;$i < count($files[""name""]);$i++)",,FOR,85,,,16,PhpForStmt
47244640262,CONTROL_STRUCTURE,-1,,"if ($_SERVER[""REQUEST_METHOD""] === ""POST"" || !empty($_GET[""action""]))",,IF,115,,,5,PhpIfStmt
47244640263,CONTROL_STRUCTURE,-1,,if (!is_dir($this->uploadDir)),,IF,54,,,1,PhpIfStmt
47244640264,CONTROL_STRUCTURE,-1,,try { ... },,TRY,69,,,6,PhpTryStmt
47244640265,CONTROL_STRUCTURE,-1,,if (!$this->validateFileUpload($fileData)),,IF,71,,,1,PhpIfStmt
47244640266,CONTROL_STRUCTURE,-1,,"if (move_uploaded_file($fileData[""tmp_name""],$targetPath))",,IF,82,,,4,PhpIfStmt
47244640267,CONTROL_STRUCTURE,2,,catch (Exception $e),,CATCH,93,,,2,PhpCatchStmt
47244640268,CONTROL_STRUCTURE,-1,,"if ($fileData[""error""] !== UPLOAD_ERR_OK)",,IF,104,,,2,PhpIfStmt
47244640269,CONTROL_STRUCTURE,-1,,"if ($fileData[""size""] > $this->maxFileSize)",,IF,109,,,3,PhpIfStmt
47244640270,CONTROL_STRUCTURE,-1,,foreach ($this->blockedExtensions as $blocked),,FOR,131,,,10,PhpForeachStmt
47244640271,CONTROL_STRUCTURE,-1,,if ($extension === $blocked),,IF,132,,,1,PhpIfStmt
47244640272,CONTROL_STRUCTURE,-1,,break,,BREAK,135,,,2,PhpBreakStmt
47244640273,CONTROL_STRUCTURE,-1,,if (!file_exists($filePath)),,IF,54,,,1,PhpIfStmt
47244640274,CONTROL_STRUCTURE,-1,,if (!$this->checkMagicBytes($filePath)),,IF,59,,,2,PhpIfStmt
47244640275,CONTROL_STRUCTURE,-1,,if (!$this->checkFileContent($filePath)),,IF,64,,,3,PhpIfStmt
47244640276,CONTROL_STRUCTURE,-1,,if (!$handle),,IF,77,,,9,PhpIfStmt
47244640277,CONTROL_STRUCTURE,-1,,foreach ($this->imageMagicBytes as $type => $bytes),,FOR,85,,,12,PhpForeachStmt
47244640278,CONTROL_STRUCTURE,-1,,for ($i = 0;$i < count($bytes);$i++),,FOR,87,,,2,PhpForStmt
47244640279,CONTROL_STRUCTURE,-1,,if (!isset($header[$i]) || ord($header[$i]) !== $bytes[$i]),,IF,88,,,1,PhpIfStmt
47244640280,CONTROL_STRUCTURE,-1,,break,,BREAK,90,,,2,PhpBreakStmt
47244640281,CONTROL_STRUCTURE,-1,,if ($match),,IF,93,,,3,PhpIfStmt
47244640282,CONTROL_STRUCTURE,-1,,if ($content === false),,IF,107,,,5,PhpIfStmt
47244640283,CONTROL_STRUCTURE,-1,,foreach ($this->dangerousPatterns as $pattern),,FOR,112,,,6,PhpForeachStmt
47244640284,CONTROL_STRUCTURE,-1,,"if (strpos($content,$pattern) !== false)",,IF,114,,,1,PhpIfStmt
