LOAD CSV FROM 'file:/nodes_LOCAL_data.csv' AS line
CREATE (:LOCAL {
id: toInteger(line[0]),
CLOSURE_BINDING_ID: line[2],
CODE: line[3],
COLUMN_NUMBER: line[4],
DYNAMIC_TYPE_HINT_FULL_NAME: toStringList(split(line[5], ";")),
GENERIC_SIGNATURE: line[6],
LINE_NUMBER: toInteger(line[7]),
NAME: line[8],
OFFSET: line[9],
OFFSET_END: line[10],
ORDER: toInteger(line[11]),
POSSIBLE_TYPES: split(line[12], ";"),
TYPE_FULL_NAME: line[13]
});
