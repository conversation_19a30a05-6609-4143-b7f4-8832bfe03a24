LOAD CSV FROM 'file:/nodes_MEMBER_data.csv' AS line
CREATE (:MEMBER {
id: toInteger(line[0]),
AST_PARENT_FULL_NAME: line[2],
AST_PARENT_TYPE: line[3],
CODE: line[4],
COLUMN_NUMBER: line[5],
DY<PERSON>MIC_TYPE_HINT_FULL_NAME: toStringList(split(line[6], ";")),
GENERIC_SIGNATURE: line[7],
LINE_NUMBER: toInteger(line[8]),
NAME: line[9],
OFFSET: line[10],
OFFSET_END: line[11],
ORDER: toInteger(line[12]),
POSSIBLE_TYPES: split(line[13], ";"),
TYPE_FULL_NAME: line[14]
});
