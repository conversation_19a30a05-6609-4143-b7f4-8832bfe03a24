25769803778,30064771075,<PERSON><PERSON><PERSON><PERSON>_DEF,
25769803778,30064771074,REACH<PERSON>_DEF,
25769803778,30064771074,REACH<PERSON>_DEF,
25769803779,30064771080,<PERSON><PERSON><PERSON><PERSON>_DEF,
25769803779,30064771079,<PERSON><PERSON><PERSON><PERSON>_DEF,
25769803779,30064771079,R<PERSON><PERSON><PERSON>_DEF,
25769803784,146028888066,REACHING_DEF,
25769803784,146028888066,REACHING_DEF,<empty>
25769803787,146028888067,REACH<PERSON>_DEF,
25769803787,146028888067,REACHING_DEF,<empty>
25769803789,30064771121,REACHING_DEF,
25769803789,30064771120,REACH<PERSON>_DEF,
25769803789,30064771120,REA<PERSON><PERSON>_DEF,
25769803792,146028888069,<PERSON><PERSON><PERSON><PERSON>_DE<PERSON>,
25769803792,146028888069,<PERSON><PERSON><PERSON><PERSON>_DE<PERSON>,<empty>
25769803793,68719476780,REACHING_DEF,<empty>
25769803793,30064771139,REACHING_DEF,<empty>
25769803793,30064771139,REACHING_DEF,
25769803795,68719476789,REACHING_DEF,<empty>
25769803795,30064771149,REACHING_DEF,<empty>
25769803795,30064771149,REACHING_DEF,
25769803796,146028888070,REACHING_DEF,
25769803796,146028888070,REACHING_DEF,<empty>
25769803799,68719476825,REACHING_DEF,
25769803799,30064771196,REACHING_DEF,
25769803799,30064771196,REACHING_DEF,
25769803804,30064771207,REACHING_DEF,<empty>
25769803804,30064771206,REACHING_DEF,<empty>
25769803804,30064771206,REACHING_DEF,
25769803805,30064771219,REACHING_DEF,<empty>
25769803805,30064771218,REACHING_DEF,<empty>
25769803805,30064771218,REACHING_DEF,
25769803811,146028888071,REACHING_DEF,
25769803811,146028888071,REACHING_DEF,<empty>
25769803813,146028888072,REACHING_DEF,
25769803813,146028888072,REACHING_DEF,<empty>
25769803814,146028888073,REACHING_DEF,
25769803814,146028888073,REACHING_DEF,<empty>
25769803816,146028888074,REACHING_DEF,
25769803816,146028888074,REACHING_DEF,<empty>
25769803821,68719476896,REACHING_DEF,<empty>
25769803821,30064771288,REACHING_DEF,<empty>
25769803821,30064771288,REACHING_DEF,
25769803828,30064771311,REACHING_DEF,<empty>
25769803828,30064771310,REACHING_DEF,<empty>
25769803828,30064771310,REACHING_DEF,
25769803829,30064771315,REACHING_DEF,<empty>
25769803829,30064771314,REACHING_DEF,<empty>
25769803829,30064771314,REACHING_DEF,
25769803830,30064771325,REACHING_DEF,<empty>
25769803830,30064771324,REACHING_DEF,<empty>
25769803830,30064771324,REACHING_DEF,
25769803831,30064771337,REACHING_DEF,<empty>
25769803831,30064771336,REACHING_DEF,<empty>
25769803831,30064771336,REACHING_DEF,
25769803832,30064771349,REACHING_DEF,<empty>
25769803832,30064771348,REACHING_DEF,<empty>
25769803832,30064771348,REACHING_DEF,
25769803854,68719476996,REACHING_DEF,<empty>
25769803854,30064771419,REACHING_DEF,<empty>
25769803854,30064771419,REACHING_DEF,
25769803857,68719477019,REACHING_DEF,<empty>
25769803857,30064771439,REACHING_DEF,<empty>
25769803857,30064771439,REACHING_DEF,
30064771072,128849018885,REACHING_DEF,"require_once ""FileUploadVulnerability.php"""
30064771073,128849018885,REACHING_DEF,"require_once ""FileValidator.php"""
30064771074,128849018880,REACHING_DEF,$this->uploader = 
30064771075,120259084288,REACHING_DEF,$this->uploader
30064771075,68719476740,REACHING_DEF,$this->uploader
30064771075,30064771074,REACHING_DEF,$this->uploader
30064771075,128849018880,REACHING_DEF,$this->uploader
30064771076,128849018880,REACHING_DEF,$SecurityDemo\FileUpload\FileUploadController->__construct@tmp-0 = SecurityDemo\FileUpload\VulnerableFileUploader.<alloc>()
30064771077,68719476737,REACHING_DEF,SecurityDemo\FileUpload\VulnerableFileUploader.<alloc>()
30064771077,30064771076,REACHING_DEF,SecurityDemo\FileUpload\VulnerableFileUploader.<alloc>()
30064771077,128849018880,REACHING_DEF,SecurityDemo\FileUpload\VulnerableFileUploader.<alloc>()
30064771078,128849018880,REACHING_DEF,SecurityDemo\FileUpload\VulnerableFileUploader->__construct()
30064771079,128849018880,REACHING_DEF,$this->validator = 
30064771080,120259084288,REACHING_DEF,$this->validator
30064771080,30064771079,REACHING_DEF,$this->validator
30064771080,128849018880,REACHING_DEF,$this->validator
30064771081,128849018880,REACHING_DEF,$SecurityDemo\FileUpload\FileUploadController->__construct@tmp-1 = SecurityDemo\FileUpload\FileValidator.<alloc>()
30064771082,68719476741,REACHING_DEF,SecurityDemo\FileUpload\FileValidator.<alloc>()
30064771082,30064771081,REACHING_DEF,SecurityDemo\FileUpload\FileValidator.<alloc>()
30064771082,128849018880,REACHING_DEF,SecurityDemo\FileUpload\FileValidator.<alloc>()
30064771083,128849018880,REACHING_DEF,SecurityDemo\FileUpload\FileValidator->__construct()
30064771084,128849018881,REACHING_DEF,"$action = $_POST[""action""] ?? $_GET[""action""] ?? ""upload"""
30064771085,68719476744,REACHING_DEF,"$_POST[""action""] ?? $_GET[""action""] ?? ""upload"""
30064771085,30064771084,REACHING_DEF,"$_POST[""action""] ?? $_GET[""action""] ?? ""upload"""
30064771085,128849018881,REACHING_DEF,"$_POST[""action""] ?? $_GET[""action""] ?? ""upload"""
30064771086,30064771087,REACHING_DEF,"$_POST[""action""]"
30064771086,30064771085,REACHING_DEF,"$_POST[""action""]"
30064771086,128849018881,REACHING_DEF,"$_POST[""action""]"
30064771087,30064771086,REACHING_DEF,"$_GET[""action""] ?? ""upload"""
30064771087,30064771085,REACHING_DEF,"$_GET[""action""] ?? ""upload"""
30064771087,128849018881,REACHING_DEF,"$_GET[""action""] ?? ""upload"""
30064771088,30064771087,REACHING_DEF,"$_GET[""action""]"
30064771088,128849018881,REACHING_DEF,"$_GET[""action""]"
30064771089,128849018881,REACHING_DEF,"$action === ""upload"""
30064771090,146028888064,REACHING_DEF,$this->processUpload()
30064771090,128849018881,REACHING_DEF,$this->processUpload()
30064771092,146028888065,REACHING_DEF,$this->processBatchUpload()
30064771094,68719476751,REACHING_DEF,array()
30064771094,30064771093,REACHING_DEF,array()
30064771096,30064771095,REACHING_DEF,"$SecurityDemo\FileUpload\FileUploadController->handleRequest@tmp-0[""success""]"
30064771096,68719476753,REACHING_DEF,"$SecurityDemo\FileUpload\FileUploadController->handleRequest@tmp-0[""success""]"
30064771096,25769803784,REACHING_DEF,"$SecurityDemo\FileUpload\FileUploadController->handleRequest@tmp-0[""success""]"
30064771098,30064771097,REACHING_DEF,"$SecurityDemo\FileUpload\FileUploadController->handleRequest@tmp-0[""message""]"
30064771098,25769803784,REACHING_DEF,"$SecurityDemo\FileUpload\FileUploadController->handleRequest@tmp-0[""message""]"
30064771099,128849018882,REACHING_DEF,"!isset($_FILES[""file""])"
30064771100,30064771099,REACHING_DEF,"isset($_FILES[""file""])"
30064771100,128849018882,REACHING_DEF,"isset($_FILES[""file""])"
30064771101,30064771100,REACHING_DEF,"$_FILES[""file""]"
30064771101,68719476773,REACHING_DEF,"$_FILES[""file""]"
30064771101,30064771129,REACHING_DEF,"$_FILES[""file""]"
30064771101,128849018882,REACHING_DEF,"$_FILES[""file""]"
30064771102,128849018882,REACHING_DEF,$SecurityDemo\FileUpload\FileUploadController->processUpload@tmp-0 = array()
30064771103,68719476756,REACHING_DEF,array()
30064771103,30064771102,REACHING_DEF,array()
30064771103,128849018882,REACHING_DEF,array()
30064771104,128849018882,REACHING_DEF,"$SecurityDemo\FileUpload\FileUploadController->processUpload@tmp-0[""success""] = false"
30064771105,25769803787,REACHING_DEF,"$SecurityDemo\FileUpload\FileUploadController->processUpload@tmp-0[""success""]"
30064771105,30064771104,REACHING_DEF,"$SecurityDemo\FileUpload\FileUploadController->processUpload@tmp-0[""success""]"
30064771105,68719476758,REACHING_DEF,"$SecurityDemo\FileUpload\FileUploadController->processUpload@tmp-0[""success""]"
30064771105,128849018882,REACHING_DEF,"$SecurityDemo\FileUpload\FileUploadController->processUpload@tmp-0[""success""]"
30064771106,128849018882,REACHING_DEF,"$SecurityDemo\FileUpload\FileUploadController->processUpload@tmp-0[""message""] = ""未找到上传文件"""
30064771107,25769803787,REACHING_DEF,"$SecurityDemo\FileUpload\FileUploadController->processUpload@tmp-0[""message""]"
30064771107,30064771106,REACHING_DEF,"$SecurityDemo\FileUpload\FileUploadController->processUpload@tmp-0[""message""]"
30064771107,128849018882,REACHING_DEF,"$SecurityDemo\FileUpload\FileUploadController->processUpload@tmp-0[""message""]"
30064771109,68719476760,REACHING_DEF,"$_POST[""filename""] ?? $_GET[""filename""] ?? """""
30064771109,30064771108,REACHING_DEF,"$_POST[""filename""] ?? $_GET[""filename""] ?? """""
30064771110,30064771111,REACHING_DEF,"$_POST[""filename""]"
30064771110,30064771109,REACHING_DEF,"$_POST[""filename""]"
30064771110,68719476764,REACHING_DEF,"$_POST[""filename""]"
30064771111,30064771110,REACHING_DEF,"$_GET[""filename""] ?? """""
30064771111,30064771109,REACHING_DEF,"$_GET[""filename""] ?? """""
30064771112,30064771111,REACHING_DEF,"$_GET[""filename""]"
30064771112,68719476765,REACHING_DEF,"$_GET[""filename""]"
30064771114,68719476763,REACHING_DEF,"$_POST[""subdir""] ?? $_GET[""subdir""] ?? """""
30064771114,30064771113,REACHING_DEF,"$_POST[""subdir""] ?? $_GET[""subdir""] ?? """""
30064771115,30064771116,REACHING_DEF,"$_POST[""subdir""]"
30064771115,30064771114,REACHING_DEF,"$_POST[""subdir""]"
30064771116,30064771115,REACHING_DEF,"$_GET[""subdir""] ?? """""
30064771116,30064771114,REACHING_DEF,"$_GET[""subdir""] ?? """""
30064771117,30064771116,REACHING_DEF,"$_GET[""subdir""]"
30064771119,30064771118,REACHING_DEF,empty($subDir)
30064771121,30064771120,REACHING_DEF,$this->uploader
30064771121,68719476772,REACHING_DEF,$this->uploader
30064771121,30064771128,REACHING_DEF,$this->uploader
30064771123,68719476768,REACHING_DEF,SecurityDemo\FileUpload\VulnerableFileUploader.<alloc>()
30064771123,30064771122,REACHING_DEF,SecurityDemo\FileUpload\VulnerableFileUploader.<alloc>()
30064771125,30064771124,REACHING_DEF,"""./uploads/"" . $subDir . ""/"""
30064771126,30064771125,REACHING_DEF,"""./uploads/"" . $subDir"
30064771127,146028888068,REACHING_DEF,"$this->uploader->handleUpload($_FILES[""file""],$customName)"
30064771128,30064771129,REACHING_DEF,$this->uploader
30064771128,68719476774,REACHING_DEF,$this->uploader
30064771128,30064771127,REACHING_DEF,$this->uploader
30064771129,30064771128,REACHING_DEF,"$_FILES[""file""]"
30064771129,68719476774,REACHING_DEF,"$_FILES[""file""]"
30064771129,30064771127,REACHING_DEF,"$_FILES[""file""]"
30064771130,128849018883,REACHING_DEF,"!isset($_FILES[""files""])"
30064771131,30064771130,REACHING_DEF,"isset($_FILES[""files""])"
30064771131,128849018883,REACHING_DEF,"isset($_FILES[""files""])"
30064771132,68719476784,REACHING_DEF,"$_FILES[""files""]"
30064771132,30064771131,REACHING_DEF,"$_FILES[""files""]"
30064771132,30064771143,REACHING_DEF,"$_FILES[""files""]"
30064771132,128849018883,REACHING_DEF,"$_FILES[""files""]"
30064771133,128849018883,REACHING_DEF,$SecurityDemo\FileUpload\FileUploadController->processBatchUpload@tmp-0 = array()
30064771134,68719476776,REACHING_DEF,array()
30064771134,30064771133,REACHING_DEF,array()
30064771134,128849018883,REACHING_DEF,array()
30064771135,128849018883,REACHING_DEF,"$SecurityDemo\FileUpload\FileUploadController->processBatchUpload@tmp-0[""success""] = false"
30064771136,30064771135,REACHING_DEF,"$SecurityDemo\FileUpload\FileUploadController->processBatchUpload@tmp-0[""success""]"
30064771136,25769803792,REACHING_DEF,"$SecurityDemo\FileUpload\FileUploadController->processBatchUpload@tmp-0[""success""]"
30064771136,68719476778,REACHING_DEF,"$SecurityDemo\FileUpload\FileUploadController->processBatchUpload@tmp-0[""success""]"
30064771136,128849018883,REACHING_DEF,"$SecurityDemo\FileUpload\FileUploadController->processBatchUpload@tmp-0[""success""]"
30064771137,128849018883,REACHING_DEF,"$SecurityDemo\FileUpload\FileUploadController->processBatchUpload@tmp-0[""message""] = ""未找到批量上传文件"""
30064771138,30064771137,REACHING_DEF,"$SecurityDemo\FileUpload\FileUploadController->processBatchUpload@tmp-0[""message""]"
30064771138,25769803792,REACHING_DEF,"$SecurityDemo\FileUpload\FileUploadController->processBatchUpload@tmp-0[""message""]"
30064771138,128849018883,REACHING_DEF,"$SecurityDemo\FileUpload\FileUploadController->processBatchUpload@tmp-0[""message""]"
30064771141,68719476781,REACHING_DEF,array()
30064771141,30064771140,REACHING_DEF,array()
30064771143,68719476783,REACHING_DEF,"$_FILES[""files""]"
30064771143,30064771142,REACHING_DEF,"$_FILES[""files""]"
30064771146,68719476786,REACHING_DEF,"count($files[""name""])"
30064771146,30064771145,REACHING_DEF,"count($files[""name""])"
30064771147,68719476787,REACHING_DEF,"$files[""name""]"
30064771147,68719476792,REACHING_DEF,"$files[""name""]"
30064771147,30064771155,REACHING_DEF,"$files[""name""]"
30064771147,68719476801,REACHING_DEF,"$files[""name""]"
30064771147,68719476804,REACHING_DEF,"$files[""name""]"
30064771147,68719476798,REACHING_DEF,"$files[""name""]"
30064771147,30064771146,REACHING_DEF,"$files[""name""]"
30064771147,68719476795,REACHING_DEF,"$files[""name""]"
30064771151,68719476790,REACHING_DEF,array()
30064771151,30064771150,REACHING_DEF,array()
30064771153,68719476803,REACHING_DEF,"$SecurityDemo\FileUpload\FileUploadController->processBatchUpload@tmp-2[""name""]"
30064771153,30064771152,REACHING_DEF,"$SecurityDemo\FileUpload\FileUploadController->processBatchUpload@tmp-2[""name""]"
30064771153,68719476800,REACHING_DEF,"$SecurityDemo\FileUpload\FileUploadController->processBatchUpload@tmp-2[""name""]"
30064771153,25769803795,REACHING_DEF,"$SecurityDemo\FileUpload\FileUploadController->processBatchUpload@tmp-2[""name""]"
30064771153,68719476797,REACHING_DEF,"$SecurityDemo\FileUpload\FileUploadController->processBatchUpload@tmp-2[""name""]"
30064771153,68719476794,REACHING_DEF,"$SecurityDemo\FileUpload\FileUploadController->processBatchUpload@tmp-2[""name""]"
30064771153,68719476791,REACHING_DEF,"$SecurityDemo\FileUpload\FileUploadController->processBatchUpload@tmp-2[""name""]"
30064771154,30064771153,REACHING_DEF,"$files[""name""][$i]"
30064771154,30064771152,REACHING_DEF,"$files[""name""][$i]"
30064771154,30064771155,REACHING_DEF,"$files[""name""][$i]"
30064771154,30064771147,REACHING_DEF,"$files[""name""][$i]"
30064771157,30064771156,REACHING_DEF,"$SecurityDemo\FileUpload\FileUploadController->processBatchUpload@tmp-2[""type""]"
30064771157,68719476803,REACHING_DEF,"$SecurityDemo\FileUpload\FileUploadController->processBatchUpload@tmp-2[""type""]"
30064771157,68719476800,REACHING_DEF,"$SecurityDemo\FileUpload\FileUploadController->processBatchUpload@tmp-2[""type""]"
30064771157,25769803795,REACHING_DEF,"$SecurityDemo\FileUpload\FileUploadController->processBatchUpload@tmp-2[""type""]"
30064771157,68719476797,REACHING_DEF,"$SecurityDemo\FileUpload\FileUploadController->processBatchUpload@tmp-2[""type""]"
30064771157,68719476794,REACHING_DEF,"$SecurityDemo\FileUpload\FileUploadController->processBatchUpload@tmp-2[""type""]"
30064771157,68719476791,REACHING_DEF,"$SecurityDemo\FileUpload\FileUploadController->processBatchUpload@tmp-2[""type""]"
30064771158,30064771157,REACHING_DEF,"$files[""type""][$i]"
30064771158,30064771156,REACHING_DEF,"$files[""type""][$i]"
30064771158,30064771159,REACHING_DEF,"$files[""type""][$i]"
30064771161,68719476803,REACHING_DEF,"$SecurityDemo\FileUpload\FileUploadController->processBatchUpload@tmp-2[""tmp_name""]"
30064771161,68719476800,REACHING_DEF,"$SecurityDemo\FileUpload\FileUploadController->processBatchUpload@tmp-2[""tmp_name""]"
30064771161,30064771160,REACHING_DEF,"$SecurityDemo\FileUpload\FileUploadController->processBatchUpload@tmp-2[""tmp_name""]"
30064771161,25769803795,REACHING_DEF,"$SecurityDemo\FileUpload\FileUploadController->processBatchUpload@tmp-2[""tmp_name""]"
30064771161,68719476797,REACHING_DEF,"$SecurityDemo\FileUpload\FileUploadController->processBatchUpload@tmp-2[""tmp_name""]"
30064771161,68719476794,REACHING_DEF,"$SecurityDemo\FileUpload\FileUploadController->processBatchUpload@tmp-2[""tmp_name""]"
30064771161,68719476791,REACHING_DEF,"$SecurityDemo\FileUpload\FileUploadController->processBatchUpload@tmp-2[""tmp_name""]"
30064771162,30064771161,REACHING_DEF,"$files[""tmp_name""][$i]"
30064771162,30064771160,REACHING_DEF,"$files[""tmp_name""][$i]"
30064771162,30064771163,REACHING_DEF,"$files[""tmp_name""][$i]"
30064771165,30064771164,REACHING_DEF,"$SecurityDemo\FileUpload\FileUploadController->processBatchUpload@tmp-2[""error""]"
30064771165,68719476803,REACHING_DEF,"$SecurityDemo\FileUpload\FileUploadController->processBatchUpload@tmp-2[""error""]"
30064771165,68719476800,REACHING_DEF,"$SecurityDemo\FileUpload\FileUploadController->processBatchUpload@tmp-2[""error""]"
30064771165,25769803795,REACHING_DEF,"$SecurityDemo\FileUpload\FileUploadController->processBatchUpload@tmp-2[""error""]"
30064771165,68719476797,REACHING_DEF,"$SecurityDemo\FileUpload\FileUploadController->processBatchUpload@tmp-2[""error""]"
30064771165,68719476794,REACHING_DEF,"$SecurityDemo\FileUpload\FileUploadController->processBatchUpload@tmp-2[""error""]"
30064771165,68719476791,REACHING_DEF,"$SecurityDemo\FileUpload\FileUploadController->processBatchUpload@tmp-2[""error""]"
30064771166,30064771165,REACHING_DEF,"$files[""error""][$i]"
30064771166,30064771164,REACHING_DEF,"$files[""error""][$i]"
30064771166,30064771167,REACHING_DEF,"$files[""error""][$i]"
30064771169,68719476803,REACHING_DEF,"$SecurityDemo\FileUpload\FileUploadController->processBatchUpload@tmp-2[""size""]"
30064771169,68719476800,REACHING_DEF,"$SecurityDemo\FileUpload\FileUploadController->processBatchUpload@tmp-2[""size""]"
30064771169,30064771168,REACHING_DEF,"$SecurityDemo\FileUpload\FileUploadController->processBatchUpload@tmp-2[""size""]"
30064771169,25769803795,REACHING_DEF,"$SecurityDemo\FileUpload\FileUploadController->processBatchUpload@tmp-2[""size""]"
30064771169,68719476797,REACHING_DEF,"$SecurityDemo\FileUpload\FileUploadController->processBatchUpload@tmp-2[""size""]"
30064771169,68719476794,REACHING_DEF,"$SecurityDemo\FileUpload\FileUploadController->processBatchUpload@tmp-2[""size""]"
30064771169,68719476791,REACHING_DEF,"$SecurityDemo\FileUpload\FileUploadController->processBatchUpload@tmp-2[""size""]"
30064771170,30064771169,REACHING_DEF,"$files[""size""][$i]"
30064771170,30064771168,REACHING_DEF,"$files[""size""][$i]"
30064771170,30064771171,REACHING_DEF,"$files[""size""][$i]"
30064771173,68719476807,REACHING_DEF,"$_POST[""filenames""][$i] ?? """""
30064771173,30064771172,REACHING_DEF,"$_POST[""filenames""][$i] ?? """""
30064771174,30064771175,REACHING_DEF,"$_POST[""filenames""][$i]"
30064771174,30064771173,REACHING_DEF,"$_POST[""filenames""][$i]"
30064771177,68719476810,REACHING_DEF,"$this->uploader->handleUpload($fileData,$customName)"
30064771177,30064771176,REACHING_DEF,"$this->uploader->handleUpload($fileData,$customName)"
30064771178,68719476811,REACHING_DEF,$this->uploader
30064771178,68719476812,REACHING_DEF,$this->uploader
30064771178,68719476813,REACHING_DEF,$this->uploader
30064771178,30064771177,REACHING_DEF,$this->uploader
30064771181,68719476816,REACHING_DEF,array()
30064771181,30064771180,REACHING_DEF,array()
30064771183,30064771182,REACHING_DEF,"$SecurityDemo\FileUpload\FileUploadController->processBatchUpload@tmp-3[""success""]"
30064771183,25769803796,REACHING_DEF,"$SecurityDemo\FileUpload\FileUploadController->processBatchUpload@tmp-3[""success""]"
30064771183,68719476818,REACHING_DEF,"$SecurityDemo\FileUpload\FileUploadController->processBatchUpload@tmp-3[""success""]"
30064771185,30064771184,REACHING_DEF,"$SecurityDemo\FileUpload\FileUploadController->processBatchUpload@tmp-3[""results""]"
30064771185,25769803796,REACHING_DEF,"$SecurityDemo\FileUpload\FileUploadController->processBatchUpload@tmp-3[""results""]"
30064771186,128849018884,REACHING_DEF,"header(""Content-Type: application/json"")"
30064771187,128849018884,REACHING_DEF,"echo json_encode($data,JSON_UNESCAPED_UNICODE)"
30064771188,30064771187,REACHING_DEF,"json_encode($data,JSON_UNESCAPED_UNICODE)"
30064771188,128849018884,REACHING_DEF,"json_encode($data,JSON_UNESCAPED_UNICODE)"
30064771189,68719476821,REACHING_DEF,JSON_UNESCAPED_UNICODE
30064771189,30064771188,REACHING_DEF,JSON_UNESCAPED_UNICODE
30064771189,128849018884,REACHING_DEF,JSON_UNESCAPED_UNICODE
30064771190,128849018885,REACHING_DEF,"$_SERVER[""REQUEST_METHOD""] === ""POST"" || !empty($_GET[""action""])"
30064771191,30064771193,REACHING_DEF,"$_SERVER[""REQUEST_METHOD""] === ""POST"""
30064771191,30064771190,REACHING_DEF,"$_SERVER[""REQUEST_METHOD""] === ""POST"""
30064771191,128849018885,REACHING_DEF,"$_SERVER[""REQUEST_METHOD""] === ""POST"""
30064771192,30064771191,REACHING_DEF,"$_SERVER[""REQUEST_METHOD""]"
30064771192,128849018885,REACHING_DEF,"$_SERVER[""REQUEST_METHOD""]"
30064771193,30064771191,REACHING_DEF,"!empty($_GET[""action""])"
30064771193,30064771190,REACHING_DEF,"!empty($_GET[""action""])"
30064771193,128849018885,REACHING_DEF,"!empty($_GET[""action""])"
30064771194,30064771193,REACHING_DEF,"empty($_GET[""action""])"
30064771194,128849018885,REACHING_DEF,"empty($_GET[""action""])"
30064771195,30064771194,REACHING_DEF,"$_GET[""action""]"
30064771195,128849018885,REACHING_DEF,"$_GET[""action""]"
30064771196,128849018885,REACHING_DEF,$controller = 
30064771197,128849018885,REACHING_DEF,$FileUploadHandler.php:SecurityDemo\FileUpload@tmp-0 = SecurityDemo\FileUpload\FileUploadController.<alloc>()
30064771198,68719476826,REACHING_DEF,SecurityDemo\FileUpload\FileUploadController.<alloc>()
30064771198,30064771197,REACHING_DEF,SecurityDemo\FileUpload\FileUploadController.<alloc>()
30064771198,128849018885,REACHING_DEF,SecurityDemo\FileUpload\FileUploadController.<alloc>()
30064771199,128849018885,REACHING_DEF,SecurityDemo\FileUpload\FileUploadController->__construct()
30064771200,128849018885,REACHING_DEF,$result = $controller->handleRequest()
30064771201,68719476829,REACHING_DEF,$controller->handleRequest()
30064771201,30064771200,REACHING_DEF,$controller->handleRequest()
30064771201,128849018885,REACHING_DEF,$controller->handleRequest()
30064771202,128849018885,REACHING_DEF,$controller->sendResponse($result)
30064771204,128849018886,REACHING_DEF,$this->uploadDir = $uploadDir
30064771205,68719476848,REACHING_DEF,$this->uploadDir
30064771205,68719476847,REACHING_DEF,$this->uploadDir
30064771205,30064771204,REACHING_DEF,$this->uploadDir
30064771205,68719476842,REACHING_DEF,$this->uploadDir
30064771205,68719476835,REACHING_DEF,$this->uploadDir
30064771206,128849018886,REACHING_DEF,$this->allowedMimeTypes = 
30064771207,30064771206,REACHING_DEF,$this->allowedMimeTypes
30064771207,68719476848,REACHING_DEF,$this->allowedMimeTypes
30064771207,68719476847,REACHING_DEF,$this->allowedMimeTypes
30064771207,68719476842,REACHING_DEF,$this->allowedMimeTypes
30064771208,128849018886,REACHING_DEF,$SecurityDemo\FileUpload\VulnerableFileUploader->__construct@tmp-0 = array()
30064771209,68719476836,REACHING_DEF,array()
30064771209,30064771208,REACHING_DEF,array()
30064771210,128849018886,REACHING_DEF,"$SecurityDemo\FileUpload\VulnerableFileUploader->__construct@tmp-0[0] = ""image/jpeg"""
30064771211,25769803804,REACHING_DEF,$SecurityDemo\FileUpload\VulnerableFileUploader->__construct@tmp-0[0]
30064771211,30064771210,REACHING_DEF,$SecurityDemo\FileUpload\VulnerableFileUploader->__construct@tmp-0[0]
30064771211,68719476840,REACHING_DEF,$SecurityDemo\FileUpload\VulnerableFileUploader->__construct@tmp-0[0]
30064771211,68719476838,REACHING_DEF,$SecurityDemo\FileUpload\VulnerableFileUploader->__construct@tmp-0[0]
30064771211,68719476839,REACHING_DEF,$SecurityDemo\FileUpload\VulnerableFileUploader->__construct@tmp-0[0]
30064771211,128849018886,REACHING_DEF,$SecurityDemo\FileUpload\VulnerableFileUploader->__construct@tmp-0[0]
30064771212,128849018886,REACHING_DEF,"$SecurityDemo\FileUpload\VulnerableFileUploader->__construct@tmp-0[1] = ""image/png"""
30064771213,25769803804,REACHING_DEF,$SecurityDemo\FileUpload\VulnerableFileUploader->__construct@tmp-0[1]
30064771213,68719476840,REACHING_DEF,$SecurityDemo\FileUpload\VulnerableFileUploader->__construct@tmp-0[1]
30064771213,30064771212,REACHING_DEF,$SecurityDemo\FileUpload\VulnerableFileUploader->__construct@tmp-0[1]
30064771213,68719476839,REACHING_DEF,$SecurityDemo\FileUpload\VulnerableFileUploader->__construct@tmp-0[1]
30064771213,128849018886,REACHING_DEF,$SecurityDemo\FileUpload\VulnerableFileUploader->__construct@tmp-0[1]
30064771214,128849018886,REACHING_DEF,"$SecurityDemo\FileUpload\VulnerableFileUploader->__construct@tmp-0[2] = ""image/gif"""
30064771215,25769803804,REACHING_DEF,$SecurityDemo\FileUpload\VulnerableFileUploader->__construct@tmp-0[2]
30064771215,68719476840,REACHING_DEF,$SecurityDemo\FileUpload\VulnerableFileUploader->__construct@tmp-0[2]
30064771215,30064771214,REACHING_DEF,$SecurityDemo\FileUpload\VulnerableFileUploader->__construct@tmp-0[2]
30064771215,128849018886,REACHING_DEF,$SecurityDemo\FileUpload\VulnerableFileUploader->__construct@tmp-0[2]
30064771216,128849018886,REACHING_DEF,"$SecurityDemo\FileUpload\VulnerableFileUploader->__construct@tmp-0[3] = ""text/plain"""
30064771217,25769803804,REACHING_DEF,$SecurityDemo\FileUpload\VulnerableFileUploader->__construct@tmp-0[3]
30064771217,30064771216,REACHING_DEF,$SecurityDemo\FileUpload\VulnerableFileUploader->__construct@tmp-0[3]
30064771217,128849018886,REACHING_DEF,$SecurityDemo\FileUpload\VulnerableFileUploader->__construct@tmp-0[3]
30064771218,128849018886,REACHING_DEF,$this->blockedExtensions = 
30064771219,68719476848,REACHING_DEF,$this->blockedExtensions
30064771219,68719476847,REACHING_DEF,$this->blockedExtensions
30064771219,30064771218,REACHING_DEF,$this->blockedExtensions
30064771220,128849018886,REACHING_DEF,$SecurityDemo\FileUpload\VulnerableFileUploader->__construct@tmp-1 = array()
30064771221,68719476843,REACHING_DEF,array()
30064771221,30064771220,REACHING_DEF,array()
30064771221,128849018886,REACHING_DEF,array()
30064771222,128849018886,REACHING_DEF,"$SecurityDemo\FileUpload\VulnerableFileUploader->__construct@tmp-1[0] = ""exe"""
30064771223,30064771222,REACHING_DEF,$SecurityDemo\FileUpload\VulnerableFileUploader->__construct@tmp-1[0]
30064771223,25769803805,REACHING_DEF,$SecurityDemo\FileUpload\VulnerableFileUploader->__construct@tmp-1[0]
30064771223,68719476845,REACHING_DEF,$SecurityDemo\FileUpload\VulnerableFileUploader->__construct@tmp-1[0]
30064771223,128849018886,REACHING_DEF,$SecurityDemo\FileUpload\VulnerableFileUploader->__construct@tmp-1[0]
30064771224,128849018886,REACHING_DEF,"$SecurityDemo\FileUpload\VulnerableFileUploader->__construct@tmp-1[1] = ""bat"""
30064771225,25769803805,REACHING_DEF,$SecurityDemo\FileUpload\VulnerableFileUploader->__construct@tmp-1[1]
30064771225,30064771224,REACHING_DEF,$SecurityDemo\FileUpload\VulnerableFileUploader->__construct@tmp-1[1]
30064771225,128849018886,REACHING_DEF,$SecurityDemo\FileUpload\VulnerableFileUploader->__construct@tmp-1[1]
30064771226,128849018886,REACHING_DEF,$this->maxFileSize = 5 * 1024 * 1024
30064771227,68719476848,REACHING_DEF,$this->maxFileSize
30064771227,30064771226,REACHING_DEF,$this->maxFileSize
30064771228,30064771227,REACHING_DEF,5 * 1024 * 1024
30064771228,30064771226,REACHING_DEF,5 * 1024 * 1024
30064771228,128849018886,REACHING_DEF,5 * 1024 * 1024
30064771229,30064771228,REACHING_DEF,5 * 1024
30064771229,128849018886,REACHING_DEF,5 * 1024
30064771230,128849018886,REACHING_DEF,$this->createUploadDirectory()
30064771231,128849018887,REACHING_DEF,!is_dir($this->uploadDir)
30064771232,30064771231,REACHING_DEF,is_dir($this->uploadDir)
30064771232,128849018887,REACHING_DEF,is_dir($this->uploadDir)
30064771233,30064771235,REACHING_DEF,$this->uploadDir
30064771233,68719476850,REACHING_DEF,$this->uploadDir
30064771233,30064771232,REACHING_DEF,$this->uploadDir
30064771234,128849018887,REACHING_DEF,"mkdir($this->uploadDir,493,true)"
30064771235,30064771234,REACHING_DEF,$this->uploadDir
30064771235,120259084296,REACHING_DEF,$this->uploadDir
30064771235,128849018887,REACHING_DEF,$this->uploadDir
30064771236,128849018888,REACHING_DEF,!$this->validateFileUpload($fileData)
30064771237,30064771236,REACHING_DEF,$this->validateFileUpload($fileData)
30064771237,128849018888,REACHING_DEF,$this->validateFileUpload($fileData)
30064771238,128849018888,REACHING_DEF,$SecurityDemo\FileUpload\VulnerableFileUploader->handleUpload@tmp-0 = array()
30064771239,68719476853,REACHING_DEF,array()
30064771239,30064771238,REACHING_DEF,array()
30064771239,128849018888,REACHING_DEF,array()
30064771240,128849018888,REACHING_DEF,"$SecurityDemo\FileUpload\VulnerableFileUploader->handleUpload@tmp-0[""success""] = false"
30064771241,25769803811,REACHING_DEF,"$SecurityDemo\FileUpload\VulnerableFileUploader->handleUpload@tmp-0[""success""]"
30064771241,30064771240,REACHING_DEF,"$SecurityDemo\FileUpload\VulnerableFileUploader->handleUpload@tmp-0[""success""]"
30064771241,68719476855,REACHING_DEF,"$SecurityDemo\FileUpload\VulnerableFileUploader->handleUpload@tmp-0[""success""]"
30064771241,128849018888,REACHING_DEF,"$SecurityDemo\FileUpload\VulnerableFileUploader->handleUpload@tmp-0[""success""]"
30064771242,128849018888,REACHING_DEF,"$SecurityDemo\FileUpload\VulnerableFileUploader->handleUpload@tmp-0[""message""] = ""文件验证失败"""
30064771243,25769803811,REACHING_DEF,"$SecurityDemo\FileUpload\VulnerableFileUploader->handleUpload@tmp-0[""message""]"
30064771243,30064771242,REACHING_DEF,"$SecurityDemo\FileUpload\VulnerableFileUploader->handleUpload@tmp-0[""message""]"
30064771243,128849018888,REACHING_DEF,"$SecurityDemo\FileUpload\VulnerableFileUploader->handleUpload@tmp-0[""message""]"
30064771245,68719476857,REACHING_DEF,"$this->processFileName($fileData[""name""],$customName)"
30064771245,30064771244,REACHING_DEF,"$this->processFileName($fileData[""name""],$customName)"
30064771246,68719476858,REACHING_DEF,"$fileData[""name""]"
30064771246,68719476860,REACHING_DEF,"$fileData[""name""]"
30064771246,30064771245,REACHING_DEF,"$fileData[""name""]"
30064771246,68719476864,REACHING_DEF,"$fileData[""name""]"
30064771248,68719476861,REACHING_DEF,$this->uploadDir . $fileName
30064771248,30064771247,REACHING_DEF,$this->uploadDir . $fileName
30064771249,68719476863,REACHING_DEF,$this->uploadDir
30064771249,30064771248,REACHING_DEF,$this->uploadDir
30064771251,68719476865,REACHING_DEF,"$fileData[""tmp_name""]"
30064771251,30064771250,REACHING_DEF,"$fileData[""tmp_name""]"
30064771253,68719476866,REACHING_DEF,array()
30064771253,30064771252,REACHING_DEF,array()
30064771255,68719476868,REACHING_DEF,"$SecurityDemo\FileUpload\VulnerableFileUploader->handleUpload@tmp-1[""success""]"
30064771255,30064771254,REACHING_DEF,"$SecurityDemo\FileUpload\VulnerableFileUploader->handleUpload@tmp-1[""success""]"
30064771255,68719476871,REACHING_DEF,"$SecurityDemo\FileUpload\VulnerableFileUploader->handleUpload@tmp-1[""success""]"
30064771255,25769803813,REACHING_DEF,"$SecurityDemo\FileUpload\VulnerableFileUploader->handleUpload@tmp-1[""success""]"
30064771255,68719476869,REACHING_DEF,"$SecurityDemo\FileUpload\VulnerableFileUploader->handleUpload@tmp-1[""success""]"
30064771257,68719476871,REACHING_DEF,"$SecurityDemo\FileUpload\VulnerableFileUploader->handleUpload@tmp-1[""message""]"
30064771257,25769803813,REACHING_DEF,"$SecurityDemo\FileUpload\VulnerableFileUploader->handleUpload@tmp-1[""message""]"
30064771257,30064771256,REACHING_DEF,"$SecurityDemo\FileUpload\VulnerableFileUploader->handleUpload@tmp-1[""message""]"
30064771257,68719476869,REACHING_DEF,"$SecurityDemo\FileUpload\VulnerableFileUploader->handleUpload@tmp-1[""message""]"
30064771259,68719476871,REACHING_DEF,"$SecurityDemo\FileUpload\VulnerableFileUploader->handleUpload@tmp-1[""path""]"
30064771259,25769803813,REACHING_DEF,"$SecurityDemo\FileUpload\VulnerableFileUploader->handleUpload@tmp-1[""path""]"
30064771259,30064771258,REACHING_DEF,"$SecurityDemo\FileUpload\VulnerableFileUploader->handleUpload@tmp-1[""path""]"
30064771261,30064771260,REACHING_DEF,"$SecurityDemo\FileUpload\VulnerableFileUploader->handleUpload@tmp-1[""url""]"
30064771261,25769803813,REACHING_DEF,"$SecurityDemo\FileUpload\VulnerableFileUploader->handleUpload@tmp-1[""url""]"
30064771262,30064771261,REACHING_DEF,"str_replace(""./"","""",$targetPath)"
30064771262,30064771260,REACHING_DEF,"str_replace(""./"","""",$targetPath)"
30064771264,68719476874,REACHING_DEF,array()
30064771264,30064771263,REACHING_DEF,array()
30064771266,30064771265,REACHING_DEF,"$SecurityDemo\FileUpload\VulnerableFileUploader->handleUpload@tmp-2[""success""]"
30064771266,25769803814,REACHING_DEF,"$SecurityDemo\FileUpload\VulnerableFileUploader->handleUpload@tmp-2[""success""]"
30064771266,68719476876,REACHING_DEF,"$SecurityDemo\FileUpload\VulnerableFileUploader->handleUpload@tmp-2[""success""]"
30064771268,25769803814,REACHING_DEF,"$SecurityDemo\FileUpload\VulnerableFileUploader->handleUpload@tmp-2[""message""]"
30064771268,30064771267,REACHING_DEF,"$SecurityDemo\FileUpload\VulnerableFileUploader->handleUpload@tmp-2[""message""]"
30064771270,68719476878,REACHING_DEF,array()
30064771270,30064771269,REACHING_DEF,array()
30064771272,30064771271,REACHING_DEF,"$SecurityDemo\FileUpload\VulnerableFileUploader->handleUpload@tmp-0[""success""]"
30064771272,68719476880,REACHING_DEF,"$SecurityDemo\FileUpload\VulnerableFileUploader->handleUpload@tmp-0[""success""]"
30064771272,25769803816,REACHING_DEF,"$SecurityDemo\FileUpload\VulnerableFileUploader->handleUpload@tmp-0[""success""]"
30064771274,25769803816,REACHING_DEF,"$SecurityDemo\FileUpload\VulnerableFileUploader->handleUpload@tmp-0[""message""]"
30064771274,30064771273,REACHING_DEF,"$SecurityDemo\FileUpload\VulnerableFileUploader->handleUpload@tmp-0[""message""]"
30064771275,30064771274,REACHING_DEF,"""上传异常: "" . $e->getMessage()"
30064771275,30064771273,REACHING_DEF,"""上传异常: "" . $e->getMessage()"
30064771276,30064771275,REACHING_DEF,$e->getMessage()
30064771277,128849018889,REACHING_DEF,"$fileData[""error""] !== UPLOAD_ERR_OK"
30064771278,120259084301,REACHING_DEF,"$fileData[""error""]"
30064771278,30064771279,REACHING_DEF,"$fileData[""error""]"
30064771278,30064771277,REACHING_DEF,"$fileData[""error""]"
30064771278,68719476885,REACHING_DEF,"$fileData[""error""]"
30064771278,128849018889,REACHING_DEF,"$fileData[""error""]"
30064771279,30064771278,REACHING_DEF,UPLOAD_ERR_OK
30064771279,30064771277,REACHING_DEF,UPLOAD_ERR_OK
30064771279,128849018889,REACHING_DEF,UPLOAD_ERR_OK
30064771281,30064771282,REACHING_DEF,"$fileData[""size""]"
30064771281,30064771280,REACHING_DEF,"$fileData[""size""]"
30064771282,30064771281,REACHING_DEF,$this->maxFileSize
30064771282,30064771280,REACHING_DEF,$this->maxFileSize
30064771283,128849018890,REACHING_DEF,$fileName = !empty($customName) ? $customName : $originalName
30064771284,68719476887,REACHING_DEF,!empty($customName) ? $customName : $originalName
30064771284,30064771283,REACHING_DEF,!empty($customName) ? $customName : $originalName
30064771284,128849018890,REACHING_DEF,!empty($customName) ? $customName : $originalName
30064771285,30064771284,REACHING_DEF,!empty($customName)
30064771285,128849018890,REACHING_DEF,!empty($customName)
30064771286,128849018890,REACHING_DEF,empty($customName)
30064771287,128849018890,REACHING_DEF,"$fileName = str_replace(,"""",$fileName)"
30064771288,68719476891,REACHING_DEF,"str_replace(,"""",$fileName)"
30064771288,30064771287,REACHING_DEF,"str_replace(,"""",$fileName)"
30064771288,128849018890,REACHING_DEF,"str_replace(,"""",$fileName)"
30064771289,128849018890,REACHING_DEF,$SecurityDemo\FileUpload\VulnerableFileUploader->processFileName@tmp-0 = array()
30064771290,68719476892,REACHING_DEF,array()
30064771290,30064771289,REACHING_DEF,array()
30064771290,128849018890,REACHING_DEF,array()
30064771291,128849018890,REACHING_DEF,"$SecurityDemo\FileUpload\VulnerableFileUploader->processFileName@tmp-0[0] = ""../"""
30064771292,68719476894,REACHING_DEF,$SecurityDemo\FileUpload\VulnerableFileUploader->processFileName@tmp-0[0]
30064771292,25769803821,REACHING_DEF,$SecurityDemo\FileUpload\VulnerableFileUploader->processFileName@tmp-0[0]
30064771292,30064771291,REACHING_DEF,$SecurityDemo\FileUpload\VulnerableFileUploader->processFileName@tmp-0[0]
30064771292,128849018890,REACHING_DEF,$SecurityDemo\FileUpload\VulnerableFileUploader->processFileName@tmp-0[0]
30064771293,128849018890,REACHING_DEF,"$SecurityDemo\FileUpload\VulnerableFileUploader->processFileName@tmp-0[1] = ""..\\"""
30064771294,30064771293,REACHING_DEF,$SecurityDemo\FileUpload\VulnerableFileUploader->processFileName@tmp-0[1]
30064771294,25769803821,REACHING_DEF,$SecurityDemo\FileUpload\VulnerableFileUploader->processFileName@tmp-0[1]
30064771294,128849018890,REACHING_DEF,$SecurityDemo\FileUpload\VulnerableFileUploader->processFileName@tmp-0[1]
30064771295,128849018890,REACHING_DEF,"$extension = strtolower(pathinfo($fileName,PATHINFO_EXTENSION))"
30064771296,68719476897,REACHING_DEF,"strtolower(pathinfo($fileName,PATHINFO_EXTENSION))"
30064771296,30064771295,REACHING_DEF,"strtolower(pathinfo($fileName,PATHINFO_EXTENSION))"
30064771296,128849018890,REACHING_DEF,"strtolower(pathinfo($fileName,PATHINFO_EXTENSION))"
30064771297,30064771296,REACHING_DEF,"pathinfo($fileName,PATHINFO_EXTENSION)"
30064771297,128849018890,REACHING_DEF,"pathinfo($fileName,PATHINFO_EXTENSION)"
30064771298,68719476898,REACHING_DEF,PATHINFO_EXTENSION
30064771298,30064771297,REACHING_DEF,PATHINFO_EXTENSION
30064771298,128849018890,REACHING_DEF,PATHINFO_EXTENSION
30064771299,128849018890,REACHING_DEF,$SecurityDemo\FileUpload\VulnerableFileUploader->processFileName@iter_tmp-1 = $this->blockedExtensions
30064771300,120259084302,REACHING_DEF,$this->blockedExtensions
30064771300,68719476900,REACHING_DEF,$this->blockedExtensions
30064771300,30064771299,REACHING_DEF,$this->blockedExtensions
30064771300,128849018890,REACHING_DEF,$this->blockedExtensions
30064771301,128849018890,REACHING_DEF,$blocked = $SecurityDemo\FileUpload\VulnerableFileUploader->processFileName@iter_tmp-1->current()
30064771302,68719476902,REACHING_DEF,$SecurityDemo\FileUpload\VulnerableFileUploader->processFileName@iter_tmp-1->current()
30064771302,30064771301,REACHING_DEF,$SecurityDemo\FileUpload\VulnerableFileUploader->processFileName@iter_tmp-1->current()
30064771302,128849018890,REACHING_DEF,$SecurityDemo\FileUpload\VulnerableFileUploader->processFileName@iter_tmp-1->current()
30064771303,128849018890,REACHING_DEF,!is_null($blocked)
30064771304,30064771303,REACHING_DEF,is_null($blocked)
30064771304,128849018890,REACHING_DEF,is_null($blocked)
30064771305,128849018890,REACHING_DEF,$SecurityDemo\FileUpload\VulnerableFileUploader->processFileName@iter_tmp-1->next()
30064771306,128849018890,REACHING_DEF,$blocked = $SecurityDemo\FileUpload\VulnerableFileUploader->processFileName@iter_tmp-1->current()
30064771307,68719476906,REACHING_DEF,$SecurityDemo\FileUpload\VulnerableFileUploader->processFileName@iter_tmp-1->current()
30064771307,30064771306,REACHING_DEF,$SecurityDemo\FileUpload\VulnerableFileUploader->processFileName@iter_tmp-1->current()
30064771307,128849018890,REACHING_DEF,$SecurityDemo\FileUpload\VulnerableFileUploader->processFileName@iter_tmp-1->current()
30064771308,128849018890,REACHING_DEF,$extension === $blocked
30064771309,128849018890,REACHING_DEF,"$fileName .= "".txt"""
30064771310,128849018892,REACHING_DEF,$this->imageMagicBytes = 
30064771311,120259084305,REACHING_DEF,$this->imageMagicBytes
30064771311,68719476935,REACHING_DEF,$this->imageMagicBytes
30064771311,30064771310,REACHING_DEF,$this->imageMagicBytes
30064771311,128849018892,REACHING_DEF,$this->imageMagicBytes
30064771312,128849018892,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->__construct@tmp-0 = array()
30064771313,68719476913,REACHING_DEF,array()
30064771313,30064771312,REACHING_DEF,array()
30064771314,128849018892,REACHING_DEF,"$SecurityDemo\FileUpload\FileValidator->__construct@tmp-0[""jpeg""] = "
30064771315,68719476920,REACHING_DEF,"$SecurityDemo\FileUpload\FileValidator->__construct@tmp-0[""jpeg""]"
30064771315,25769803828,REACHING_DEF,"$SecurityDemo\FileUpload\FileValidator->__construct@tmp-0[""jpeg""]"
30064771315,30064771314,REACHING_DEF,"$SecurityDemo\FileUpload\FileValidator->__construct@tmp-0[""jpeg""]"
30064771315,68719476927,REACHING_DEF,"$SecurityDemo\FileUpload\FileValidator->__construct@tmp-0[""jpeg""]"
30064771315,128849018892,REACHING_DEF,"$SecurityDemo\FileUpload\FileValidator->__construct@tmp-0[""jpeg""]"
30064771316,128849018892,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->__construct@tmp-1 = array()
30064771317,68719476915,REACHING_DEF,array()
30064771317,30064771316,REACHING_DEF,array()
30064771318,128849018892,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->__construct@tmp-1[0] = 255
30064771319,68719476917,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->__construct@tmp-1[0]
30064771319,30064771318,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->__construct@tmp-1[0]
30064771319,68719476918,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->__construct@tmp-1[0]
30064771319,25769803829,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->__construct@tmp-1[0]
30064771319,128849018892,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->__construct@tmp-1[0]
30064771320,128849018892,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->__construct@tmp-1[1] = 216
30064771321,30064771320,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->__construct@tmp-1[1]
30064771321,68719476918,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->__construct@tmp-1[1]
30064771321,25769803829,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->__construct@tmp-1[1]
30064771321,128849018892,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->__construct@tmp-1[1]
30064771322,128849018892,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->__construct@tmp-1[2] = 255
30064771323,30064771322,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->__construct@tmp-1[2]
30064771323,25769803829,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->__construct@tmp-1[2]
30064771323,128849018892,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->__construct@tmp-1[2]
30064771324,128849018892,REACHING_DEF,"$SecurityDemo\FileUpload\FileValidator->__construct@tmp-0[""png""] = "
30064771325,30064771324,REACHING_DEF,"$SecurityDemo\FileUpload\FileValidator->__construct@tmp-0[""png""]"
30064771325,25769803828,REACHING_DEF,"$SecurityDemo\FileUpload\FileValidator->__construct@tmp-0[""png""]"
30064771325,68719476927,REACHING_DEF,"$SecurityDemo\FileUpload\FileValidator->__construct@tmp-0[""png""]"
30064771325,128849018892,REACHING_DEF,"$SecurityDemo\FileUpload\FileValidator->__construct@tmp-0[""png""]"
30064771326,128849018892,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->__construct@tmp-2 = array()
30064771327,68719476921,REACHING_DEF,array()
30064771327,30064771326,REACHING_DEF,array()
30064771328,128849018892,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->__construct@tmp-2[0] = 137
30064771329,30064771328,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->__construct@tmp-2[0]
30064771329,68719476924,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->__construct@tmp-2[0]
30064771329,25769803830,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->__construct@tmp-2[0]
30064771329,68719476925,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->__construct@tmp-2[0]
30064771329,68719476923,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->__construct@tmp-2[0]
30064771329,128849018892,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->__construct@tmp-2[0]
30064771330,128849018892,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->__construct@tmp-2[1] = 80
30064771331,68719476924,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->__construct@tmp-2[1]
30064771331,25769803830,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->__construct@tmp-2[1]
30064771331,68719476925,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->__construct@tmp-2[1]
30064771331,30064771330,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->__construct@tmp-2[1]
30064771331,128849018892,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->__construct@tmp-2[1]
30064771332,128849018892,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->__construct@tmp-2[2] = 78
30064771333,25769803830,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->__construct@tmp-2[2]
30064771333,68719476925,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->__construct@tmp-2[2]
30064771333,30064771332,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->__construct@tmp-2[2]
30064771333,128849018892,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->__construct@tmp-2[2]
30064771334,128849018892,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->__construct@tmp-2[3] = 71
30064771335,25769803830,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->__construct@tmp-2[3]
30064771335,30064771334,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->__construct@tmp-2[3]
30064771335,128849018892,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->__construct@tmp-2[3]
30064771336,128849018892,REACHING_DEF,"$SecurityDemo\FileUpload\FileValidator->__construct@tmp-0[""gif""] = "
30064771337,30064771336,REACHING_DEF,"$SecurityDemo\FileUpload\FileValidator->__construct@tmp-0[""gif""]"
30064771337,25769803828,REACHING_DEF,"$SecurityDemo\FileUpload\FileValidator->__construct@tmp-0[""gif""]"
30064771337,128849018892,REACHING_DEF,"$SecurityDemo\FileUpload\FileValidator->__construct@tmp-0[""gif""]"
30064771338,128849018892,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->__construct@tmp-3 = array()
30064771339,68719476928,REACHING_DEF,array()
30064771339,30064771338,REACHING_DEF,array()
30064771340,128849018892,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->__construct@tmp-3[0] = 71
30064771341,68719476930,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->__construct@tmp-3[0]
30064771341,25769803831,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->__construct@tmp-3[0]
30064771341,30064771340,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->__construct@tmp-3[0]
30064771341,68719476931,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->__construct@tmp-3[0]
30064771341,68719476932,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->__construct@tmp-3[0]
30064771341,128849018892,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->__construct@tmp-3[0]
30064771342,128849018892,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->__construct@tmp-3[1] = 73
30064771343,25769803831,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->__construct@tmp-3[1]
30064771343,68719476931,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->__construct@tmp-3[1]
30064771343,30064771342,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->__construct@tmp-3[1]
30064771343,68719476932,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->__construct@tmp-3[1]
30064771343,128849018892,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->__construct@tmp-3[1]
30064771344,128849018892,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->__construct@tmp-3[2] = 70
30064771345,25769803831,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->__construct@tmp-3[2]
30064771345,30064771344,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->__construct@tmp-3[2]
30064771345,68719476932,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->__construct@tmp-3[2]
30064771345,128849018892,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->__construct@tmp-3[2]
30064771346,128849018892,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->__construct@tmp-3[3] = 56
30064771347,25769803831,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->__construct@tmp-3[3]
30064771347,30064771346,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->__construct@tmp-3[3]
30064771347,128849018892,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->__construct@tmp-3[3]
30064771348,128849018892,REACHING_DEF,$this->dangerousPatterns = 
30064771349,30064771348,REACHING_DEF,$this->dangerousPatterns
30064771349,120259084305,REACHING_DEF,$this->dangerousPatterns
30064771349,128849018892,REACHING_DEF,$this->dangerousPatterns
30064771350,128849018892,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->__construct@tmp-4 = array()
30064771351,68719476936,REACHING_DEF,array()
30064771351,30064771350,REACHING_DEF,array()
30064771351,128849018892,REACHING_DEF,array()
30064771352,128849018892,REACHING_DEF,"$SecurityDemo\FileUpload\FileValidator->__construct@tmp-4[0] = ""<?php"""
30064771353,68719476939,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->__construct@tmp-4[0]
30064771353,25769803832,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->__construct@tmp-4[0]
30064771353,68719476941,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->__construct@tmp-4[0]
30064771353,30064771352,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->__construct@tmp-4[0]
30064771353,68719476942,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->__construct@tmp-4[0]
30064771353,68719476938,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->__construct@tmp-4[0]
30064771353,68719476940,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->__construct@tmp-4[0]
30064771353,128849018892,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->__construct@tmp-4[0]
30064771354,128849018892,REACHING_DEF,"$SecurityDemo\FileUpload\FileValidator->__construct@tmp-4[1] = ""<?="""
30064771355,68719476939,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->__construct@tmp-4[1]
30064771355,25769803832,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->__construct@tmp-4[1]
30064771355,68719476941,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->__construct@tmp-4[1]
30064771355,68719476942,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->__construct@tmp-4[1]
30064771355,30064771354,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->__construct@tmp-4[1]
30064771355,68719476940,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->__construct@tmp-4[1]
30064771355,128849018892,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->__construct@tmp-4[1]
30064771356,128849018892,REACHING_DEF,"$SecurityDemo\FileUpload\FileValidator->__construct@tmp-4[2] = ""<script"""
30064771357,25769803832,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->__construct@tmp-4[2]
30064771357,68719476941,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->__construct@tmp-4[2]
30064771357,30064771356,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->__construct@tmp-4[2]
30064771357,68719476942,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->__construct@tmp-4[2]
30064771357,68719476940,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->__construct@tmp-4[2]
30064771357,128849018892,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->__construct@tmp-4[2]
30064771358,128849018892,REACHING_DEF,"$SecurityDemo\FileUpload\FileValidator->__construct@tmp-4[3] = ""eval("""
30064771359,25769803832,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->__construct@tmp-4[3]
30064771359,68719476941,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->__construct@tmp-4[3]
30064771359,68719476942,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->__construct@tmp-4[3]
30064771359,30064771358,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->__construct@tmp-4[3]
30064771359,128849018892,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->__construct@tmp-4[3]
30064771360,128849018892,REACHING_DEF,"$SecurityDemo\FileUpload\FileValidator->__construct@tmp-4[4] = ""system("""
30064771361,30064771360,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->__construct@tmp-4[4]
30064771361,25769803832,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->__construct@tmp-4[4]
30064771361,68719476942,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->__construct@tmp-4[4]
30064771361,128849018892,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->__construct@tmp-4[4]
30064771362,128849018892,REACHING_DEF,"$SecurityDemo\FileUpload\FileValidator->__construct@tmp-4[5] = ""exec("""
30064771363,25769803832,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->__construct@tmp-4[5]
30064771363,30064771362,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->__construct@tmp-4[5]
30064771363,128849018892,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->__construct@tmp-4[5]
30064771364,128849018893,REACHING_DEF,!file_exists($filePath)
30064771365,30064771364,REACHING_DEF,file_exists($filePath)
30064771365,128849018893,REACHING_DEF,file_exists($filePath)
30064771367,30064771366,REACHING_DEF,$this->checkMagicBytes($filePath)
30064771369,30064771368,REACHING_DEF,$this->checkFileContent($filePath)
30064771370,128849018894,REACHING_DEF,"$handle = fopen($filePath,""rb"")"
30064771371,68719476949,REACHING_DEF,"fopen($filePath,""rb"")"
30064771371,30064771370,REACHING_DEF,"fopen($filePath,""rb"")"
30064771371,128849018894,REACHING_DEF,"fopen($filePath,""rb"")"
30064771372,128849018894,REACHING_DEF,!$handle
30064771374,68719476952,REACHING_DEF,"fread($handle,8)"
30064771374,30064771373,REACHING_DEF,"fread($handle,8)"
30064771377,68719476955,REACHING_DEF,$this->imageMagicBytes
30064771377,30064771376,REACHING_DEF,$this->imageMagicBytes
30064771379,68719476957,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->checkMagicBytes@iter_tmp-0->key()
30064771379,30064771378,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->checkMagicBytes@iter_tmp-0->key()
30064771381,68719476959,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->checkMagicBytes@iter_tmp-0->current()
30064771381,30064771380,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->checkMagicBytes@iter_tmp-0->current()
30064771383,30064771382,REACHING_DEF,is_null($bytes)
30064771386,68719476963,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->checkMagicBytes@iter_tmp-0->key()
30064771386,30064771385,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->checkMagicBytes@iter_tmp-0->key()
30064771388,68719476965,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->checkMagicBytes@iter_tmp-0->current()
30064771388,30064771387,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->checkMagicBytes@iter_tmp-0->current()
30064771392,68719476969,REACHING_DEF,count($bytes)
30064771392,30064771391,REACHING_DEF,count($bytes)
30064771395,30064771398,REACHING_DEF,!isset($header[$i])
30064771395,30064771394,REACHING_DEF,!isset($header[$i])
30064771396,30064771395,REACHING_DEF,isset($header[$i])
30064771397,68719476972,REACHING_DEF,$header[$i]
30064771397,68719476974,REACHING_DEF,$header[$i]
30064771397,30064771400,REACHING_DEF,$header[$i]
30064771397,30064771396,REACHING_DEF,$header[$i]
30064771398,30064771395,REACHING_DEF,ord($header[$i]) !== $bytes[$i]
30064771398,30064771394,REACHING_DEF,ord($header[$i]) !== $bytes[$i]
30064771399,30064771401,REACHING_DEF,ord($header[$i])
30064771399,30064771398,REACHING_DEF,ord($header[$i])
30064771400,68719476972,REACHING_DEF,$header[$i]
30064771400,30064771399,REACHING_DEF,$header[$i]
30064771400,30064771397,REACHING_DEF,$header[$i]
30064771401,68719476961,REACHING_DEF,$bytes[$i]
30064771401,30064771399,REACHING_DEF,$bytes[$i]
30064771401,30064771398,REACHING_DEF,$bytes[$i]
30064771401,68719476970,REACHING_DEF,$bytes[$i]
30064771401,68719476976,REACHING_DEF,$bytes[$i]
30064771403,128849018895,REACHING_DEF,$content = file_get_contents($filePath)
30064771404,68719476980,REACHING_DEF,file_get_contents($filePath)
30064771404,30064771403,REACHING_DEF,file_get_contents($filePath)
30064771404,128849018895,REACHING_DEF,file_get_contents($filePath)
30064771405,128849018895,REACHING_DEF,$content === false
30064771407,68719476983,REACHING_DEF,$this->dangerousPatterns
30064771407,30064771406,REACHING_DEF,$this->dangerousPatterns
30064771409,68719476985,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->checkFileContent@iter_tmp-0->current()
30064771409,30064771408,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->checkFileContent@iter_tmp-0->current()
30064771411,30064771410,REACHING_DEF,is_null($pattern)
30064771414,68719476989,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->checkFileContent@iter_tmp-0->current()
30064771414,30064771413,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->checkFileContent@iter_tmp-0->current()
30064771416,30064771415,REACHING_DEF,"strpos($content,$pattern)"
30064771417,128849018896,REACHING_DEF,$extension = $this->getFileExtension($fileName)
30064771418,68719476993,REACHING_DEF,$this->getFileExtension($fileName)
30064771418,30064771417,REACHING_DEF,$this->getFileExtension($fileName)
30064771418,128849018896,REACHING_DEF,$this->getFileExtension($fileName)
30064771419,128849018896,REACHING_DEF,$allowedExtensions = 
30064771420,128849018896,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->validateFileExtension@tmp-0 = array()
30064771421,68719476997,REACHING_DEF,array()
30064771421,30064771420,REACHING_DEF,array()
30064771421,128849018896,REACHING_DEF,array()
30064771422,128849018896,REACHING_DEF,"$SecurityDemo\FileUpload\FileValidator->validateFileExtension@tmp-0[0] = ""jpg"""
30064771423,30064771422,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->validateFileExtension@tmp-0[0]
30064771423,68719477001,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->validateFileExtension@tmp-0[0]
30064771423,68719476999,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->validateFileExtension@tmp-0[0]
30064771423,68719477002,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->validateFileExtension@tmp-0[0]
30064771423,68719477000,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->validateFileExtension@tmp-0[0]
30064771423,25769803854,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->validateFileExtension@tmp-0[0]
30064771423,128849018896,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->validateFileExtension@tmp-0[0]
30064771424,128849018896,REACHING_DEF,"$SecurityDemo\FileUpload\FileValidator->validateFileExtension@tmp-0[1] = ""jpeg"""
30064771425,68719477001,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->validateFileExtension@tmp-0[1]
30064771425,68719477002,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->validateFileExtension@tmp-0[1]
30064771425,68719477000,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->validateFileExtension@tmp-0[1]
30064771425,25769803854,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->validateFileExtension@tmp-0[1]
30064771425,30064771424,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->validateFileExtension@tmp-0[1]
30064771425,128849018896,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->validateFileExtension@tmp-0[1]
30064771426,128849018896,REACHING_DEF,"$SecurityDemo\FileUpload\FileValidator->validateFileExtension@tmp-0[2] = ""png"""
30064771427,68719477001,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->validateFileExtension@tmp-0[2]
30064771427,68719477002,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->validateFileExtension@tmp-0[2]
30064771427,30064771426,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->validateFileExtension@tmp-0[2]
30064771427,25769803854,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->validateFileExtension@tmp-0[2]
30064771427,128849018896,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->validateFileExtension@tmp-0[2]
30064771428,128849018896,REACHING_DEF,"$SecurityDemo\FileUpload\FileValidator->validateFileExtension@tmp-0[3] = ""gif"""
30064771429,30064771428,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->validateFileExtension@tmp-0[3]
30064771429,68719477002,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->validateFileExtension@tmp-0[3]
30064771429,25769803854,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->validateFileExtension@tmp-0[3]
30064771429,128849018896,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->validateFileExtension@tmp-0[3]
30064771430,128849018896,REACHING_DEF,"$SecurityDemo\FileUpload\FileValidator->validateFileExtension@tmp-0[4] = ""txt"""
30064771431,30064771430,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->validateFileExtension@tmp-0[4]
30064771431,25769803854,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->validateFileExtension@tmp-0[4]
30064771431,128849018896,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->validateFileExtension@tmp-0[4]
30064771432,146028888089,REACHING_DEF,"in_array($extension,$allowedExtensions)"
30064771432,128849018896,REACHING_DEF,"in_array($extension,$allowedExtensions)"
30064771433,128849018897,REACHING_DEF,"$parts = explode(""."",$fileName)"
30064771434,68719477006,REACHING_DEF,"explode(""."",$fileName)"
30064771434,30064771433,REACHING_DEF,"explode(""."",$fileName)"
30064771434,128849018897,REACHING_DEF,"explode(""."",$fileName)"
30064771435,146028888090,REACHING_DEF,strtolower(end($parts))
30064771435,128849018897,REACHING_DEF,strtolower(end($parts))
30064771436,30064771435,REACHING_DEF,end($parts)
30064771436,128849018897,REACHING_DEF,end($parts)
30064771437,128849018898,REACHING_DEF,$fileName = $originalName
30064771438,128849018898,REACHING_DEF,"$fileName = str_replace(,"""",$fileName)"
30064771439,68719477011,REACHING_DEF,"str_replace(,"""",$fileName)"
30064771439,30064771438,REACHING_DEF,"str_replace(,"""",$fileName)"
30064771439,128849018898,REACHING_DEF,"str_replace(,"""",$fileName)"
30064771440,128849018898,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->sanitizeFileName@tmp-0 = array()
30064771441,68719477012,REACHING_DEF,array()
30064771441,30064771440,REACHING_DEF,array()
30064771441,128849018898,REACHING_DEF,array()
30064771442,128849018898,REACHING_DEF,"$SecurityDemo\FileUpload\FileValidator->sanitizeFileName@tmp-0[0] = ""../"""
30064771443,25769803857,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->sanitizeFileName@tmp-0[0]
30064771443,68719477016,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->sanitizeFileName@tmp-0[0]
30064771443,68719477014,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->sanitizeFileName@tmp-0[0]
30064771443,68719477017,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->sanitizeFileName@tmp-0[0]
30064771443,68719477015,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->sanitizeFileName@tmp-0[0]
30064771443,30064771442,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->sanitizeFileName@tmp-0[0]
30064771443,128849018898,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->sanitizeFileName@tmp-0[0]
30064771444,128849018898,REACHING_DEF,"$SecurityDemo\FileUpload\FileValidator->sanitizeFileName@tmp-0[1] = ""..\\"""
30064771445,25769803857,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->sanitizeFileName@tmp-0[1]
30064771445,68719477016,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->sanitizeFileName@tmp-0[1]
30064771445,30064771444,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->sanitizeFileName@tmp-0[1]
30064771445,68719477017,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->sanitizeFileName@tmp-0[1]
30064771445,68719477015,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->sanitizeFileName@tmp-0[1]
30064771445,128849018898,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->sanitizeFileName@tmp-0[1]
30064771446,128849018898,REACHING_DEF,"$SecurityDemo\FileUpload\FileValidator->sanitizeFileName@tmp-0[2] = ""<"""
30064771447,25769803857,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->sanitizeFileName@tmp-0[2]
30064771447,68719477016,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->sanitizeFileName@tmp-0[2]
30064771447,30064771446,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->sanitizeFileName@tmp-0[2]
30064771447,68719477017,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->sanitizeFileName@tmp-0[2]
30064771447,128849018898,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->sanitizeFileName@tmp-0[2]
30064771448,128849018898,REACHING_DEF,"$SecurityDemo\FileUpload\FileValidator->sanitizeFileName@tmp-0[3] = "">"""
30064771449,25769803857,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->sanitizeFileName@tmp-0[3]
30064771449,30064771448,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->sanitizeFileName@tmp-0[3]
30064771449,68719477017,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->sanitizeFileName@tmp-0[3]
30064771449,128849018898,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->sanitizeFileName@tmp-0[3]
30064771450,128849018898,REACHING_DEF,"$SecurityDemo\FileUpload\FileValidator->sanitizeFileName@tmp-0[4] = ""|"""
30064771451,25769803857,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->sanitizeFileName@tmp-0[4]
30064771451,30064771450,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->sanitizeFileName@tmp-0[4]
30064771451,128849018898,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->sanitizeFileName@tmp-0[4]
68719476737,30064771076,REACHING_DEF,$SecurityDemo\FileUpload\FileUploadController->__construct@tmp-0
68719476737,68719476738,REACHING_DEF,$SecurityDemo\FileUpload\FileUploadController->__construct@tmp-0
68719476738,25769803778,REACHING_DEF,$SecurityDemo\FileUpload\FileUploadController->__construct@tmp-0
68719476738,30064771078,REACHING_DEF,$SecurityDemo\FileUpload\FileUploadController->__construct@tmp-0
68719476738,128849018880,REACHING_DEF,$SecurityDemo\FileUpload\FileUploadController->__construct@tmp-0
68719476741,68719476742,REACHING_DEF,$SecurityDemo\FileUpload\FileUploadController->__construct@tmp-1
68719476741,30064771081,REACHING_DEF,$SecurityDemo\FileUpload\FileUploadController->__construct@tmp-1
68719476742,30064771083,REACHING_DEF,$SecurityDemo\FileUpload\FileUploadController->__construct@tmp-1
68719476742,25769803779,REACHING_DEF,$SecurityDemo\FileUpload\FileUploadController->__construct@tmp-1
68719476742,128849018880,REACHING_DEF,$SecurityDemo\FileUpload\FileUploadController->__construct@tmp-1
68719476744,30064771084,REACHING_DEF,$action
68719476744,68719476747,REACHING_DEF,$action
68719476747,68719476749,REACHING_DEF,$action
68719476747,30064771089,REACHING_DEF,$action
68719476747,128849018881,REACHING_DEF,$action
68719476748,30064771090,REACHING_DEF,$this
68719476748,120259084289,REACHING_DEF,$this
68719476748,128849018881,REACHING_DEF,$this
68719476749,30064771091,REACHING_DEF,$action
68719476750,30064771092,REACHING_DEF,$this
68719476751,68719476753,REACHING_DEF,$SecurityDemo\FileUpload\FileUploadController->handleRequest@tmp-0
68719476751,68719476752,REACHING_DEF,$SecurityDemo\FileUpload\FileUploadController->handleRequest@tmp-0
68719476751,25769803784,REACHING_DEF,$SecurityDemo\FileUpload\FileUploadController->handleRequest@tmp-0
68719476751,30064771093,REACHING_DEF,$SecurityDemo\FileUpload\FileUploadController->handleRequest@tmp-0
68719476756,68719476757,REACHING_DEF,$SecurityDemo\FileUpload\FileUploadController->processUpload@tmp-0
68719476756,25769803787,REACHING_DEF,$SecurityDemo\FileUpload\FileUploadController->processUpload@tmp-0
68719476756,68719476758,REACHING_DEF,$SecurityDemo\FileUpload\FileUploadController->processUpload@tmp-0
68719476756,30064771102,REACHING_DEF,$SecurityDemo\FileUpload\FileUploadController->processUpload@tmp-0
68719476756,128849018882,REACHING_DEF,$SecurityDemo\FileUpload\FileUploadController->processUpload@tmp-0
68719476760,30064771108,REACHING_DEF,$customName
68719476760,68719476774,REACHING_DEF,$customName
68719476763,68719476766,REACHING_DEF,$subDir
68719476763,30064771113,REACHING_DEF,$subDir
68719476766,30064771119,REACHING_DEF,$subDir
68719476766,68719476770,REACHING_DEF,$subDir
68719476768,68719476769,REACHING_DEF,$SecurityDemo\FileUpload\FileUploadController->processUpload@tmp-1
68719476768,30064771122,REACHING_DEF,$SecurityDemo\FileUpload\FileUploadController->processUpload@tmp-1
68719476769,30064771124,REACHING_DEF,$SecurityDemo\FileUpload\FileUploadController->processUpload@tmp-1
68719476769,25769803789,REACHING_DEF,$SecurityDemo\FileUpload\FileUploadController->processUpload@tmp-1
68719476770,30064771126,REACHING_DEF,$subDir
68719476774,30064771128,REACHING_DEF,$customName
68719476774,30064771129,REACHING_DEF,$customName
68719476774,30064771127,REACHING_DEF,$customName
68719476776,30064771133,REACHING_DEF,$SecurityDemo\FileUpload\FileUploadController->processBatchUpload@tmp-0
68719476776,68719476777,REACHING_DEF,$SecurityDemo\FileUpload\FileUploadController->processBatchUpload@tmp-0
68719476776,25769803792,REACHING_DEF,$SecurityDemo\FileUpload\FileUploadController->processBatchUpload@tmp-0
68719476776,68719476778,REACHING_DEF,$SecurityDemo\FileUpload\FileUploadController->processBatchUpload@tmp-0
68719476776,128849018883,REACHING_DEF,$SecurityDemo\FileUpload\FileUploadController->processBatchUpload@tmp-0
68719476780,68719476814,REACHING_DEF,$results
68719476780,68719476819,REACHING_DEF,$results
68719476780,30064771139,REACHING_DEF,$results
68719476781,30064771140,REACHING_DEF,$SecurityDemo\FileUpload\FileUploadController->processBatchUpload@tmp-1
68719476781,25769803793,REACHING_DEF,$SecurityDemo\FileUpload\FileUploadController->processBatchUpload@tmp-1
68719476783,68719476787,REACHING_DEF,$files
68719476783,68719476792,REACHING_DEF,$files
68719476783,30064771163,REACHING_DEF,$files
68719476783,30064771155,REACHING_DEF,$files
68719476783,68719476801,REACHING_DEF,$files
68719476783,68719476804,REACHING_DEF,$files
68719476783,68719476798,REACHING_DEF,$files
68719476783,30064771159,REACHING_DEF,$files
68719476783,30064771147,REACHING_DEF,$files
68719476783,30064771167,REACHING_DEF,$files
68719476783,68719476795,REACHING_DEF,$files
68719476783,30064771171,REACHING_DEF,$files
68719476783,30064771142,REACHING_DEF,$files
68719476785,30064771144,REACHING_DEF,$i
68719476785,68719476786,REACHING_DEF,$i
68719476786,68719476788,REACHING_DEF,$i
68719476786,30064771146,REACHING_DEF,$i
68719476786,30064771145,REACHING_DEF,$i
68719476788,30064771148,REACHING_DEF,$i
68719476788,68719476786,REACHING_DEF,$i
68719476789,30064771149,REACHING_DEF,$fileData
68719476789,68719476812,REACHING_DEF,$fileData
68719476790,68719476803,REACHING_DEF,$SecurityDemo\FileUpload\FileUploadController->processBatchUpload@tmp-2
68719476790,68719476800,REACHING_DEF,$SecurityDemo\FileUpload\FileUploadController->processBatchUpload@tmp-2
68719476790,25769803795,REACHING_DEF,$SecurityDemo\FileUpload\FileUploadController->processBatchUpload@tmp-2
68719476790,30064771150,REACHING_DEF,$SecurityDemo\FileUpload\FileUploadController->processBatchUpload@tmp-2
68719476790,68719476797,REACHING_DEF,$SecurityDemo\FileUpload\FileUploadController->processBatchUpload@tmp-2
68719476790,68719476794,REACHING_DEF,$SecurityDemo\FileUpload\FileUploadController->processBatchUpload@tmp-2
68719476790,68719476791,REACHING_DEF,$SecurityDemo\FileUpload\FileUploadController->processBatchUpload@tmp-2
68719476807,30064771172,REACHING_DEF,$customName
68719476807,68719476813,REACHING_DEF,$customName
68719476810,68719476815,REACHING_DEF,$result
68719476810,30064771176,REACHING_DEF,$result
68719476812,30064771178,REACHING_DEF,$fileData
68719476812,68719476813,REACHING_DEF,$fileData
68719476812,30064771177,REACHING_DEF,$fileData
68719476813,30064771178,REACHING_DEF,$customName
68719476813,68719476812,REACHING_DEF,$customName
68719476813,30064771177,REACHING_DEF,$customName
68719476814,68719476815,REACHING_DEF,$results
68719476814,30064771179,REACHING_DEF,$results
68719476814,68719476819,REACHING_DEF,$results
68719476815,68719476814,REACHING_DEF,$result
68719476815,30064771179,REACHING_DEF,$result
68719476816,30064771180,REACHING_DEF,$SecurityDemo\FileUpload\FileUploadController->processBatchUpload@tmp-3
68719476816,68719476817,REACHING_DEF,$SecurityDemo\FileUpload\FileUploadController->processBatchUpload@tmp-3
68719476816,25769803796,REACHING_DEF,$SecurityDemo\FileUpload\FileUploadController->processBatchUpload@tmp-3
68719476816,68719476818,REACHING_DEF,$SecurityDemo\FileUpload\FileUploadController->processBatchUpload@tmp-3
68719476819,30064771185,REACHING_DEF,$results
68719476819,30064771184,REACHING_DEF,$results
68719476821,30064771189,REACHING_DEF,$data
68719476821,30064771188,REACHING_DEF,$data
68719476821,120259084293,REACHING_DEF,$data
68719476821,128849018884,REACHING_DEF,$data
68719476823,128849018885,REACHING_DEF,$_SERVER
68719476824,128849018885,REACHING_DEF,$_GET
68719476825,68719476830,REACHING_DEF,$controller
68719476825,30064771196,REACHING_DEF,$controller
68719476826,68719476827,REACHING_DEF,$FileUploadHandler.php:SecurityDemo\FileUpload@tmp-0
68719476826,30064771197,REACHING_DEF,$FileUploadHandler.php:SecurityDemo\FileUpload@tmp-0
68719476827,30064771199,REACHING_DEF,$FileUploadHandler.php:SecurityDemo\FileUpload@tmp-0
68719476827,25769803799,REACHING_DEF,$FileUploadHandler.php:SecurityDemo\FileUpload@tmp-0
68719476827,128849018885,REACHING_DEF,$FileUploadHandler.php:SecurityDemo\FileUpload@tmp-0
68719476829,68719476832,REACHING_DEF,$result
68719476829,30064771200,REACHING_DEF,$result
68719476830,68719476831,REACHING_DEF,$controller
68719476830,30064771201,REACHING_DEF,$controller
68719476831,68719476832,REACHING_DEF,$controller
68719476831,30064771202,REACHING_DEF,$controller
68719476831,128849018885,REACHING_DEF,$controller
68719476832,68719476831,REACHING_DEF,$result
68719476832,30064771202,REACHING_DEF,$result
68719476832,128849018885,REACHING_DEF,$result
68719476834,120259084295,REACHING_DEF,$uploadDir
68719476834,30064771205,REACHING_DEF,$uploadDir
68719476834,30064771204,REACHING_DEF,$uploadDir
68719476834,128849018886,REACHING_DEF,$uploadDir
68719476836,25769803804,REACHING_DEF,$SecurityDemo\FileUpload\VulnerableFileUploader->__construct@tmp-0
68719476836,68719476840,REACHING_DEF,$SecurityDemo\FileUpload\VulnerableFileUploader->__construct@tmp-0
68719476836,68719476838,REACHING_DEF,$SecurityDemo\FileUpload\VulnerableFileUploader->__construct@tmp-0
68719476836,30064771208,REACHING_DEF,$SecurityDemo\FileUpload\VulnerableFileUploader->__construct@tmp-0
68719476836,68719476839,REACHING_DEF,$SecurityDemo\FileUpload\VulnerableFileUploader->__construct@tmp-0
68719476836,68719476837,REACHING_DEF,$SecurityDemo\FileUpload\VulnerableFileUploader->__construct@tmp-0
68719476836,128849018886,REACHING_DEF,$SecurityDemo\FileUpload\VulnerableFileUploader->__construct@tmp-0
68719476843,68719476844,REACHING_DEF,$SecurityDemo\FileUpload\VulnerableFileUploader->__construct@tmp-1
68719476843,25769803805,REACHING_DEF,$SecurityDemo\FileUpload\VulnerableFileUploader->__construct@tmp-1
68719476843,68719476845,REACHING_DEF,$SecurityDemo\FileUpload\VulnerableFileUploader->__construct@tmp-1
68719476843,30064771220,REACHING_DEF,$SecurityDemo\FileUpload\VulnerableFileUploader->__construct@tmp-1
68719476843,128849018886,REACHING_DEF,$SecurityDemo\FileUpload\VulnerableFileUploader->__construct@tmp-1
68719476848,30064771230,REACHING_DEF,$this
68719476848,120259084294,REACHING_DEF,$this
68719476848,128849018886,REACHING_DEF,$this
68719476851,68719476858,REACHING_DEF,$this
68719476851,120259084297,REACHING_DEF,$this
68719476851,68719476852,REACHING_DEF,$this
68719476851,30064771237,REACHING_DEF,$this
68719476851,128849018888,REACHING_DEF,$this
68719476852,30064771251,REACHING_DEF,$fileData
68719476852,30064771246,REACHING_DEF,$fileData
68719476852,68719476859,REACHING_DEF,$fileData
68719476852,68719476864,REACHING_DEF,$fileData
68719476852,68719476851,REACHING_DEF,$fileData
68719476852,30064771237,REACHING_DEF,$fileData
68719476852,120259084298,REACHING_DEF,$fileData
68719476852,128849018888,REACHING_DEF,$fileData
68719476853,25769803811,REACHING_DEF,$SecurityDemo\FileUpload\VulnerableFileUploader->handleUpload@tmp-0
68719476853,30064771238,REACHING_DEF,$SecurityDemo\FileUpload\VulnerableFileUploader->handleUpload@tmp-0
68719476853,68719476854,REACHING_DEF,$SecurityDemo\FileUpload\VulnerableFileUploader->handleUpload@tmp-0
68719476853,68719476855,REACHING_DEF,$SecurityDemo\FileUpload\VulnerableFileUploader->handleUpload@tmp-0
68719476853,128849018888,REACHING_DEF,$SecurityDemo\FileUpload\VulnerableFileUploader->handleUpload@tmp-0
68719476857,30064771244,REACHING_DEF,$fileName
68719476857,68719476863,REACHING_DEF,$fileName
68719476858,68719476862,REACHING_DEF,$this
68719476858,30064771246,REACHING_DEF,$this
68719476858,68719476860,REACHING_DEF,$this
68719476858,30064771245,REACHING_DEF,$this
68719476858,30064771249,REACHING_DEF,$this
68719476860,68719476858,REACHING_DEF,$customName
68719476860,30064771246,REACHING_DEF,$customName
68719476860,30064771245,REACHING_DEF,$customName
68719476861,68719476865,REACHING_DEF,$targetPath
68719476861,30064771247,REACHING_DEF,$targetPath
68719476863,30064771249,REACHING_DEF,$fileName
68719476863,30064771248,REACHING_DEF,$fileName
68719476865,30064771251,REACHING_DEF,$targetPath
68719476865,30064771250,REACHING_DEF,$targetPath
68719476865,68719476870,REACHING_DEF,$targetPath
68719476866,68719476868,REACHING_DEF,$SecurityDemo\FileUpload\VulnerableFileUploader->handleUpload@tmp-1
68719476866,68719476871,REACHING_DEF,$SecurityDemo\FileUpload\VulnerableFileUploader->handleUpload@tmp-1
68719476866,25769803813,REACHING_DEF,$SecurityDemo\FileUpload\VulnerableFileUploader->handleUpload@tmp-1
68719476866,68719476867,REACHING_DEF,$SecurityDemo\FileUpload\VulnerableFileUploader->handleUpload@tmp-1
68719476866,68719476869,REACHING_DEF,$SecurityDemo\FileUpload\VulnerableFileUploader->handleUpload@tmp-1
68719476866,30064771252,REACHING_DEF,$SecurityDemo\FileUpload\VulnerableFileUploader->handleUpload@tmp-1
68719476870,68719476872,REACHING_DEF,$targetPath
68719476870,30064771259,REACHING_DEF,$targetPath
68719476870,30064771258,REACHING_DEF,$targetPath
68719476872,30064771262,REACHING_DEF,$targetPath
68719476874,68719476875,REACHING_DEF,$SecurityDemo\FileUpload\VulnerableFileUploader->handleUpload@tmp-2
68719476874,25769803814,REACHING_DEF,$SecurityDemo\FileUpload\VulnerableFileUploader->handleUpload@tmp-2
68719476874,68719476876,REACHING_DEF,$SecurityDemo\FileUpload\VulnerableFileUploader->handleUpload@tmp-2
68719476874,30064771263,REACHING_DEF,$SecurityDemo\FileUpload\VulnerableFileUploader->handleUpload@tmp-2
68719476878,68719476879,REACHING_DEF,$SecurityDemo\FileUpload\VulnerableFileUploader->handleUpload@tmp-0
68719476878,30064771269,REACHING_DEF,$SecurityDemo\FileUpload\VulnerableFileUploader->handleUpload@tmp-0
68719476878,68719476880,REACHING_DEF,$SecurityDemo\FileUpload\VulnerableFileUploader->handleUpload@tmp-0
68719476878,25769803816,REACHING_DEF,$SecurityDemo\FileUpload\VulnerableFileUploader->handleUpload@tmp-0
68719476881,30064771276,REACHING_DEF,$e
68719476887,30064771283,REACHING_DEF,$fileName
68719476887,68719476896,REACHING_DEF,$fileName
68719476888,30064771286,REACHING_DEF,$customName
68719476888,68719476889,REACHING_DEF,$customName
68719476889,30064771284,REACHING_DEF,$customName
68719476889,120259084304,REACHING_DEF,$customName
68719476889,128849018890,REACHING_DEF,$customName
68719476890,30064771284,REACHING_DEF,$originalName
68719476890,120259084303,REACHING_DEF,$originalName
68719476890,128849018890,REACHING_DEF,$originalName
68719476891,68719476898,REACHING_DEF,$fileName
68719476891,30064771287,REACHING_DEF,$fileName
68719476892,68719476894,REACHING_DEF,$SecurityDemo\FileUpload\VulnerableFileUploader->processFileName@tmp-0
68719476892,30064771289,REACHING_DEF,$SecurityDemo\FileUpload\VulnerableFileUploader->processFileName@tmp-0
68719476892,25769803821,REACHING_DEF,$SecurityDemo\FileUpload\VulnerableFileUploader->processFileName@tmp-0
68719476892,68719476893,REACHING_DEF,$SecurityDemo\FileUpload\VulnerableFileUploader->processFileName@tmp-0
68719476892,128849018890,REACHING_DEF,$SecurityDemo\FileUpload\VulnerableFileUploader->processFileName@tmp-0
68719476896,30064771288,REACHING_DEF,$fileName
68719476897,68719476908,REACHING_DEF,$extension
68719476897,30064771295,REACHING_DEF,$extension
68719476897,128849018890,REACHING_DEF,$extension
68719476898,68719476910,REACHING_DEF,$fileName
68719476898,30064771298,REACHING_DEF,$fileName
68719476898,30064771297,REACHING_DEF,$fileName
68719476898,68719476911,REACHING_DEF,$fileName
68719476898,128849018890,REACHING_DEF,$fileName
68719476900,68719476903,REACHING_DEF,$SecurityDemo\FileUpload\VulnerableFileUploader->processFileName@iter_tmp-1
68719476900,30064771299,REACHING_DEF,$SecurityDemo\FileUpload\VulnerableFileUploader->processFileName@iter_tmp-1
68719476902,68719476904,REACHING_DEF,$blocked
68719476902,30064771301,REACHING_DEF,$blocked
68719476903,68719476905,REACHING_DEF,$SecurityDemo\FileUpload\VulnerableFileUploader->processFileName@iter_tmp-1
68719476903,30064771302,REACHING_DEF,$SecurityDemo\FileUpload\VulnerableFileUploader->processFileName@iter_tmp-1
68719476903,128849018890,REACHING_DEF,$SecurityDemo\FileUpload\VulnerableFileUploader->processFileName@iter_tmp-1
68719476904,30064771304,REACHING_DEF,$blocked
68719476904,68719476909,REACHING_DEF,$blocked
68719476904,128849018890,REACHING_DEF,$blocked
68719476905,30064771305,REACHING_DEF,$SecurityDemo\FileUpload\VulnerableFileUploader->processFileName@iter_tmp-1
68719476905,68719476907,REACHING_DEF,$SecurityDemo\FileUpload\VulnerableFileUploader->processFileName@iter_tmp-1
68719476906,68719476904,REACHING_DEF,$blocked
68719476906,30064771306,REACHING_DEF,$blocked
68719476907,68719476905,REACHING_DEF,$SecurityDemo\FileUpload\VulnerableFileUploader->processFileName@iter_tmp-1
68719476907,30064771307,REACHING_DEF,$SecurityDemo\FileUpload\VulnerableFileUploader->processFileName@iter_tmp-1
68719476907,128849018890,REACHING_DEF,$SecurityDemo\FileUpload\VulnerableFileUploader->processFileName@iter_tmp-1
68719476908,68719476909,REACHING_DEF,$extension
68719476908,30064771308,REACHING_DEF,$extension
68719476908,128849018890,REACHING_DEF,$extension
68719476909,68719476908,REACHING_DEF,$blocked
68719476909,30064771308,REACHING_DEF,$blocked
68719476909,128849018890,REACHING_DEF,$blocked
68719476910,30064771309,REACHING_DEF,$fileName
68719476910,68719476911,REACHING_DEF,$fileName
68719476910,128849018890,REACHING_DEF,$fileName
68719476911,146028888078,REACHING_DEF,$fileName
68719476913,68719476920,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->__construct@tmp-0
68719476913,68719476914,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->__construct@tmp-0
68719476913,30064771312,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->__construct@tmp-0
68719476913,25769803828,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->__construct@tmp-0
68719476913,68719476927,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->__construct@tmp-0
68719476913,128849018892,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->__construct@tmp-0
68719476915,68719476917,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->__construct@tmp-1
68719476915,30064771316,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->__construct@tmp-1
68719476915,68719476918,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->__construct@tmp-1
68719476915,68719476916,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->__construct@tmp-1
68719476915,25769803829,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->__construct@tmp-1
68719476915,128849018892,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->__construct@tmp-1
68719476921,68719476924,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->__construct@tmp-2
68719476921,68719476922,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->__construct@tmp-2
68719476921,25769803830,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->__construct@tmp-2
68719476921,68719476925,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->__construct@tmp-2
68719476921,68719476923,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->__construct@tmp-2
68719476921,30064771326,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->__construct@tmp-2
68719476921,128849018892,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->__construct@tmp-2
68719476928,68719476930,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->__construct@tmp-3
68719476928,25769803831,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->__construct@tmp-3
68719476928,30064771338,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->__construct@tmp-3
68719476928,68719476931,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->__construct@tmp-3
68719476928,68719476929,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->__construct@tmp-3
68719476928,68719476932,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->__construct@tmp-3
68719476928,128849018892,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->__construct@tmp-3
68719476936,68719476939,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->__construct@tmp-4
68719476936,25769803832,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->__construct@tmp-4
68719476936,30064771350,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->__construct@tmp-4
68719476936,68719476937,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->__construct@tmp-4
68719476936,68719476941,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->__construct@tmp-4
68719476936,68719476942,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->__construct@tmp-4
68719476936,68719476938,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->__construct@tmp-4
68719476936,68719476940,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->__construct@tmp-4
68719476936,128849018892,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->__construct@tmp-4
68719476944,120259084307,REACHING_DEF,$filePath
68719476944,30064771365,REACHING_DEF,$filePath
68719476944,68719476946,REACHING_DEF,$filePath
68719476944,128849018893,REACHING_DEF,$filePath
68719476945,68719476946,REACHING_DEF,$this
68719476945,30064771367,REACHING_DEF,$this
68719476945,68719476947,REACHING_DEF,$this
68719476946,68719476945,REACHING_DEF,$filePath
68719476946,30064771367,REACHING_DEF,$filePath
68719476946,68719476948,REACHING_DEF,$filePath
68719476947,68719476948,REACHING_DEF,$this
68719476947,30064771369,REACHING_DEF,$this
68719476948,68719476947,REACHING_DEF,$filePath
68719476948,30064771369,REACHING_DEF,$filePath
68719476949,68719476951,REACHING_DEF,$handle
68719476949,30064771370,REACHING_DEF,$handle
68719476950,30064771371,REACHING_DEF,$filePath
68719476950,120259084309,REACHING_DEF,$filePath
68719476950,128849018894,REACHING_DEF,$filePath
68719476951,30064771372,REACHING_DEF,$handle
68719476951,68719476953,REACHING_DEF,$handle
68719476951,128849018894,REACHING_DEF,$handle
68719476952,68719476972,REACHING_DEF,$header
68719476952,68719476974,REACHING_DEF,$header
68719476952,30064771400,REACHING_DEF,$header
68719476952,30064771373,REACHING_DEF,$header
68719476952,30064771397,REACHING_DEF,$header
68719476953,68719476954,REACHING_DEF,$handle
68719476953,30064771374,REACHING_DEF,$handle
68719476954,30064771375,REACHING_DEF,$handle
68719476955,30064771376,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->checkMagicBytes@iter_tmp-0
68719476955,68719476958,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->checkMagicBytes@iter_tmp-0
68719476957,30064771378,REACHING_DEF,$type
68719476958,68719476960,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->checkMagicBytes@iter_tmp-0
68719476958,30064771379,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->checkMagicBytes@iter_tmp-0
68719476959,30064771380,REACHING_DEF,$bytes
68719476959,68719476961,REACHING_DEF,$bytes
68719476960,68719476962,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->checkMagicBytes@iter_tmp-0
68719476960,30064771381,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->checkMagicBytes@iter_tmp-0
68719476961,30064771383,REACHING_DEF,$bytes
68719476961,68719476970,REACHING_DEF,$bytes
68719476962,30064771384,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->checkMagicBytes@iter_tmp-0
68719476962,68719476964,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->checkMagicBytes@iter_tmp-0
68719476963,30064771385,REACHING_DEF,$type
68719476964,68719476966,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->checkMagicBytes@iter_tmp-0
68719476964,30064771386,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->checkMagicBytes@iter_tmp-0
68719476965,68719476961,REACHING_DEF,$bytes
68719476965,30064771387,REACHING_DEF,$bytes
68719476966,68719476962,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->checkMagicBytes@iter_tmp-0
68719476966,30064771388,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->checkMagicBytes@iter_tmp-0
68719476967,30064771389,REACHING_DEF,$match
68719476968,30064771390,REACHING_DEF,$i
68719476968,68719476969,REACHING_DEF,$i
68719476969,68719476971,REACHING_DEF,$i
68719476969,30064771392,REACHING_DEF,$i
68719476969,30064771391,REACHING_DEF,$i
68719476970,30064771401,REACHING_DEF,$bytes
68719476970,30064771392,REACHING_DEF,$bytes
68719476970,68719476976,REACHING_DEF,$bytes
68719476971,30064771393,REACHING_DEF,$i
68719476971,68719476969,REACHING_DEF,$i
68719476978,30064771402,REACHING_DEF,$match
68719476980,30064771403,REACHING_DEF,$content
68719476980,68719476982,REACHING_DEF,$content
68719476981,30064771404,REACHING_DEF,$filePath
68719476981,120259084311,REACHING_DEF,$filePath
68719476981,128849018895,REACHING_DEF,$filePath
68719476982,68719476991,REACHING_DEF,$content
68719476982,30064771405,REACHING_DEF,$content
68719476982,128849018895,REACHING_DEF,$content
68719476983,30064771406,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->checkFileContent@iter_tmp-0
68719476983,68719476986,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->checkFileContent@iter_tmp-0
68719476985,30064771408,REACHING_DEF,$pattern
68719476985,68719476987,REACHING_DEF,$pattern
68719476986,68719476988,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->checkFileContent@iter_tmp-0
68719476986,30064771409,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->checkFileContent@iter_tmp-0
68719476987,68719476992,REACHING_DEF,$pattern
68719476987,30064771411,REACHING_DEF,$pattern
68719476988,30064771412,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->checkFileContent@iter_tmp-0
68719476988,68719476990,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->checkFileContent@iter_tmp-0
68719476989,30064771413,REACHING_DEF,$pattern
68719476989,68719476987,REACHING_DEF,$pattern
68719476990,68719476988,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->checkFileContent@iter_tmp-0
68719476990,30064771414,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->checkFileContent@iter_tmp-0
68719476991,68719476992,REACHING_DEF,$content
68719476991,30064771416,REACHING_DEF,$content
68719476992,68719476991,REACHING_DEF,$pattern
68719476992,30064771416,REACHING_DEF,$pattern
68719476993,30064771417,REACHING_DEF,$extension
68719476993,68719477004,REACHING_DEF,$extension
68719476994,120259084312,REACHING_DEF,$this
68719476994,68719476995,REACHING_DEF,$this
68719476994,30064771418,REACHING_DEF,$this
68719476994,128849018896,REACHING_DEF,$this
68719476995,120259084313,REACHING_DEF,$fileName
68719476995,68719476994,REACHING_DEF,$fileName
68719476995,30064771418,REACHING_DEF,$fileName
68719476995,128849018896,REACHING_DEF,$fileName
68719476996,68719477005,REACHING_DEF,$allowedExtensions
68719476996,30064771419,REACHING_DEF,$allowedExtensions
68719476997,30064771420,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->validateFileExtension@tmp-0
68719476997,68719477001,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->validateFileExtension@tmp-0
68719476997,68719476999,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->validateFileExtension@tmp-0
68719476997,68719477002,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->validateFileExtension@tmp-0
68719476997,68719477000,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->validateFileExtension@tmp-0
68719476997,25769803854,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->validateFileExtension@tmp-0
68719476997,68719476998,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->validateFileExtension@tmp-0
68719476997,128849018896,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->validateFileExtension@tmp-0
68719477004,68719477005,REACHING_DEF,$extension
68719477004,30064771432,REACHING_DEF,$extension
68719477004,128849018896,REACHING_DEF,$extension
68719477005,68719477004,REACHING_DEF,$allowedExtensions
68719477005,30064771432,REACHING_DEF,$allowedExtensions
68719477005,128849018896,REACHING_DEF,$allowedExtensions
68719477006,68719477008,REACHING_DEF,$parts
68719477006,30064771433,REACHING_DEF,$parts
68719477007,30064771434,REACHING_DEF,$fileName
68719477007,120259084315,REACHING_DEF,$fileName
68719477007,128849018897,REACHING_DEF,$fileName
68719477008,30064771436,REACHING_DEF,$parts
68719477008,128849018897,REACHING_DEF,$parts
68719477009,68719477019,REACHING_DEF,$fileName
68719477009,30064771437,REACHING_DEF,$fileName
68719477010,120259084317,REACHING_DEF,$originalName
68719477010,68719477009,REACHING_DEF,$originalName
68719477010,30064771437,REACHING_DEF,$originalName
68719477010,128849018898,REACHING_DEF,$originalName
68719477011,68719477020,REACHING_DEF,$fileName
68719477011,30064771438,REACHING_DEF,$fileName
68719477011,128849018898,REACHING_DEF,$fileName
68719477012,25769803857,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->sanitizeFileName@tmp-0
68719477012,30064771440,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->sanitizeFileName@tmp-0
68719477012,68719477016,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->sanitizeFileName@tmp-0
68719477012,68719477014,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->sanitizeFileName@tmp-0
68719477012,68719477017,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->sanitizeFileName@tmp-0
68719477012,68719477013,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->sanitizeFileName@tmp-0
68719477012,68719477015,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->sanitizeFileName@tmp-0
68719477012,128849018898,REACHING_DEF,$SecurityDemo\FileUpload\FileValidator->sanitizeFileName@tmp-0
68719477019,30064771439,REACHING_DEF,$fileName
68719477020,146028888091,REACHING_DEF,$fileName
90194313216,30064771072,REACHING_DEF,"""FileUploadVulnerability.php"""
90194313217,30064771073,REACHING_DEF,"""FileValidator.php"""
90194313220,30064771088,REACHING_DEF,"""upload"""
90194313220,30064771087,REACHING_DEF,"""upload"""
90194313221,68719476747,REACHING_DEF,"""upload"""
90194313221,30064771089,REACHING_DEF,"""upload"""
90194313222,68719476749,REACHING_DEF,"""batch"""
90194313222,30064771091,REACHING_DEF,"""batch"""
90194313224,30064771096,REACHING_DEF,false
90194313224,30064771095,REACHING_DEF,false
90194313226,30064771098,REACHING_DEF,"""未知操作"""
90194313226,30064771097,REACHING_DEF,"""未知操作"""
90194313229,30064771105,REACHING_DEF,false
90194313229,30064771104,REACHING_DEF,false
90194313231,30064771107,REACHING_DEF,"""未找到上传文件"""
90194313231,30064771106,REACHING_DEF,"""未找到上传文件"""
90194313234,30064771112,REACHING_DEF,""""""
90194313234,30064771111,REACHING_DEF,""""""
90194313237,30064771117,REACHING_DEF,""""""
90194313237,30064771116,REACHING_DEF,""""""
90194313238,68719476770,REACHING_DEF,"""./uploads/"""
90194313238,30064771126,REACHING_DEF,"""./uploads/"""
90194313239,30064771126,REACHING_DEF,"""/"""
90194313239,30064771125,REACHING_DEF,"""/"""
90194313243,30064771136,REACHING_DEF,false
90194313243,30064771135,REACHING_DEF,false
90194313245,30064771138,REACHING_DEF,"""未找到批量上传文件"""
90194313245,30064771137,REACHING_DEF,"""未找到批量上传文件"""
90194313247,68719476785,REACHING_DEF,0
90194313247,30064771144,REACHING_DEF,0
90194313260,30064771174,REACHING_DEF,""""""
90194313260,30064771173,REACHING_DEF,""""""
90194313262,30064771183,REACHING_DEF,true
90194313262,30064771182,REACHING_DEF,true
90194313264,30064771186,REACHING_DEF,"""Content-Type: application/json"""
90194313266,30064771192,REACHING_DEF,"""POST"""
90194313266,30064771191,REACHING_DEF,"""POST"""
90194313268,30064771203,REACHING_DEF,"""<!DOCTYPE html>\n<html>\n<head><title>文件上传测试 - 仅用于安全研究</title></head>\n<body>\n<h2>文件上传漏洞演示</h2>\n<p style=\""color:red;\"">警告：此页面包含安全漏洞，仅用于教学目的</p>\n\n<h3>单文件上传</h3>\n<form method=\""post\"" enctype=\""multipart/form-data\"">\n    <input type=\""hidden\"" name=\""action\"" value=\""upload\"">\n    文件: <input type=\""file\"" name=\""file\""><br>\n    自定义文件名: <input type=\""text\"" name=\""filename\"" placeholder=\""可选\""><br>\n    子目录: <input type=\""text\"" name=\""subdir\"" placeholder=\""可选\""><br>\n    <input type=\""submit\"" value=\""上传\"">\n</form>\n\n<h3>批量上传</h3>\n<form method=\""post\"" enctype=\""multipart/form-data\"">\n    <input type=\""hidden\"" name=\""action\"" value=\""batch\"">\n    文件: <input type=\""file\"" name=\""files[]\"" multiple><br>\n    <input type=\""submit\"" value=\""批量上传\"">\n</form>\n</body>\n</html>"""
90194313270,30064771211,REACHING_DEF,"""image/jpeg"""
90194313270,30064771210,REACHING_DEF,"""image/jpeg"""
90194313272,30064771213,REACHING_DEF,"""image/png"""
90194313272,30064771212,REACHING_DEF,"""image/png"""
90194313274,30064771215,REACHING_DEF,"""image/gif"""
90194313274,30064771214,REACHING_DEF,"""image/gif"""
90194313276,30064771217,REACHING_DEF,"""text/plain"""
90194313276,30064771216,REACHING_DEF,"""text/plain"""
90194313278,30064771223,REACHING_DEF,"""exe"""
90194313278,30064771222,REACHING_DEF,"""exe"""
90194313280,30064771225,REACHING_DEF,"""bat"""
90194313280,30064771224,REACHING_DEF,"""bat"""
90194313281,30064771229,REACHING_DEF,5
90194313282,30064771229,REACHING_DEF,1024
90194313283,30064771229,REACHING_DEF,1024
90194313283,30064771228,REACHING_DEF,1024
90194313284,30064771235,REACHING_DEF,493
90194313284,30064771234,REACHING_DEF,493
90194313285,30064771235,REACHING_DEF,true
90194313285,30064771234,REACHING_DEF,true
90194313287,30064771241,REACHING_DEF,false
90194313287,30064771240,REACHING_DEF,false
90194313289,30064771243,REACHING_DEF,"""文件验证失败"""
90194313289,30064771242,REACHING_DEF,"""文件验证失败"""
90194313293,30064771255,REACHING_DEF,true
90194313293,30064771254,REACHING_DEF,true
90194313295,30064771257,REACHING_DEF,"""文件上传成功"""
90194313295,30064771256,REACHING_DEF,"""文件上传成功"""
90194313298,68719476872,REACHING_DEF,"""./"""
90194313298,30064771262,REACHING_DEF,"""./"""
90194313299,68719476872,REACHING_DEF,""""""
90194313299,30064771262,REACHING_DEF,""""""
90194313301,30064771266,REACHING_DEF,false
90194313301,30064771265,REACHING_DEF,false
90194313303,30064771268,REACHING_DEF,"""文件移动失败"""
90194313303,30064771267,REACHING_DEF,"""文件移动失败"""
90194313305,30064771272,REACHING_DEF,false
90194313305,30064771271,REACHING_DEF,false
90194313307,30064771276,REACHING_DEF,"""上传异常: """
90194313307,30064771275,REACHING_DEF,"""上传异常: """
90194313309,146028888075,REACHING_DEF,false
90194313311,146028888076,REACHING_DEF,false
90194313312,146028888077,REACHING_DEF,true
90194313314,30064771292,REACHING_DEF,"""../"""
90194313314,30064771291,REACHING_DEF,"""../"""
90194313316,30064771294,REACHING_DEF,"""..\\"""
90194313316,30064771293,REACHING_DEF,"""..\\"""
90194313317,68719476896,REACHING_DEF,""""""
90194313317,30064771288,REACHING_DEF,""""""
90194313318,68719476910,REACHING_DEF,""".txt"""
90194313318,30064771309,REACHING_DEF,""".txt"""
90194313321,30064771319,REACHING_DEF,255
90194313321,30064771318,REACHING_DEF,255
90194313323,30064771321,REACHING_DEF,216
90194313323,30064771320,REACHING_DEF,216
90194313325,30064771323,REACHING_DEF,255
90194313325,30064771322,REACHING_DEF,255
90194313328,30064771329,REACHING_DEF,137
90194313328,30064771328,REACHING_DEF,137
90194313330,30064771331,REACHING_DEF,80
90194313330,30064771330,REACHING_DEF,80
90194313332,30064771333,REACHING_DEF,78
90194313332,30064771332,REACHING_DEF,78
90194313334,30064771335,REACHING_DEF,71
90194313334,30064771334,REACHING_DEF,71
90194313337,30064771341,REACHING_DEF,71
90194313337,30064771340,REACHING_DEF,71
90194313339,30064771343,REACHING_DEF,73
90194313339,30064771342,REACHING_DEF,73
90194313341,30064771345,REACHING_DEF,70
90194313341,30064771344,REACHING_DEF,70
90194313343,30064771347,REACHING_DEF,56
90194313343,30064771346,REACHING_DEF,56
90194313345,30064771353,REACHING_DEF,"""<?php"""
90194313345,30064771352,REACHING_DEF,"""<?php"""
90194313347,30064771355,REACHING_DEF,"""<?="""
90194313347,30064771354,REACHING_DEF,"""<?="""
90194313349,30064771357,REACHING_DEF,"""<script"""
90194313349,30064771356,REACHING_DEF,"""<script"""
90194313351,30064771359,REACHING_DEF,"""eval("""
90194313351,30064771358,REACHING_DEF,"""eval("""
90194313353,30064771361,REACHING_DEF,"""system("""
90194313353,30064771360,REACHING_DEF,"""system("""
90194313355,30064771363,REACHING_DEF,"""exec("""
90194313355,30064771362,REACHING_DEF,"""exec("""
90194313356,146028888079,REACHING_DEF,false
90194313357,146028888080,REACHING_DEF,false
90194313358,146028888081,REACHING_DEF,false
90194313359,146028888082,REACHING_DEF,true
90194313360,68719476950,REACHING_DEF,"""rb"""
90194313360,30064771371,REACHING_DEF,"""rb"""
90194313361,146028888083,REACHING_DEF,false
90194313362,68719476953,REACHING_DEF,8
90194313362,30064771374,REACHING_DEF,8
90194313363,68719476967,REACHING_DEF,true
90194313363,30064771389,REACHING_DEF,true
90194313364,68719476968,REACHING_DEF,0
90194313364,30064771390,REACHING_DEF,0
90194313365,68719476978,REACHING_DEF,false
90194313365,30064771402,REACHING_DEF,false
90194313366,146028888084,REACHING_DEF,true
90194313367,146028888085,REACHING_DEF,false
90194313368,68719476982,REACHING_DEF,false
90194313368,30064771405,REACHING_DEF,false
90194313369,146028888086,REACHING_DEF,false
90194313370,30064771416,REACHING_DEF,false
90194313370,30064771415,REACHING_DEF,false
90194313371,146028888087,REACHING_DEF,false
90194313372,146028888088,REACHING_DEF,true
90194313374,30064771423,REACHING_DEF,"""jpg"""
90194313374,30064771422,REACHING_DEF,"""jpg"""
90194313376,30064771425,REACHING_DEF,"""jpeg"""
90194313376,30064771424,REACHING_DEF,"""jpeg"""
90194313378,30064771427,REACHING_DEF,"""png"""
90194313378,30064771426,REACHING_DEF,"""png"""
90194313380,30064771429,REACHING_DEF,"""gif"""
90194313380,30064771428,REACHING_DEF,"""gif"""
90194313382,30064771431,REACHING_DEF,"""txt"""
90194313382,30064771430,REACHING_DEF,"""txt"""
90194313383,68719477007,REACHING_DEF,"""."""
90194313383,30064771434,REACHING_DEF,"""."""
90194313385,30064771443,REACHING_DEF,"""../"""
90194313385,30064771442,REACHING_DEF,"""../"""
90194313387,30064771445,REACHING_DEF,"""..\\"""
90194313387,30064771444,REACHING_DEF,"""..\\"""
90194313389,30064771447,REACHING_DEF,"""<"""
90194313389,30064771446,REACHING_DEF,"""<"""
90194313391,30064771449,REACHING_DEF,""">"""
90194313391,30064771448,REACHING_DEF,""">"""
90194313393,30064771451,REACHING_DEF,"""|"""
90194313393,30064771450,REACHING_DEF,"""|"""
90194313394,68719477019,REACHING_DEF,""""""
90194313394,30064771439,REACHING_DEF,""""""
111669149696,68719476824,REACHING_DEF,
111669149696,68719476831,REACHING_DEF,
111669149696,90194313268,REACHING_DEF,
111669149696,30064771198,REACHING_DEF,
111669149696,25769803799,REACHING_DEF,
111669149696,68719476830,REACHING_DEF,
111669149696,90194313266,REACHING_DEF,
111669149696,90194313216,REACHING_DEF,
111669149696,90194313217,REACHING_DEF,
111669149696,68719476823,REACHING_DEF,
111669149696,68719476828,REACHING_DEF,
111669149696,68719476832,REACHING_DEF,
111669149696,68719476827,REACHING_DEF,
111669149697,68719476742,REACHING_DEF,
111669149697,68719476743,REACHING_DEF,
111669149697,25769803778,REACHING_DEF,
111669149697,68719476736,REACHING_DEF,
111669149697,68719476739,REACHING_DEF,
111669149697,115964116992,REACHING_DEF,
111669149697,68719476738,REACHING_DEF,
111669149697,30064771077,REACHING_DEF,
111669149697,25769803779,REACHING_DEF,
111669149697,68719476740,REACHING_DEF,
111669149697,30064771082,REACHING_DEF,
111669149698,68719476745,REACHING_DEF,
111669149698,90194313220,REACHING_DEF,
111669149698,68719476753,REACHING_DEF,
111669149698,115964116993,REACHING_DEF,
111669149698,68719476746,REACHING_DEF,
111669149698,68719476747,REACHING_DEF,
111669149698,90194313222,REACHING_DEF,
111669149698,68719476750,REACHING_DEF,
111669149698,68719476752,REACHING_DEF,
111669149698,68719476748,REACHING_DEF,
111669149698,90194313224,REACHING_DEF,
111669149698,68719476749,REACHING_DEF,
111669149698,30064771094,REACHING_DEF,
111669149698,90194313226,REACHING_DEF,
111669149698,25769803784,REACHING_DEF,
111669149698,90194313221,REACHING_DEF,
111669149698,68719476754,REACHING_DEF,
111669149698,146028888066,REACHING_DEF,
111669149699,68719476774,REACHING_DEF,
111669149699,68719476761,REACHING_DEF,
111669149699,30064771123,REACHING_DEF,
111669149699,68719476765,REACHING_DEF,
111669149699,90194313237,REACHING_DEF,
111669149699,68719476770,REACHING_DEF,
111669149699,68719476757,REACHING_DEF,
111669149699,68719476769,REACHING_DEF,
111669149699,90194313231,REACHING_DEF,
111669149699,68719476767,REACHING_DEF,
111669149699,68719476773,REACHING_DEF,
111669149699,90194313238,REACHING_DEF,
111669149699,90194313234,REACHING_DEF,
111669149699,68719476755,REACHING_DEF,
111669149699,25769803789,REACHING_DEF,
111669149699,68719476762,REACHING_DEF,
111669149699,25769803787,REACHING_DEF,
111669149699,68719476766,REACHING_DEF,
111669149699,90194313239,REACHING_DEF,
111669149699,68719476772,REACHING_DEF,
111669149699,30064771103,REACHING_DEF,
111669149699,68719476764,REACHING_DEF,
111669149699,68719476758,REACHING_DEF,
111669149699,90194313229,REACHING_DEF,
111669149699,68719476771,REACHING_DEF,
111669149699,115964116994,REACHING_DEF,
111669149699,68719476759,REACHING_DEF,
111669149699,146028888067,REACHING_DEF,
111669149700,68719476797,REACHING_DEF,
111669149700,68719476782,REACHING_DEF,
111669149700,68719476814,REACHING_DEF,
111669149700,68719476792,REACHING_DEF,
111669149700,90194313247,REACHING_DEF,
111669149700,30064771141,REACHING_DEF,
111669149700,68719476806,REACHING_DEF,
111669149700,68719476817,REACHING_DEF,
111669149700,25769803795,REACHING_DEF,
111669149700,68719476798,REACHING_DEF,
111669149700,68719476778,REACHING_DEF,
111669149700,30064771151,REACHING_DEF,
111669149700,68719476784,REACHING_DEF,
111669149700,68719476788,REACHING_DEF,
111669149700,68719476801,REACHING_DEF,
111669149700,68719476820,REACHING_DEF,
111669149700,30064771181,REACHING_DEF,
111669149700,68719476819,REACHING_DEF,
111669149700,68719476787,REACHING_DEF,
111669149700,90194313245,REACHING_DEF,
111669149700,90194313243,REACHING_DEF,
111669149700,68719476812,REACHING_DEF,
111669149700,68719476794,REACHING_DEF,
111669149700,68719476775,REACHING_DEF,
111669149700,90194313262,REACHING_DEF,
111669149700,68719476811,REACHING_DEF,
111669149700,25769803793,REACHING_DEF,
111669149700,68719476779,REACHING_DEF,
111669149700,68719476800,REACHING_DEF,
111669149700,115964116995,REACHING_DEF,
111669149700,68719476815,REACHING_DEF,
111669149700,68719476804,REACHING_DEF,
111669149700,68719476808,REACHING_DEF,
111669149700,25769803792,REACHING_DEF,
111669149700,68719476803,REACHING_DEF,
111669149700,30064771134,REACHING_DEF,
111669149700,68719476791,REACHING_DEF,
111669149700,68719476813,REACHING_DEF,
111669149700,90194313260,REACHING_DEF,
111669149700,68719476777,REACHING_DEF,
111669149700,68719476795,REACHING_DEF,
111669149700,68719476818,REACHING_DEF,
111669149700,68719476786,REACHING_DEF,
111669149700,25769803796,REACHING_DEF,
111669149700,146028888070,REACHING_DEF,
111669149700,146028888069,REACHING_DEF,
111669149701,68719476821,REACHING_DEF,
111669149701,115964116996,REACHING_DEF,
111669149701,68719476822,REACHING_DEF,
111669149701,90194313264,REACHING_DEF,
111669149701,115964116997,REACHING_DEF,
111669149703,90194313278,REACHING_DEF,
111669149703,90194313274,REACHING_DEF,
111669149703,90194313270,REACHING_DEF,
111669149703,68719476837,REACHING_DEF,
111669149703,30064771221,REACHING_DEF,
111669149703,68719476848,REACHING_DEF,
111669149703,90194313283,REACHING_DEF,
111669149703,68719476833,REACHING_DEF,
111669149703,68719476838,REACHING_DEF,
111669149703,68719476842,REACHING_DEF,
111669149703,25769803804,REACHING_DEF,
111669149703,30064771209,REACHING_DEF,
111669149703,68719476846,REACHING_DEF,
111669149703,90194313282,REACHING_DEF,
111669149703,68719476834,REACHING_DEF,
111669149703,68719476835,REACHING_DEF,
111669149703,68719476841,REACHING_DEF,
111669149703,90194313276,REACHING_DEF,
111669149703,68719476845,REACHING_DEF,
111669149703,90194313281,REACHING_DEF,
111669149703,115964116999,REACHING_DEF,
111669149703,68719476840,REACHING_DEF,
111669149703,115964116998,REACHING_DEF,
111669149703,90194313272,REACHING_DEF,
111669149703,68719476847,REACHING_DEF,
111669149703,25769803805,REACHING_DEF,
111669149703,68719476844,REACHING_DEF,
111669149703,68719476839,REACHING_DEF,
111669149703,90194313280,REACHING_DEF,
111669149704,90194313284,REACHING_DEF,
111669149704,115964117000,REACHING_DEF,
111669149704,68719476849,REACHING_DEF,
111669149704,90194313285,REACHING_DEF,
111669149704,68719476850,REACHING_DEF,
111669149705,30064771253,REACHING_DEF,
111669149705,68719476869,REACHING_DEF,
111669149705,115964117002,REACHING_DEF,
111669149705,90194313301,REACHING_DEF,
111669149705,68719476863,REACHING_DEF,
111669149705,25769803813,REACHING_DEF,
111669149705,68719476852,REACHING_DEF,
111669149705,68719476880,REACHING_DEF,
111669149705,115964117001,REACHING_DEF,
111669149705,68719476875,REACHING_DEF,
111669149705,115964117003,REACHING_DEF,
111669149705,68719476862,REACHING_DEF,
111669149705,30064771264,REACHING_DEF,
111669149705,68719476865,REACHING_DEF,
111669149705,25769803811,REACHING_DEF,
111669149705,90194313287,REACHING_DEF,
111669149705,25769803814,REACHING_DEF,
111669149705,90194313307,REACHING_DEF,
111669149705,68719476870,REACHING_DEF,
111669149705,90194313295,REACHING_DEF,
111669149705,30064771270,REACHING_DEF,
111669149705,90194313299,REACHING_DEF,
111669149705,68719476881,REACHING_DEF,
111669149705,90194313289,REACHING_DEF,
111669149705,90194313303,REACHING_DEF,
111669149705,68719476882,REACHING_DEF,
111669149705,68719476867,REACHING_DEF,
111669149705,68719476856,REACHING_DEF,
111669149705,68719476854,REACHING_DEF,
111669149705,68719476873,REACHING_DEF,
111669149705,68719476877,REACHING_DEF,
111669149705,68719476855,REACHING_DEF,
111669149705,68719476860,REACHING_DEF,
111669149705,90194313298,REACHING_DEF,
111669149705,68719476879,REACHING_DEF,
111669149705,68719476872,REACHING_DEF,
111669149705,30064771239,REACHING_DEF,
111669149705,90194313305,REACHING_DEF,
111669149705,68719476851,REACHING_DEF,
111669149705,25769803816,REACHING_DEF,
111669149705,68719476868,REACHING_DEF,
111669149705,68719476871,REACHING_DEF,
111669149705,68719476864,REACHING_DEF,
111669149705,68719476859,REACHING_DEF,
111669149705,68719476858,REACHING_DEF,
111669149705,90194313293,REACHING_DEF,
111669149705,68719476876,REACHING_DEF,
111669149705,146028888071,REACHING_DEF,
111669149705,146028888072,REACHING_DEF,
111669149705,146028888073,REACHING_DEF,
111669149705,146028888074,REACHING_DEF,
111669149706,68719476885,REACHING_DEF,
111669149706,90194313309,REACHING_DEF,
111669149706,90194313312,REACHING_DEF,
111669149706,115964117004,REACHING_DEF,
111669149706,68719476883,REACHING_DEF,
111669149706,68719476884,REACHING_DEF,
111669149706,90194313311,REACHING_DEF,
111669149706,68719476886,REACHING_DEF,
111669149706,115964117005,REACHING_DEF,
111669149706,146028888076,REACHING_DEF,
111669149706,146028888077,REACHING_DEF,
111669149706,146028888075,REACHING_DEF,
111669149707,90194313314,REACHING_DEF,
111669149707,90194313317,REACHING_DEF,
111669149707,68719476908,REACHING_DEF,
111669149707,68719476901,REACHING_DEF,
111669149707,90194313318,REACHING_DEF,
111669149707,90194313316,REACHING_DEF,
111669149707,68719476895,REACHING_DEF,
111669149707,68719476907,REACHING_DEF,
111669149707,115964117008,REACHING_DEF,
111669149707,68719476894,REACHING_DEF,
111669149707,115964117006,REACHING_DEF,
111669149707,68719476889,REACHING_DEF,
111669149707,115964117007,REACHING_DEF,
111669149707,68719476898,REACHING_DEF,
111669149707,68719476893,REACHING_DEF,
111669149707,68719476910,REACHING_DEF,
111669149707,68719476909,REACHING_DEF,
111669149707,68719476888,REACHING_DEF,
111669149707,68719476905,REACHING_DEF,
111669149707,68719476899,REACHING_DEF,
111669149707,25769803821,REACHING_DEF,
111669149707,68719476904,REACHING_DEF,
111669149707,68719476911,REACHING_DEF,
111669149707,68719476903,REACHING_DEF,
111669149707,68719476896,REACHING_DEF,
111669149707,68719476890,REACHING_DEF,
111669149707,30064771290,REACHING_DEF,
111669149709,90194313355,REACHING_DEF,
111669149709,68719476922,REACHING_DEF,
111669149709,90194313347,REACHING_DEF,
111669149709,68719476927,REACHING_DEF,
111669149709,25769803830,REACHING_DEF,
111669149709,90194313328,REACHING_DEF,
111669149709,68719476917,REACHING_DEF,
111669149709,68719476912,REACHING_DEF,
111669149709,68719476939,REACHING_DEF,
111669149709,68719476926,REACHING_DEF,
111669149709,68719476929,REACHING_DEF,
111669149709,90194313343,REACHING_DEF,
111669149709,68719476925,REACHING_DEF,
111669149709,68719476938,REACHING_DEF,
111669149709,90194313334,REACHING_DEF,
111669149709,25769803831,REACHING_DEF,
111669149709,68719476934,REACHING_DEF,
111669149709,90194313351,REACHING_DEF,
111669149709,68719476930,REACHING_DEF,
111669149709,90194313323,REACHING_DEF,
111669149709,30064771339,REACHING_DEF,
111669149709,68719476942,REACHING_DEF,
111669149709,90194313321,REACHING_DEF,
111669149709,25769803832,REACHING_DEF,
111669149709,68719476941,REACHING_DEF,
111669149709,90194313353,REACHING_DEF,
111669149709,68719476931,REACHING_DEF,
111669149709,68719476920,REACHING_DEF,
111669149709,30064771317,REACHING_DEF,
111669149709,68719476918,REACHING_DEF,
111669149709,68719476937,REACHING_DEF,
111669149709,68719476924,REACHING_DEF,
111669149709,90194313345,REACHING_DEF,
111669149709,30064771313,REACHING_DEF,
111669149709,68719476914,REACHING_DEF,
111669149709,90194313339,REACHING_DEF,
111669149709,68719476919,REACHING_DEF,
111669149709,90194313341,REACHING_DEF,
111669149709,30064771351,REACHING_DEF,
111669149709,90194313330,REACHING_DEF,
111669149709,90194313349,REACHING_DEF,
111669149709,90194313337,REACHING_DEF,
111669149709,90194313332,REACHING_DEF,
111669149709,68719476932,REACHING_DEF,
111669149709,68719476943,REACHING_DEF,
111669149709,30064771327,REACHING_DEF,
111669149709,25769803829,REACHING_DEF,
111669149709,68719476935,REACHING_DEF,
111669149709,68719476933,REACHING_DEF,
111669149709,68719476940,REACHING_DEF,
111669149709,90194313325,REACHING_DEF,
111669149709,68719476923,REACHING_DEF,
111669149709,68719476916,REACHING_DEF,
111669149709,25769803828,REACHING_DEF,
111669149709,115964117009,REACHING_DEF,
111669149710,68719476948,REACHING_DEF,
111669149710,68719476945,REACHING_DEF,
111669149710,90194313358,REACHING_DEF,
111669149710,68719476946,REACHING_DEF,
111669149710,115964117010,REACHING_DEF,
111669149710,90194313357,REACHING_DEF,
111669149710,115964117011,REACHING_DEF,
111669149710,68719476944,REACHING_DEF,
111669149710,90194313356,REACHING_DEF,
111669149710,90194313359,REACHING_DEF,
111669149710,68719476947,REACHING_DEF,
111669149710,146028888081,REACHING_DEF,
111669149710,146028888080,REACHING_DEF,
111669149710,146028888082,REACHING_DEF,
111669149710,146028888079,REACHING_DEF,
111669149711,68719476970,REACHING_DEF,
111669149711,25769803842,REACHING_DEF,
111669149711,90194313361,REACHING_DEF,
111669149711,68719476954,REACHING_DEF,
111669149711,90194313360,REACHING_DEF,
111669149711,68719476961,REACHING_DEF,
111669149711,68719476976,REACHING_DEF,
111669149711,68719476971,REACHING_DEF,
111669149711,68719476966,REACHING_DEF,
111669149711,90194313366,REACHING_DEF,
111669149711,68719476958,REACHING_DEF,
111669149711,25769803840,REACHING_DEF,
111669149711,68719476953,REACHING_DEF,
111669149711,68719476962,REACHING_DEF,
111669149711,90194313363,REACHING_DEF,
111669149711,115964117012,REACHING_DEF,
111669149711,68719476974,REACHING_DEF,
111669149711,68719476964,REACHING_DEF,
111669149711,115964117013,REACHING_DEF,
111669149711,68719476950,REACHING_DEF,
111669149711,90194313367,REACHING_DEF,
111669149711,68719476969,REACHING_DEF,
111669149711,68719476951,REACHING_DEF,
111669149711,68719476956,REACHING_DEF,
111669149711,90194313362,REACHING_DEF,
111669149711,68719476960,REACHING_DEF,
111669149711,90194313364,REACHING_DEF,
111669149711,68719476972,REACHING_DEF,
111669149711,68719476979,REACHING_DEF,
111669149711,90194313365,REACHING_DEF,
111669149711,146028888085,REACHING_DEF,
111669149711,146028888084,REACHING_DEF,
111669149711,146028888083,REACHING_DEF,
111669149712,68719476981,REACHING_DEF,
111669149712,90194313371,REACHING_DEF,
111669149712,68719476982,REACHING_DEF,
111669149712,68719476990,REACHING_DEF,
111669149712,115964117015,REACHING_DEF,
111669149712,68719476991,REACHING_DEF,
111669149712,90194313370,REACHING_DEF,
111669149712,68719476986,REACHING_DEF,
111669149712,68719476988,REACHING_DEF,
111669149712,68719476984,REACHING_DEF,
111669149712,90194313369,REACHING_DEF,
111669149712,68719476987,REACHING_DEF,
111669149712,68719476992,REACHING_DEF,
111669149712,90194313368,REACHING_DEF,
111669149712,90194313372,REACHING_DEF,
111669149712,115964117014,REACHING_DEF,
111669149712,146028888088,REACHING_DEF,
111669149712,146028888087,REACHING_DEF,
111669149712,146028888086,REACHING_DEF,
111669149713,30064771421,REACHING_DEF,
111669149713,90194313378,REACHING_DEF,
111669149713,68719477004,REACHING_DEF,
111669149713,68719477000,REACHING_DEF,
111669149713,90194313380,REACHING_DEF,
111669149713,68719477003,REACHING_DEF,
111669149713,68719476999,REACHING_DEF,
111669149713,115964117016,REACHING_DEF,
111669149713,90194313374,REACHING_DEF,
111669149713,68719476994,REACHING_DEF,
111669149713,68719476998,REACHING_DEF,
111669149713,68719477002,REACHING_DEF,
111669149713,25769803854,REACHING_DEF,
111669149713,115964117017,REACHING_DEF,
111669149713,68719477001,REACHING_DEF,
111669149713,68719477005,REACHING_DEF,
111669149713,68719476995,REACHING_DEF,
111669149713,90194313376,REACHING_DEF,
111669149713,90194313382,REACHING_DEF,
111669149714,68719477008,REACHING_DEF,
111669149714,115964117018,REACHING_DEF,
111669149714,90194313383,REACHING_DEF,
111669149714,68719477007,REACHING_DEF,
111669149714,115964117019,REACHING_DEF,
111669149715,68719477013,REACHING_DEF,
111669149715,90194313391,REACHING_DEF,
111669149715,68719477016,REACHING_DEF,
111669149715,90194313394,REACHING_DEF,
111669149715,90194313393,REACHING_DEF,
111669149715,90194313387,REACHING_DEF,
111669149715,68719477018,REACHING_DEF,
111669149715,115964117021,REACHING_DEF,
111669149715,68719477017,REACHING_DEF,
111669149715,68719477015,REACHING_DEF,
111669149715,30064771441,REACHING_DEF,
111669149715,68719477010,REACHING_DEF,
111669149715,90194313385,REACHING_DEF,
111669149715,68719477020,REACHING_DEF,
111669149715,25769803857,REACHING_DEF,
111669149715,68719477014,REACHING_DEF,
111669149715,68719477019,REACHING_DEF,
111669149715,115964117020,REACHING_DEF,
111669149715,90194313389,REACHING_DEF,
111669149716,115964117022,REACHING_DEF,
111669149717,115964117023,REACHING_DEF,
111669149717,115964117024,REACHING_DEF,
111669149718,115964117026,REACHING_DEF,
111669149718,115964117025,REACHING_DEF,
111669149719,115964117027,REACHING_DEF,
111669149720,115964117028,REACHING_DEF,
111669149720,115964117029,REACHING_DEF,
111669149721,115964117030,REACHING_DEF,
111669149721,115964117031,REACHING_DEF,
111669149722,115964117033,REACHING_DEF,
111669149722,115964117032,REACHING_DEF,
111669149723,115964117034,REACHING_DEF,
111669149724,115964117035,REACHING_DEF,
111669149725,115964117036,REACHING_DEF,
111669149726,115964117037,REACHING_DEF,
111669149727,115964117038,REACHING_DEF,
111669149728,115964117039,REACHING_DEF,
111669149729,115964117040,REACHING_DEF,
111669149729,115964117041,REACHING_DEF,
111669149730,115964117043,REACHING_DEF,
111669149730,115964117044,REACHING_DEF,
111669149730,115964117042,REACHING_DEF,
111669149731,115964117045,REACHING_DEF,
111669149731,115964117046,REACHING_DEF,
111669149732,115964117047,REACHING_DEF,
111669149733,115964117048,REACHING_DEF,
111669149734,115964117050,REACHING_DEF,
111669149734,115964117049,REACHING_DEF,
111669149735,115964117051,REACHING_DEF,
111669149736,115964117052,REACHING_DEF,
111669149737,115964117054,REACHING_DEF,
111669149737,115964117053,REACHING_DEF,
111669149738,115964117056,REACHING_DEF,
111669149738,115964117055,REACHING_DEF,
111669149739,115964117057,REACHING_DEF,
111669149740,115964117058,REACHING_DEF,
111669149740,115964117059,REACHING_DEF,
111669149741,115964117060,REACHING_DEF,
111669149741,115964117061,REACHING_DEF,
111669149742,115964117062,REACHING_DEF,
111669149743,115964117063,REACHING_DEF,
111669149744,115964117064,REACHING_DEF,
111669149744,115964117065,REACHING_DEF,
111669149744,115964117066,REACHING_DEF,
111669149745,115964117067,REACHING_DEF,
111669149745,115964117068,REACHING_DEF,
111669149746,115964117070,REACHING_DEF,
111669149746,115964117071,REACHING_DEF,
111669149746,115964117069,REACHING_DEF,
111669149747,115964117073,REACHING_DEF,
111669149747,115964117072,REACHING_DEF,
111669149748,115964117075,REACHING_DEF,
111669149748,115964117076,REACHING_DEF,
111669149748,115964117074,REACHING_DEF,
111669149749,115964117077,REACHING_DEF,
111669149750,115964117078,REACHING_DEF,
111669149750,115964117079,REACHING_DEF,
111669149751,115964117080,REACHING_DEF,
111669149751,115964117081,REACHING_DEF,
111669149752,115964117084,REACHING_DEF,
111669149752,115964117083,REACHING_DEF,
111669149752,115964117082,REACHING_DEF,
111669149753,115964117085,REACHING_DEF,
111669149754,115964117087,REACHING_DEF,
111669149754,115964117086,REACHING_DEF,
111669149755,115964117088,REACHING_DEF,
111669149756,115964117089,REACHING_DEF,
111669149757,115964117090,REACHING_DEF,
111669149758,115964117092,REACHING_DEF,
111669149758,115964117091,REACHING_DEF,
111669149759,115964117093,REACHING_DEF,
111669149760,115964117094,REACHING_DEF,
111669149760,115964117095,REACHING_DEF,
111669149761,115964117097,REACHING_DEF,
111669149761,115964117096,REACHING_DEF,
111669149762,115964117099,REACHING_DEF,
111669149762,115964117098,REACHING_DEF,
111669149763,115964117101,REACHING_DEF,
111669149763,115964117100,REACHING_DEF,
111669149764,115964117102,REACHING_DEF,
111669149765,115964117103,REACHING_DEF,
111669149766,115964117104,REACHING_DEF,
111669149767,115964117105,REACHING_DEF,
111669149768,115964117107,REACHING_DEF,
111669149768,115964117106,REACHING_DEF,
111669149769,115964117109,REACHING_DEF,
111669149769,115964117108,REACHING_DEF,
111669149770,115964117110,REACHING_DEF,
111669149770,115964117111,REACHING_DEF,
111669149771,115964117112,REACHING_DEF,
111669149771,115964117113,REACHING_DEF,
111669149772,115964117114,REACHING_DEF,
115964116992,120259084288,REACHING_DEF,this
115964116992,120259084288,REACHING_DEF,this
115964116992,68719476740,REACHING_DEF,this
115964116992,68719476736,REACHING_DEF,this
115964116992,128849018880,REACHING_DEF,this
115964116993,68719476750,REACHING_DEF,this
115964116993,68719476748,REACHING_DEF,this
115964116993,120259084289,REACHING_DEF,this
115964116994,68719476772,REACHING_DEF,this
115964116994,120259084290,REACHING_DEF,this
115964116994,120259084290,REACHING_DEF,this
115964116994,68719476767,REACHING_DEF,this
115964116994,30064771128,REACHING_DEF,this
115964116994,128849018882,REACHING_DEF,this
115964116995,120259084291,REACHING_DEF,this
115964116995,120259084291,REACHING_DEF,this
115964116995,68719476811,REACHING_DEF,this
115964116995,30064771178,REACHING_DEF,this
115964116995,128849018883,REACHING_DEF,this
115964116996,120259084292,REACHING_DEF,this
115964116996,120259084292,REACHING_DEF,this
115964116996,128849018884,REACHING_DEF,this
115964116997,68719476821,REACHING_DEF,data
115964116997,120259084293,REACHING_DEF,data
115964116998,68719476848,REACHING_DEF,this
115964116998,68719476847,REACHING_DEF,this
115964116998,68719476842,REACHING_DEF,this
115964116998,68719476835,REACHING_DEF,this
115964116998,120259084294,REACHING_DEF,this
115964116998,68719476833,REACHING_DEF,this
115964116999,120259084295,REACHING_DEF,uploadDir
115964116999,68719476834,REACHING_DEF,uploadDir
115964117000,30064771235,REACHING_DEF,this
115964117000,120259084296,REACHING_DEF,this
115964117000,120259084296,REACHING_DEF,this
115964117000,68719476850,REACHING_DEF,this
115964117000,68719476849,REACHING_DEF,this
115964117000,30064771233,REACHING_DEF,this
115964117000,128849018887,REACHING_DEF,this
115964117001,120259084297,REACHING_DEF,this
115964117001,68719476851,REACHING_DEF,this
115964117002,68719476852,REACHING_DEF,fileData
115964117002,120259084298,REACHING_DEF,fileData
115964117003,120259084299,REACHING_DEF,customName
115964117003,120259084299,REACHING_DEF,customName
115964117003,68719476860,REACHING_DEF,customName
115964117003,128849018888,REACHING_DEF,customName
115964117004,120259084300,REACHING_DEF,this
115964117004,120259084300,REACHING_DEF,this
115964117004,30064771282,REACHING_DEF,this
115964117004,68719476886,REACHING_DEF,this
115964117004,128849018889,REACHING_DEF,this
115964117005,120259084301,REACHING_DEF,fileData
115964117005,120259084301,REACHING_DEF,fileData
115964117005,30064771281,REACHING_DEF,fileData
115964117005,30064771278,REACHING_DEF,fileData
115964117005,68719476885,REACHING_DEF,fileData
115964117005,68719476883,REACHING_DEF,fileData
115964117005,128849018889,REACHING_DEF,fileData
115964117006,68719476901,REACHING_DEF,this
115964117006,120259084302,REACHING_DEF,this
115964117006,120259084302,REACHING_DEF,this
115964117006,30064771300,REACHING_DEF,this
115964117006,128849018890,REACHING_DEF,this
115964117007,68719476890,REACHING_DEF,originalName
115964117007,120259084303,REACHING_DEF,originalName
115964117008,68719476888,REACHING_DEF,customName
115964117008,120259084304,REACHING_DEF,customName
115964117009,120259084305,REACHING_DEF,this
115964117009,120259084305,REACHING_DEF,this
115964117009,68719476912,REACHING_DEF,this
115964117009,68719476935,REACHING_DEF,this
115964117009,128849018892,REACHING_DEF,this
115964117010,68719476945,REACHING_DEF,this
115964117010,120259084306,REACHING_DEF,this
115964117010,120259084306,REACHING_DEF,this
115964117010,128849018893,REACHING_DEF,this
115964117011,120259084307,REACHING_DEF,filePath
115964117011,68719476944,REACHING_DEF,filePath
115964117012,120259084308,REACHING_DEF,this
115964117012,120259084308,REACHING_DEF,this
115964117012,30064771377,REACHING_DEF,this
115964117012,68719476956,REACHING_DEF,this
115964117012,128849018894,REACHING_DEF,this
115964117013,68719476950,REACHING_DEF,filePath
115964117013,120259084309,REACHING_DEF,filePath
115964117014,120259084310,REACHING_DEF,this
115964117014,120259084310,REACHING_DEF,this
115964117014,30064771407,REACHING_DEF,this
115964117014,68719476984,REACHING_DEF,this
115964117014,128849018895,REACHING_DEF,this
115964117015,68719476981,REACHING_DEF,filePath
115964117015,120259084311,REACHING_DEF,filePath
115964117016,120259084312,REACHING_DEF,this
115964117016,68719476994,REACHING_DEF,this
115964117017,120259084313,REACHING_DEF,fileName
115964117017,68719476995,REACHING_DEF,fileName
115964117018,120259084314,REACHING_DEF,this
115964117018,120259084314,REACHING_DEF,this
115964117018,128849018897,REACHING_DEF,this
115964117019,68719477007,REACHING_DEF,fileName
115964117019,120259084315,REACHING_DEF,fileName
115964117020,120259084316,REACHING_DEF,this
115964117020,120259084316,REACHING_DEF,this
115964117020,128849018898,REACHING_DEF,this
115964117021,120259084317,REACHING_DEF,originalName
115964117021,68719477010,REACHING_DEF,originalName
115964117022,120259084318,REACHING_DEF,p1
115964117022,128849018900,REACHING_DEF,p1
115964117023,120259084319,REACHING_DEF,p1
115964117023,128849018901,REACHING_DEF,p1
115964117024,120259084320,REACHING_DEF,p2
115964117024,128849018901,REACHING_DEF,p2
115964117025,120259084321,REACHING_DEF,p1
115964117025,128849018902,REACHING_DEF,p1
115964117026,120259084322,REACHING_DEF,p2
115964117026,128849018902,REACHING_DEF,p2
115964117027,120259084323,REACHING_DEF,p0
115964117027,128849018903,REACHING_DEF,p0
115964117028,120259084324,REACHING_DEF,p1
115964117028,128849018904,REACHING_DEF,p1
115964117029,120259084325,REACHING_DEF,p2
115964117029,128849018904,REACHING_DEF,p2
115964117030,120259084326,REACHING_DEF,p1
115964117030,128849018905,REACHING_DEF,p1
115964117031,120259084327,REACHING_DEF,p2
115964117031,128849018905,REACHING_DEF,p2
115964117032,120259084328,REACHING_DEF,p1
115964117032,128849018906,REACHING_DEF,p1
115964117033,120259084329,REACHING_DEF,p2
115964117033,128849018906,REACHING_DEF,p2
115964117034,120259084330,REACHING_DEF,p0
115964117034,128849018907,REACHING_DEF,p0
115964117035,120259084331,REACHING_DEF,p0
115964117035,128849018908,REACHING_DEF,p0
115964117036,120259084332,REACHING_DEF,p0
115964117036,128849018909,REACHING_DEF,p0
115964117037,120259084333,REACHING_DEF,p1
115964117037,128849018910,REACHING_DEF,p1
115964117038,120259084334,REACHING_DEF,p1
115964117038,128849018911,REACHING_DEF,p1
115964117039,120259084335,REACHING_DEF,p1
115964117039,128849018912,REACHING_DEF,p1
115964117040,120259084336,REACHING_DEF,p1
115964117040,128849018913,REACHING_DEF,p1
115964117041,120259084337,REACHING_DEF,p2
115964117041,128849018913,REACHING_DEF,p2
115964117042,120259084338,REACHING_DEF,p0
115964117042,128849018914,REACHING_DEF,p0
115964117043,120259084339,REACHING_DEF,p1
115964117043,128849018914,REACHING_DEF,p1
115964117044,120259084340,REACHING_DEF,p2
115964117044,128849018914,REACHING_DEF,p2
115964117045,120259084341,REACHING_DEF,p1
115964117045,128849018915,REACHING_DEF,p1
115964117046,120259084342,REACHING_DEF,p2
115964117046,128849018915,REACHING_DEF,p2
115964117047,120259084343,REACHING_DEF,p1
115964117047,128849018916,REACHING_DEF,p1
115964117048,120259084344,REACHING_DEF,p1
115964117048,128849018917,REACHING_DEF,p1
115964117049,120259084345,REACHING_DEF,p1
115964117049,128849018918,REACHING_DEF,p1
115964117050,120259084346,REACHING_DEF,p2
115964117050,128849018918,REACHING_DEF,p2
115964117051,120259084347,REACHING_DEF,p1
115964117051,128849018919,REACHING_DEF,p1
115964117052,120259084348,REACHING_DEF,p1
115964117052,128849018920,REACHING_DEF,p1
115964117053,120259084349,REACHING_DEF,p1
115964117053,128849018921,REACHING_DEF,p1
115964117054,120259084350,REACHING_DEF,p2
115964117054,128849018921,REACHING_DEF,p2
115964117055,120259084351,REACHING_DEF,p1
115964117055,128849018922,REACHING_DEF,p1
115964117056,120259084352,REACHING_DEF,p2
115964117056,128849018922,REACHING_DEF,p2
115964117057,120259084353,REACHING_DEF,p0
115964117057,128849018923,REACHING_DEF,p0
115964117058,120259084354,REACHING_DEF,p0
115964117058,128849018924,REACHING_DEF,p0
115964117059,120259084355,REACHING_DEF,p1
115964117059,128849018924,REACHING_DEF,p1
115964117060,120259084356,REACHING_DEF,p1
115964117060,128849018925,REACHING_DEF,p1
115964117061,120259084357,REACHING_DEF,p2
115964117061,128849018925,REACHING_DEF,p2
115964117062,120259084358,REACHING_DEF,p0
115964117062,128849018926,REACHING_DEF,p0
115964117063,120259084359,REACHING_DEF,p1
115964117063,128849018927,REACHING_DEF,p1
115964117064,120259084360,REACHING_DEF,p1
115964117064,128849018928,REACHING_DEF,p1
115964117065,120259084361,REACHING_DEF,p2
115964117065,128849018928,REACHING_DEF,p2
115964117066,120259084362,REACHING_DEF,p3
115964117066,128849018928,REACHING_DEF,p3
115964117067,120259084363,REACHING_DEF,p0
115964117067,128849018929,REACHING_DEF,p0
115964117068,120259084364,REACHING_DEF,p1
115964117068,128849018929,REACHING_DEF,p1
115964117069,120259084365,REACHING_DEF,p0
115964117069,128849018930,REACHING_DEF,p0
115964117070,120259084366,REACHING_DEF,p1
115964117070,128849018930,REACHING_DEF,p1
115964117071,120259084367,REACHING_DEF,p2
115964117071,128849018930,REACHING_DEF,p2
115964117072,120259084368,REACHING_DEF,p1
115964117072,128849018931,REACHING_DEF,p1
115964117073,120259084369,REACHING_DEF,p2
115964117073,128849018931,REACHING_DEF,p2
115964117074,120259084370,REACHING_DEF,p1
115964117074,128849018932,REACHING_DEF,p1
115964117075,120259084371,REACHING_DEF,p2
115964117075,128849018932,REACHING_DEF,p2
115964117076,120259084372,REACHING_DEF,p3
115964117076,128849018932,REACHING_DEF,p3
115964117077,120259084373,REACHING_DEF,p0
115964117077,128849018933,REACHING_DEF,p0
115964117078,120259084374,REACHING_DEF,p1
115964117078,128849018934,REACHING_DEF,p1
115964117079,120259084375,REACHING_DEF,p2
115964117079,128849018934,REACHING_DEF,p2
115964117080,120259084376,REACHING_DEF,p1
115964117080,128849018935,REACHING_DEF,p1
115964117081,120259084377,REACHING_DEF,p2
115964117081,128849018935,REACHING_DEF,p2
115964117082,120259084378,REACHING_DEF,p1
115964117082,128849018936,REACHING_DEF,p1
115964117083,120259084379,REACHING_DEF,p2
115964117083,128849018936,REACHING_DEF,p2
115964117084,120259084380,REACHING_DEF,p3
115964117084,128849018936,REACHING_DEF,p3
115964117085,120259084381,REACHING_DEF,p1
115964117085,128849018937,REACHING_DEF,p1
115964117086,120259084382,REACHING_DEF,p1
115964117086,128849018938,REACHING_DEF,p1
115964117087,120259084383,REACHING_DEF,p2
115964117087,128849018938,REACHING_DEF,p2
115964117088,120259084384,REACHING_DEF,p0
115964117088,128849018939,REACHING_DEF,p0
115964117089,120259084385,REACHING_DEF,p1
115964117089,128849018940,REACHING_DEF,p1
115964117090,120259084386,REACHING_DEF,p0
115964117090,128849018941,REACHING_DEF,p0
115964117091,120259084387,REACHING_DEF,p1
115964117091,128849018942,REACHING_DEF,p1
115964117092,120259084388,REACHING_DEF,p2
115964117092,128849018942,REACHING_DEF,p2
115964117093,120259084389,REACHING_DEF,p1
115964117093,128849018943,REACHING_DEF,p1
115964117094,120259084390,REACHING_DEF,p0
115964117094,128849018944,REACHING_DEF,p0
115964117095,120259084391,REACHING_DEF,p1
115964117095,128849018944,REACHING_DEF,p1
115964117096,120259084392,REACHING_DEF,p0
115964117096,128849018945,REACHING_DEF,p0
115964117097,120259084393,REACHING_DEF,p1
115964117097,128849018945,REACHING_DEF,p1
115964117098,120259084394,REACHING_DEF,p1
115964117098,128849018946,REACHING_DEF,p1
115964117099,120259084395,REACHING_DEF,p2
115964117099,128849018946,REACHING_DEF,p2
115964117100,120259084396,REACHING_DEF,p1
115964117100,128849018947,REACHING_DEF,p1
115964117101,120259084397,REACHING_DEF,p2
115964117101,128849018947,REACHING_DEF,p2
115964117102,120259084398,REACHING_DEF,p1
115964117102,128849018948,REACHING_DEF,p1
115964117103,120259084399,REACHING_DEF,p0
115964117103,128849018949,REACHING_DEF,p0
115964117104,120259084400,REACHING_DEF,p1
115964117104,128849018950,REACHING_DEF,p1
115964117105,120259084401,REACHING_DEF,p1
115964117105,128849018951,REACHING_DEF,p1
115964117106,120259084402,REACHING_DEF,p1
115964117106,128849018952,REACHING_DEF,p1
115964117107,120259084403,REACHING_DEF,p2
115964117107,128849018952,REACHING_DEF,p2
115964117108,120259084404,REACHING_DEF,p0
115964117108,128849018953,REACHING_DEF,p0
115964117109,120259084405,REACHING_DEF,p1
115964117109,128849018953,REACHING_DEF,p1
115964117110,120259084406,REACHING_DEF,p1
115964117110,128849018954,REACHING_DEF,p1
115964117111,120259084407,REACHING_DEF,p2
115964117111,128849018954,REACHING_DEF,p2
115964117112,120259084408,REACHING_DEF,p1
115964117112,128849018955,REACHING_DEF,p1
115964117113,120259084409,REACHING_DEF,p2
115964117113,128849018955,REACHING_DEF,p2
115964117114,120259084410,REACHING_DEF,p1
115964117114,128849018956,REACHING_DEF,p1
146028888064,128849018881,REACHING_DEF,<RET>
146028888065,128849018881,REACHING_DEF,<RET>
146028888066,128849018881,REACHING_DEF,<RET>
146028888067,128849018882,REACHING_DEF,<RET>
146028888068,128849018882,REACHING_DEF,<RET>
146028888069,128849018883,REACHING_DEF,<RET>
146028888070,128849018883,REACHING_DEF,<RET>
146028888071,128849018888,REACHING_DEF,<RET>
146028888072,128849018888,REACHING_DEF,<RET>
146028888073,128849018888,REACHING_DEF,<RET>
146028888074,128849018888,REACHING_DEF,<RET>
146028888075,128849018889,REACHING_DEF,<RET>
146028888076,128849018889,REACHING_DEF,<RET>
146028888077,128849018889,REACHING_DEF,<RET>
146028888078,128849018890,REACHING_DEF,<RET>
146028888079,128849018893,REACHING_DEF,<RET>
146028888080,128849018893,REACHING_DEF,<RET>
146028888081,128849018893,REACHING_DEF,<RET>
146028888082,128849018893,REACHING_DEF,<RET>
146028888083,128849018894,REACHING_DEF,<RET>
146028888084,128849018894,REACHING_DEF,<RET>
146028888085,128849018894,REACHING_DEF,<RET>
146028888086,128849018895,REACHING_DEF,<RET>
146028888087,128849018895,REACHING_DEF,<RET>
146028888088,128849018895,REACHING_DEF,<RET>
146028888089,128849018896,REACHING_DEF,<RET>
146028888090,128849018897,REACHING_DEF,<RET>
146028888091,128849018898,REACHING_DEF,<RET>
