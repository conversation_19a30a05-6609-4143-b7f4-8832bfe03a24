LOAD CSV FROM 'file:/nodes_BLOCK_data.csv' AS line
CREATE (:BLOCK {
id: toInteger(line[0]),
ARGUMENT_INDEX: toInteger(line[2]),
ARGUMENT_NAME: line[3],
CODE: line[4],
COLUMN_NUMBER: line[5],
DYNAMIC_TYPE_HINT_FULL_NAME: split(line[6], ";"),
LINE_NUMBER: toInteger(line[7]),
OFFSET: line[8],
OFFSET_END: line[9],
ORDER: toInteger(line[10]),
POSSIBLE_TYPES: split(line[11], ";"),
TYPE_FULL_NAME: line[12]
});
