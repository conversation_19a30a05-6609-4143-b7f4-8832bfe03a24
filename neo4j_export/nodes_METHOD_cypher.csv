LOAD CSV FROM 'file:/nodes_METHOD_data.csv' AS line
CREATE (:METHOD {
id: toInteger(line[0]),
AST_PARENT_FULL_NAME: line[2],
AST_PARENT_TYPE: line[3],
CODE: line[4],
COLUMN_NUMBER: line[5],
COLUMN_NUMBER_END: line[6],
FILENAME: line[7],
FULL_NAME: line[8],
GENERIC_SIGNATURE: line[9],
HASH: line[10],
IS_EXTERNAL: toBoolean(line[11]),
LINE_NUMBER: toInteger(line[12]),
LINE_NUMBER_END: line[13],
NAME: line[14],
OFFSET: line[15],
OFFSET_END: line[16],
ORDER: toInteger(line[17]),
SIGNATURE: line[18]
});
