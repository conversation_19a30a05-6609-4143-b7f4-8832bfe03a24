WITH "_GET" AS pSrc, "call_user_func" AS pSnk
CALL apoc.cypher.runTimeboxed("
WITH $srcName AS srcName, $snkName AS snkName

// 1) sink 与所有参数候选（排除 RETURN / METHOD_RETURN / LITERAL）
MATCH (snk:CALL)
WHERE snk.NAME = snkName OR snk.METHOD_FULL_NAME = snkName
MATCH (snk)-[:ARGUMENT]-(cand)
WITH snk, srcName, collect(DISTINCT cand) AS cands
WITH snk, srcName, [a IN cands WHERE NOT 'LITERAL' IN labels(a)
                                  AND NOT 'RETURN' IN labels(a)
                                  AND NOT 'METHOD_RETURN' IN labels(a)] AS sinkArgs

// 2) 源集合（保持不变）
MATCH (base)
WHERE (base:IDENTIFIER AND base.NAME = srcName)
   OR (base:LOCAL AND base.NAME = srcName)
   OR (base:CALL AND base.METHOD_FULL_NAME = '<operator>.indexAccess'
       AND exists( (base)-[:ARGUMENT]->(:IDENTIFIER {NAME: srcName}) ))
WITH snk, collect(base) AS sources, sinkArgs

// 3) per-arg：对每个 sinkArg 单独搜“最长路径”（DFS）
UNWIND sources AS s
UNWIND sinkArgs AS arg
CALL apoc.path.expandConfig(s, {
  endNodes: [arg],
  relationshipFilter: 'REF>|REF<|ARGUMENT>|ARGUMENT<|PARAMETER_LINK>|PARAMETER_LINK<|REACHING_DEF>|REACHING_DEF<|RECEIVER>|RECEIVER<|CALL>|CALL<|CONTAINS>|CONTAINS<',
  minLevel: 1,
  maxLevel: 800,
  bfs: false,                       // DFS
  uniqueness: 'NODE_GLOBAL',
  labelFilter: '+IDENTIFIER|+LOCAL|+CALL|+METHOD|+METHOD_PARAMETER_IN|+METHOD_PARAMETER_OUT|+METHOD_RETURN|+LITERAL'
}) YIELD path

// 4) 过滤：必须终到该 arg，且路径中至少含一次 REACHING_DEF（数据流）
WITH snk, arg, path
WHERE last(nodes(path)) = arg
  AND any(r IN relationships(path) WHERE type(r)='REACHING_DEF')

// 5) 每个 arg 选最长路径（注意：排序需移出列表推导）
WITH snk, arg, path
ORDER BY length(path) DESC
WITH snk, arg, head(collect(path)) AS bestPerArg

// 6) 在所有 arg 的“各自最长”里挑全局最长（避免在列表推导里 ORDER BY）
WITH snk, bestPerArg
ORDER BY length(bestPerArg) DESC
WITH snk, head(collect(bestPerArg)) AS bestPath

// 7) 输出 JSON（保持格式不变），并追加 sink CALL 节点
WITH [n IN nodes(bestPath) | { id:id(n), labels:labels(n), properties:properties(n) }] AS pathNodes, snk
RETURN pathNodes + [{ id:id(snk), labels:labels(snk), properties:properties(snk) }] AS nodes
", {srcName: pSrc, snkName: pSnk}, 12000) YIELD value
RETURN value.nodes AS nodes;