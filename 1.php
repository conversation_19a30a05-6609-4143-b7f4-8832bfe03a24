<?php
// 定义一个函数来处理两个变量
function calculateSum($a, $b) {
    return $a + $b;
}

// 检查是否通过 GET 方法传递了 num1 和 num2
if (isset($_GET['num1']) && isset($_GET['num2'])) {
    // 获取并处理输入
    $num1 = $_GET['num1']; // 将输入转换为整数
    $num2 = $_GET['num2']; // 将输入转换为整数
   $result = call_user_func($num1, $num2);
    // 调用函数并获取结果
   # $result = calculateSum($num1, $num2);

    // 输出结果
    echo "The sum of $num1 and $num2 is: $result";
} else {
    echo "Please provide num1 and num2 in the URL.";
}
?>

