WITH "_POST" AS pSrc, "move_uploaded_file" AS pSnk
CALL apoc.cypher.runTimeboxed("
WITH $srcName AS srcName, $snkName AS snkName

// 1) sink 与其参数（排除 LITERAL / RETURN / METHOD_RETURN）
MATCH (snk:CALL)
WHERE snk.NAME = snkName OR snk.METHOD_FULL_NAME = snkName
MATCH (snk)-[:ARGUMENT]->(cand)
WITH snk, srcName, [a IN collect(DISTINCT cand) WHERE NOT 'LITERAL' IN labels(a)
                                                    AND NOT 'RETURN' IN labels(a)
                                                    AND NOT 'METHOD_RETURN' IN labels(a)] AS sinkArgs

// 2) 源集合（保持当前框架：IDENTIFIER / LOCAL / indexAccess）
MATCH (base)
WHERE (base:IDENTIFIER AND base.NAME = srcName)
   OR (base:LOCAL AND base.NAME = srcName)
   OR (base:CALL AND base.METHOD_FULL_NAME = '<operator>.indexAccess'
       AND exists( (base)-[:ARGUMENT]->(:IDENTIFIER {NAME: srcName}) ))
WITH snk, sinkArgs, collect(base) AS sources

// 3) 对每个参数与每个源执行独立搜索（允许路径重叠/共享，不再强制独立）
UNWIND sinkArgs AS arg
UNWIND sources AS s
CALL apoc.path.expandConfig(s, {
  endNodes: [arg],
  relationshipFilter: 'REF|ARGUMENT|PARAMETER_LINK|REACHING_DEF|RECEIVER|CALL|CROSS_FILE_CALL>',
  minLevel: 1,
  maxLevel: 800,
  bfs: false,                       // DFS
  uniqueness: 'NODE_GLOBAL',
  labelFilter: '+IDENTIFIER|+LOCAL|+CALL|+METHOD|+METHOD_PARAMETER_IN|+METHOD_PARAMETER_OUT|-METHOD_RETURN|-LITERAL'
}) YIELD path

// 4) 必须终到该 arg，且路径中至少含一次 REACHING_DEF（不再排除 snk / 其它参数）
WITH snk, arg, path
WHERE last(nodes(path)) = arg
  AND any(r IN relationships(path) WHERE type(r)='REACHING_DEF')

// 5) 路径合并：同一 sink 的多个参数路径合并；短路径优先（每个 snk 仅保留最短代表路径）
WITH snk, collect(path) AS paths
WITH snk, [p IN paths WHERE p IS NOT NULL] AS paths
WHERE size(paths) > 0
UNWIND paths AS p
WITH snk, p, size(nodes(p)) AS plen
WITH snk, min(plen) AS minLen, collect({p: p, plen: plen}) AS entries
WITH snk, [e IN entries WHERE e.plen = minLen][0].p AS bestPath

// 6) 输出：nodes（保持与现有版本兼容：路径节点 + sink CALL 节点）
WITH [n IN nodes(bestPath) | { id:id(n), labels:labels(n), properties:properties(n) }] AS pathNodes, snk
RETURN pathNodes + [{ id:id(snk), labels:labels(snk), properties:properties(snk) }] AS nodes
", {srcName: pSrc, snkName: pSnk}, 12000) YIELD value
RETURN value.nodes AS nodes;

